package com.timebasedscan.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Class to represent a scan entry in the history
 */
public class HistoryEntry {
    private final String url;
    private final String method;
    private final long timestamp;
    private final long duration;
    private final byte[] request;
    private final List<TestResult> testResults;
    
    /**
     * Create a new history entry
     */
    public HistoryEntry(String url, String method, byte[] request, List<TestResult> results, long duration) {
        this.url = url;
        this.method = method;
        this.timestamp = System.currentTimeMillis();
        this.request = request;
        this.testResults = new ArrayList<>(results);
        this.duration = duration;
    }
    
    /**
     * Get the URL
     */
    public String getUrl() {
        return url;
    }
    
    /**
     * Get the HTTP method
     */
    public String getMethod() {
        return method;
    }
    
    /**
     * Get the timestamp when this scan was performed
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Get the scan duration in milliseconds
     */
    public long getDuration() {
        return duration;
    }
    
    /**
     * Get the HTTP request
     */
    public byte[] getRequest() {
        return request;
    }
    
    /**
     * Get the test results
     */
    public List<TestResult> getTestResults() {
        return new ArrayList<>(testResults);
    }
    
    /**
     * Get the total number of parameters tested
     */
    public int getParamCount() {
        return testResults.size();
    }
    
    /**
     * Get the number of vulnerable parameters found
     */
    public int getVulnerableCount() {
        int count = 0;
        for (TestResult result : testResults) {
            if (result.isVulnerable()) {
                count++;
            }
        }
        return count;
    }

    /**
     * Get the number of parameters with SQL errors detected
     */
    public int getSqlErrorCount() {
        int count = 0;
        for (TestResult result : testResults) {
            if (result.isSqlErrorDetected()) {
                count++;
            }
        }
        return count;
    }

    /**
     * Check if any SQL errors were detected in this scan
     */
    public boolean hasSqlErrors() {
        return getSqlErrorCount() > 0;
    }
}