package com.timebasedscan;

import burp.IBurpExtender;
import burp.IBurpExtenderCallbacks;
import burp.IContextMenuFactory;
import burp.IContextMenuInvocation;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.ITab;
import com.timebasedscan.ui.MainPanel;
import com.timebasedscan.utils.IconUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Main extension class that implements multiple Burp interfaces
 * to provide a comprehensive time-based scanning capability.
 */
public class TimeBasedScannerExtension implements IBurpExtender, ITab, IContextMenuFactory {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private MainPanel mainPanel;
    private String extensionName = "Time-Based Scanner";
    private static final String VERSION = "1.0.0";
    private static final String AUTHOR = "<PERSON><PERSON><PERSON> (<EMAIL>)";

    /**
     * This method is called when the extension is loaded
     */
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        // Save the callbacks object
        this.callbacks = callbacks;
        
        // Obtain an extension helpers object
        this.helpers = callbacks.getHelpers();
        
        // Set the extension name
        callbacks.setExtensionName(extensionName);
        
        // Create the UI on the Event Dispatch Thread
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                // Create our UI
                mainPanel = new MainPanel(callbacks, helpers);
                
                // Register the tab with Burp
                callbacks.customizeUiComponent(mainPanel);
                callbacks.addSuiteTab(TimeBasedScannerExtension.this);
                
                // Register context menu
                callbacks.registerContextMenuFactory(TimeBasedScannerExtension.this);
                
                // Print extension information
                callbacks.printOutput("===================================================");
                callbacks.printOutput(" Time-Based Scanner Extension v" + VERSION);
                callbacks.printOutput(" Author: " + AUTHOR);
                callbacks.printOutput(" Loaded successfully!");
                callbacks.printOutput("===================================================");
                callbacks.printOutput(" Features:");
                callbacks.printOutput(" - Advanced parameter detection for REST, GraphQL, etc.");
                callbacks.printOutput(" - Parallel scanning with customizable payloads");
                callbacks.printOutput(" - Adaptive baseline measurements");
                callbacks.printOutput(" - Sophisticated time delay detection algorithm");
                callbacks.printOutput(" - Detailed vulnerability reporting and evidence tracking");
                callbacks.printOutput("===================================================");
            }
        });
    }
    
    /**
     * Return the extension tab name
     */
    @Override
    public String getTabCaption() {
        return extensionName;
    }
    
    /**
     * Return the extension UI component
     */
    @Override
    public Component getUiComponent() {
        return mainPanel;
    }
    
    /**
     * Create enhanced context menu items with icons and submenu options
     */
    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        // Create a menu item if the context is right
        List<JMenuItem> menuItems = new ArrayList<>();
        
        // Get selected messages
        IHttpRequestResponse[] selectedMessages = invocation.getSelectedMessages();
        
        // Only show menu if we have selected messages
        if (selectedMessages != null && selectedMessages.length > 0) {
            // Create a submenu for the scanner with multiple options
            JMenu scannerMenu = new JMenu("Time-Based Scanner");
            
            // Load the menu icon using our utility class
            try {
                ImageIcon icon = IconUtils.getMenuIcon("/icons/time_scanner_icon.svg");
                if (icon != null) {
                    scannerMenu.setIcon(icon);
                }
            } catch (Exception ex) {
                // Icon couldn't be loaded, just continue without it
                callbacks.printError("Could not load menu icon: " + ex.getMessage());
            }
            
            // Add "Send to Scanner" menu item - Default action
            JMenuItem sendToScannerMenuItem = new JMenuItem("Send to Scanner");
            sendToScannerMenuItem.setToolTipText("Send the selected request(s) to the Time-Based Scanner tab - By " + AUTHOR);
            
            // Try to add icon to the send menu item
            try {
                // Try to use a system icon first
                Icon fileIcon = UIManager.getIcon("FileView.fileIcon");
                if (fileIcon != null) {
                    sendToScannerMenuItem.setIcon(fileIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load send menu item icon: " + ex.getMessage());
            }
            
            // Add action to the menu item
            sendToScannerMenuItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    // For each selected message
                    for (IHttpRequestResponse message : selectedMessages) {
                        // Send to our scanner tab
                        mainPanel.loadRequest(message);
                    }
                    
                    // Switch to our tab
                    callbacks.customizeUiComponent(mainPanel);
                    int tabIndex = getTabIndex();
                    if (tabIndex != -1) {
                        // Focus our tab
                        JTabbedPane burpTabs = getBurpSuiteJTabbedPane();
                        if (burpTabs != null) {
                            burpTabs.setSelectedIndex(tabIndex);
                        }
                    }
                }
            });
            
            // Add "Scan All Parameters" menu item for quick scanning
            JMenuItem scanAllMenuItem = new JMenuItem("Scan All Parameters");
            scanAllMenuItem.setToolTipText("Send to scanner and immediately start scanning all parameters - By " + AUTHOR);
            
            // Try to add icon to the scan all menu item
            try {
                // Try to use a system icon first
                Icon scanAllIcon = UIManager.getIcon("Table.ascendingSortIcon");
                if (scanAllIcon == null) {
                    scanAllIcon = UIManager.getIcon("Tree.expandedIcon");
                }
                if (scanAllIcon != null) {
                    scanAllMenuItem.setIcon(scanAllIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load scan all menu item icon: " + ex.getMessage());
            }
            
            scanAllMenuItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (selectedMessages.length > 0) {
                        // Send the first selected message to the scanner
                        mainPanel.loadRequest(selectedMessages[0]);
                        
                        // Switch to our tab
                        callbacks.customizeUiComponent(mainPanel);
                        int tabIndex = getTabIndex();
                        if (tabIndex != -1) {
                            JTabbedPane burpTabs = getBurpSuiteJTabbedPane();
                            if (burpTabs != null) {
                                burpTabs.setSelectedIndex(tabIndex);
                            }
                        }
                        
                        // Start the scan with all parameters
                        mainPanel.scanAllParameters();
                    }
                }
            });
            
            // Add "Scan Empty Parameters" menu item for targeted quick scanning
            JMenuItem scanEmptyMenuItem = new JMenuItem("Scan Empty Parameters");
            scanEmptyMenuItem.setToolTipText("Send to scanner and start scanning only empty parameters (often more vulnerable) - By " + AUTHOR);
            
            // Try to add icon to the scan empty menu item
            try {
                // Try to use a system icon first
                Icon scanEmptyIcon = UIManager.getIcon("Tree.leafIcon");
                if (scanEmptyIcon == null) {
                    scanEmptyIcon = UIManager.getIcon("Tree.collapsedIcon");
                }
                if (scanEmptyIcon != null) {
                    scanEmptyMenuItem.setIcon(scanEmptyIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load scan empty menu item icon: " + ex.getMessage());
            }
            
            scanEmptyMenuItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (selectedMessages.length > 0) {
                        // Send the first selected message to the scanner
                        mainPanel.loadRequest(selectedMessages[0]);
                        
                        // Switch to our tab
                        callbacks.customizeUiComponent(mainPanel);
                        int tabIndex = getTabIndex();
                        if (tabIndex != -1) {
                            JTabbedPane burpTabs = getBurpSuiteJTabbedPane();
                            if (burpTabs != null) {
                                burpTabs.setSelectedIndex(tabIndex);
                            }
                        }
                        
                        // Start the scan with only empty parameters
                        mainPanel.scanEmptyParameters();
                    }
                }
            });
            
            // Add "Send to Mass Testing" menu item
            JMenuItem sendToMassTestingMenuItem = new JMenuItem("Send to Mass Testing");
            sendToMassTestingMenuItem.setToolTipText("Send the selected request(s) to the Mass Testing tab for batch processing - By " + AUTHOR);
            
            // Try to add icon to the mass testing menu item
            try {
                // Try to use a system icon first
                Icon massTestingIcon = UIManager.getIcon("FileView.directoryIcon");
                if (massTestingIcon == null) {
                    massTestingIcon = UIManager.getIcon("FileChooser.listViewIcon");
                }
                if (massTestingIcon != null) {
                    sendToMassTestingMenuItem.setIcon(massTestingIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load mass testing menu item icon: " + ex.getMessage());
            }
            
            sendToMassTestingMenuItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    // Add all selected messages to mass testing
                    for (IHttpRequestResponse message : selectedMessages) {
                        mainPanel.addRequestToMassTesting(message);
                    }
                    
                    // Switch to our tab and then to mass testing tab
                    callbacks.customizeUiComponent(mainPanel);
                    int tabIndex = getTabIndex();
                    if (tabIndex != -1) {
                        JTabbedPane burpTabs = getBurpSuiteJTabbedPane();
                        if (burpTabs != null) {
                            burpTabs.setSelectedIndex(tabIndex);
                            // Show the mass testing tab
                            mainPanel.showMassTestingTab();
                        }
                    }
                }
            });
            
            // Add "Send to SQL Error Testing" menu item
            JMenuItem sendToSQLErrorTestingMenuItem = new JMenuItem("🚨 Send to SQL Error Testing");
            sendToSQLErrorTestingMenuItem.setToolTipText("Send the selected request(s) to the SQL Error Testing tab for database error detection - By " + AUTHOR);

            // Try to add icon to the SQL error testing menu item
            try {
                // Try to use a system icon first
                Icon sqlErrorIcon = UIManager.getIcon("OptionPane.errorIcon");
                if (sqlErrorIcon == null) {
                    sqlErrorIcon = UIManager.getIcon("OptionPane.warningIcon");
                }
                if (sqlErrorIcon != null) {
                    sendToSQLErrorTestingMenuItem.setIcon(sqlErrorIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load SQL error testing menu item icon: " + ex.getMessage());
            }

            sendToSQLErrorTestingMenuItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    try {
                        callbacks.printOutput("Context Menu: SQL Error Testing option selected from submenu");

                        // Add all selected messages to SQL error testing
                        for (IHttpRequestResponse message : selectedMessages) {
                            callbacks.printOutput("Context Menu: Adding message to SQL Error Testing");
                            mainPanel.addRequestToSQLErrorDetection(message);
                        }

                        // Switch to our tab and then to SQL error testing tab
                        callbacks.customizeUiComponent(mainPanel);
                        int tabIndex = getTabIndex();
                        if (tabIndex != -1) {
                            JTabbedPane burpTabs = getBurpSuiteJTabbedPane();
                            if (burpTabs != null) {
                                burpTabs.setSelectedIndex(tabIndex);
                                // Show the SQL error testing tab
                                mainPanel.showSQLErrorDetectionTab();
                            }
                        }

                        // Show notification
                        String messageText = selectedMessages.length == 1 ?
                            "1 request sent to SQL Error Testing" :
                            selectedMessages.length + " requests sent to SQL Error Testing";
                        callbacks.printOutput(messageText);

                    } catch (Exception ex) {
                        callbacks.printError("Context Menu: Error in SQL Error Testing submenu action - " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            });

            // Add menu items to submenu
            scannerMenu.add(sendToScannerMenuItem);
            scannerMenu.addSeparator();
            scannerMenu.add(scanAllMenuItem);
            scannerMenu.add(scanEmptyMenuItem);
            scannerMenu.addSeparator();
            scannerMenu.add(sendToMassTestingMenuItem);
            scannerMenu.add(sendToSQLErrorTestingMenuItem);
            
            // Add main menu item for compatibility with older versions
            JMenuItem mainMenuItem = new JMenuItem("Send to Time-Based Scanner");
            mainMenuItem.setToolTipText("Send the selected request to the Time-Based Scanner extension - By " + AUTHOR);
            
            // Try to add icon to the main menu item
            try {
                // Use the same icon as the menu
                ImageIcon icon = IconUtils.getMenuIcon("/icons/time_scanner_icon.svg");
                if (icon != null) {
                    mainMenuItem.setIcon(icon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load main menu icon: " + ex.getMessage());
            }
            
            mainMenuItem.addActionListener(sendToScannerMenuItem.getActionListeners()[0]);

            // Add standalone SQL Error Testing menu item for better visibility
            JMenuItem sqlErrorMenuItem = new JMenuItem("🚨 Send to SQL Error Testing");
            sqlErrorMenuItem.setToolTipText("Send the selected request(s) to SQL Error Testing for database error detection - By " + AUTHOR);

            // Try to add icon to the standalone SQL error menu item
            try {
                Icon sqlErrorIcon = UIManager.getIcon("OptionPane.errorIcon");
                if (sqlErrorIcon == null) {
                    sqlErrorIcon = UIManager.getIcon("OptionPane.warningIcon");
                }
                if (sqlErrorIcon != null) {
                    sqlErrorMenuItem.setIcon(sqlErrorIcon);
                }
            } catch (Exception ex) {
                callbacks.printError("Could not load standalone SQL error menu icon: " + ex.getMessage());
            }

            // Use the same action as the submenu item
            sqlErrorMenuItem.addActionListener(sendToSQLErrorTestingMenuItem.getActionListeners()[0]);

            // Add all options to menu items
            menuItems.add(scannerMenu);
            menuItems.add(mainMenuItem);
            menuItems.add(sqlErrorMenuItem);
        }
        
        return menuItems;
    }
    
    /**
     * Helper method to find our tab index
     */
    private int getTabIndex() {
        // Get the root component of Burp's UI
        Component rootComponent = SwingUtilities.getRoot(mainPanel);
        if (rootComponent instanceof JFrame) {
            JFrame rootFrame = (JFrame) rootComponent;
            
            // Try to find the main tabbed pane
            JTabbedPane tabbedPane = getBurpSuiteJTabbedPane();
            if (tabbedPane != null) {
                // Look for our tab
                for (int i = 0; i < tabbedPane.getTabCount(); i++) {
                    String title = tabbedPane.getTitleAt(i);
                    if (extensionName.equals(title)) {
                        return i;
                    }
                }
            }
        }
        
        return -1;
    }
    
    /**
     * Helper method to find Burp's tabbed pane
     */
    private JTabbedPane getBurpSuiteJTabbedPane() {
        // Get the root component of Burp's UI
        Component rootComponent = SwingUtilities.getRoot(mainPanel);
        if (rootComponent instanceof JFrame) {
            JFrame rootFrame = (JFrame) rootComponent;
            
            // Search for the tabbed pane in the component hierarchy
            return findTabbedPane(rootFrame.getContentPane());
        }
        
        return null;
    }
    
    /**
     * Recursively search for JTabbedPane in the component hierarchy
     */
    private JTabbedPane findTabbedPane(Component component) {
        if (component instanceof JTabbedPane) {
            return (JTabbedPane) component;
        } else if (component instanceof Container) {
            Container container = (Container) component;
            for (int i = 0; i < container.getComponentCount(); i++) {
                JTabbedPane result = findTabbedPane(container.getComponent(i));
                if (result != null) {
                    return result;
                }
            }
        }
        
        return null;
    }
}
