package com.timebasedscan.model;

/**
 * Represents the result of a time-based test
 */
public class TestResult {

    private String parameterName;
    private String parameterType;
    private String parameterValue;
    private String result;
    private String evidence;
    private byte[] request;
    private byte[] response;
    private long responseTime;
    private long baselineTime;

    // SQL error detection fields
    private boolean sqlErrorDetected;
    private String sqlErrorType;
    private String sqlErrorMessage;
    private String sqlErrorSnippet;

    // Additional fields for SQL Error Detection Panel
    private boolean vulnerable;
    private String databaseType;
    private String errorType;
    private String errorMessage;
    private String payload;

    // Default constructor for SQL Error Detection Panel
    public TestResult() {
        this.sqlErrorDetected = false;
        this.vulnerable = false;
    }

    public TestResult(String parameterName, String parameterType, String parameterValue,
                     String result, String evidence, byte[] request, byte[] response,
                     long responseTime, long baselineTime) {
        this.parameterName = parameterName;
        this.parameterType = parameterType;
        this.parameterValue = parameterValue;
        this.result = result;
        this.evidence = evidence;
        this.request = request;
        this.response = response;
        this.responseTime = responseTime;
        this.baselineTime = baselineTime;

        // Initialize SQL error fields
        this.sqlErrorDetected = false;
        this.sqlErrorType = null;
        this.sqlErrorMessage = null;
        this.sqlErrorSnippet = null;
    }

    // Constructor with SQL error detection
    public TestResult(String parameterName, String parameterType, String parameterValue,
                     String result, String evidence, byte[] request, byte[] response,
                     long responseTime, long baselineTime,
                     boolean sqlErrorDetected, String sqlErrorType, String sqlErrorMessage, String sqlErrorSnippet) {
        this.parameterName = parameterName;
        this.parameterType = parameterType;
        this.parameterValue = parameterValue;
        this.result = result;
        this.evidence = evidence;
        this.request = request;
        this.response = response;
        this.responseTime = responseTime;
        this.baselineTime = baselineTime;
        this.sqlErrorDetected = sqlErrorDetected;
        this.sqlErrorType = sqlErrorType;
        this.sqlErrorMessage = sqlErrorMessage;
        this.sqlErrorSnippet = sqlErrorSnippet;
    }

    public String getParameterName() {
        return parameterName;
    }

    public String getParameterType() {
        return parameterType;
    }

    public String getParameterValue() {
        return parameterValue;
    }

    public String getResult() {
        return result;
    }

    /**
     * Get the evidence with enhanced formatting
     */
    public String getEvidence() {
        return evidence;
    }
    
    /**
     * Get a formatted HTML version of the evidence for enhanced display
     */
    public String getFormattedEvidence() {
        if (evidence == null || evidence.isEmpty()) {
            return "";
        }
        
        // Format the evidence in HTML for better readability
        StringBuilder formattedEvidence = new StringBuilder("<html>");
        
        // Style the evidence based on the result
        String bgColor = isVulnerable() ? "#fff0f0" : "#f0f0ff";
        String borderColor = isVulnerable() ? "#ffcccc" : "#ccccff";
        
        formattedEvidence.append("<div style='font-family: Arial, sans-serif; padding: 5px; ")
                         .append("background-color: ").append(bgColor).append("; ")
                         .append("border: 1px solid ").append(borderColor).append("; ")
                         .append("border-radius: 3px;'>");
        
        // Special formatting for MySQL SLEEP payloads
        if (evidence.contains("SLEEP") || parameterValue.contains("SLEEP")) {
            formattedEvidence.append("<div style='font-weight: bold; color: #cc0000;'>MySQL SLEEP Payload Detected</div>");
        }
        
        // Format the timing information
        if (evidence.contains("Baseline:")) {
            String[] parts = evidence.split(",");
            for (String part : parts) {
                part = part.trim();
                if (part.startsWith("Baseline:")) {
                    formattedEvidence.append("<div style='margin: 2px 0;'><span style='color: #666666;'>")
                                    .append(part).append("</span></div>");
                } else if (part.startsWith("With payload:")) {
                    formattedEvidence.append("<div style='margin: 2px 0;'><span style='color: #0000cc; font-weight: bold;'>")
                                    .append(part).append("</span></div>");
                } else if (part.startsWith("Delay:")) {
                    // Bold and red if significant delay
                    if (getTimeDifference() > 500 || getDelayFactor() > 1.5) {
                        formattedEvidence.append("<div style='margin: 2px 0;'><span style='color: #cc0000; font-weight: bold;'>")
                                        .append(part).append("</span></div>");
                    } else {
                        formattedEvidence.append("<div style='margin: 2px 0;'><span style='color: #333333;'>")
                                        .append(part).append("</span></div>");
                    }
                }
            }
        } else {
            // If evidence doesn't have the expected format, display it directly
            formattedEvidence.append("<div>").append(evidence).append("</div>");
        }
        
        formattedEvidence.append("</div></html>");
        return formattedEvidence.toString();
    }

    public byte[] getRequest() {
        return request;
    }

    public byte[] getResponse() {
        return response;
    }

    public long getResponseTime() {
        return responseTime;
    }

    public long getBaselineTime() {
        return baselineTime;
    }

    public long getTimeDifference() {
        return responseTime - baselineTime;
    }
    
    /**
     * Get the method used in the request (GET, POST, etc.)
     */
    public String getMethod() {
        // In a real implementation, this would be parsed from the request
        return "POST"; // Default value for now
    }
    
    /**
     * Get the URL of the request
     */
    public String getUrl() {
        // In a real implementation, this would be parsed from the request
        return "https://example.com/api/resource"; // Default value for now
    }
    
    /**
     * Check if this result indicates a vulnerability
     */
    public boolean isVulnerable() {
        return vulnerable || "Vulnerable".equals(result) || "Potentially Vulnerable".equals(result);
    }
    
    /**
     * Get the response time with payload
     */
    public long getTimeWithPayload() {
        return responseTime;
    }
    
    /**
     * Get the delay factor (ratio of payload time to baseline time)
     */
    public double getDelayFactor() {
        if (baselineTime <= 0) return 0;
        return (double) responseTime / baselineTime;
    }

    /**
     * Check if SQL error was detected
     */
    public boolean isSqlErrorDetected() {
        return sqlErrorDetected;
    }

    /**
     * Get the SQL error database type
     */
    public String getSqlErrorType() {
        return sqlErrorType;
    }

    /**
     * Get the SQL error message
     */
    public String getSqlErrorMessage() {
        return sqlErrorMessage;
    }

    /**
     * Get the SQL error snippet
     */
    public String getSqlErrorSnippet() {
        return sqlErrorSnippet;
    }

    /**
     * Set SQL error detection information
     */
    public void setSqlErrorInfo(boolean detected, String type, String message, String snippet) {
        this.sqlErrorDetected = detected;
        this.sqlErrorType = type;
        this.sqlErrorMessage = message;
        this.sqlErrorSnippet = snippet;
    }

    /**
     * Get formatted SQL error information for display
     */
    public String getFormattedSqlError() {
        if (!sqlErrorDetected) {
            return "No SQL Error";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SQL Error Detected");
        if (sqlErrorType != null) {
            sb.append(" (").append(sqlErrorType).append(")");
        }
        if (sqlErrorMessage != null) {
            sb.append(": ").append(sqlErrorMessage);
        }
        return sb.toString();
    }

    // Setter methods for SQL Error Detection Panel
    public void setParameterName(String parameterName) {
        this.parameterName = parameterName;
    }

    public void setParameterType(String parameterType) {
        this.parameterType = parameterType;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public void setResponseTime(long responseTime) {
        this.responseTime = responseTime;
    }

    public void setVulnerable(boolean vulnerable) {
        this.vulnerable = vulnerable;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public void setRequest(byte[] request) {
        this.request = request;
    }

    public void setResponse(byte[] response) {
        this.response = response;
    }

    // Getter methods for SQL Error Detection Panel
    public String getPayload() {
        return payload;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public String getErrorType() {
        return errorType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
