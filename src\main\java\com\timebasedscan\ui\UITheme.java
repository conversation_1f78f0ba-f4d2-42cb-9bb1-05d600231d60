package com.timebasedscan.ui;

import javax.swing.*;
import javax.swing.border.Border;
import java.awt.*;

/**
 * Centralized theme control for consistent UI design
 */
public class UITheme {
    // Primary color scheme
    public static final Color PRIMARY_COLOR = new Color(66, 133, 180);      // Main blue color
    public static final Color PRIMARY_DARK = new Color(49, 100, 135);       // Darker blue for accents
    public static final Color PRIMARY_LIGHT = new Color(160, 200, 230);     // Lighter blue for highlights
    
    // Secondary colors
    public static final Color SECONDARY_COLOR = new Color(255, 204, 0);     // Yellow gold for important buttons
    public static final Color ACCENT_COLOR = new Color(180, 66, 66);        // Red for alerts and vulnerabilities
    
    // Background and text colors
    public static final Color BACKGROUND_LIGHT = new Color(245, 245, 250);  // Off-white light background
    public static final Color BACKGROUND_MEDIUM = new Color(230, 230, 240); // Medium background for panel headers
    public static final Color TEXT_COLOR = new Color(50, 50, 60);           // Dark text
    public static final Color TEXT_LIGHT = new Color(120, 120, 130);        // Light text for secondary information
    
    // Border colors
    public static final Color BORDER_COLOR = new Color(200, 200, 210);      // Light border
    
    // Row colors for tables
    public static final Color ROW_EVEN = new Color(255, 255, 255);          // White for even rows
    public static final Color ROW_ODD = new Color(240, 240, 245);           // Light blue-gray for odd rows
    public static final Color ROW_VULNERABLE = new Color(255, 240, 240);    // Light red for vulnerable rows
    
    // Standard fonts
    public static final Font TITLE_FONT = new Font("Sans-Serif", Font.BOLD, 16);
    public static final Font HEADER_FONT = new Font("Sans-Serif", Font.BOLD, 14);
    public static final Font REGULAR_FONT = new Font("Sans-Serif", Font.PLAIN, 12);
    public static final Font SMALL_FONT = new Font("Sans-Serif", Font.PLAIN, 11);
    public static final Font MONOSPACE_FONT = new Font("Monospaced", Font.PLAIN, 12);
    
    // Standard borders
    public static Border createPanelBorder(String title) {
        return BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(BORDER_COLOR, 1, true),
                title
            ),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        );
    }
    
    public static Border createEmptyBorder() {
        return BorderFactory.createEmptyBorder(10, 10, 10, 10);
    }
    
    /**
     * Create a gradient button with the primary color scheme
     */
    public static JButton createPrimaryButton(String text) {
        JButton button = new JButton(text) {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D)g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                int w = getWidth();
                int h = getHeight();
                
                // Create gradient background
                GradientPaint gp = new GradientPaint(
                    0, 0, PRIMARY_COLOR,
                    0, h, PRIMARY_DARK
                );
                
                g2d.setPaint(gp);
                g2d.fillRoundRect(0, 0, w, h, 10, 10);
                
                // Add slight bevel effect
                g2d.setColor(new Color(255, 255, 255, 50));
                g2d.drawRoundRect(0, 0, w-1, h/2, 10, 10);
                
                g2d.setColor(new Color(0, 0, 0, 50));
                g2d.drawRoundRect(0, 0, w-1, h-1, 10, 10);
                
                g2d.dispose();
                
                super.paintComponent(g);
            }
        };
        
        button.setForeground(Color.WHITE);
        button.setFont(REGULAR_FONT);
        button.setOpaque(false);
        button.setBorderPainted(false);
        button.setContentAreaFilled(false);
        button.setFocusPainted(false);
        
        return button;
    }
    
    /**
     * Create a gradient button with the secondary color scheme
     */
    public static JButton createSecondaryButton(String text) {
        JButton button = new JButton(text) {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D)g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                int w = getWidth();
                int h = getHeight();
                
                // Create gradient background
                GradientPaint gp = new GradientPaint(
                    0, 0, SECONDARY_COLOR,
                    0, h, new Color(220, 176, 0)
                );
                
                g2d.setPaint(gp);
                g2d.fillRoundRect(0, 0, w, h, 10, 10);
                
                // Add slight bevel effect
                g2d.setColor(new Color(255, 255, 255, 50));
                g2d.drawRoundRect(0, 0, w-1, h/2, 10, 10);
                
                g2d.setColor(new Color(0, 0, 0, 50));
                g2d.drawRoundRect(0, 0, w-1, h-1, 10, 10);
                
                g2d.dispose();
                
                super.paintComponent(g);
            }
        };
        
        button.setForeground(Color.BLACK);
        button.setFont(REGULAR_FONT);
        button.setOpaque(false);
        button.setBorderPainted(false);
        button.setContentAreaFilled(false);
        button.setFocusPainted(false);
        
        return button;
    }
    
    /**
     * Apply color scheme to a JTabbedPane
     */
    public static void styleTabPane(JTabbedPane tabbedPane) {
        tabbedPane.setFont(REGULAR_FONT);
        tabbedPane.setBackground(BACKGROUND_LIGHT);
        tabbedPane.setForeground(TEXT_COLOR);
    }
    
    /**
     * Create a gradient panel with the primary color scheme
     */
    public static JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D)g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                int w = getWidth();
                int h = getHeight();
                
                // Create gradient background
                GradientPaint gp = new GradientPaint(
                    0, 0, new Color(240, 240, 245),
                    0, h, new Color(225, 225, 235)
                );
                
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
                
                // Add a subtle line at the bottom
                g2d.setColor(BORDER_COLOR);
                g2d.drawLine(0, h-1, w, h-1);
                
                g2d.dispose();
            }
        };
        
        panel.setOpaque(false);
        panel.setBorder(BorderFactory.createEmptyBorder(8, 10, 8, 10));
        
        return panel;
    }
    
    /**
     * Create the attribution gradient text
     */
    public static JLabel createAttributionLabel() {
        JLabel attributionLabel = new JLabel("Created by Pankaj Kumar Thakur") {
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                // Create gradient for text
                GradientPaint gp = new GradientPaint(
                    0, 0, new Color(100, 100, 150), 
                    getWidth(), 0, new Color(66, 133, 180)
                );
                g2d.setPaint(gp);
                
                // Draw text with gradient
                g2d.setFont(getFont());
                FontMetrics fm = g2d.getFontMetrics();
                int textWidth = fm.stringWidth(getText());
                int x = getWidth() - textWidth - 5;
                int y = (getHeight() - fm.getHeight()) / 2 + fm.getAscent();
                g2d.drawString(getText(), x, y);
                
                g2d.dispose();
            }
        };
        
        attributionLabel.setFont(REGULAR_FONT);
        attributionLabel.setForeground(new Color(0, 0, 0, 0)); // Transparent as we're custom painting
        attributionLabel.setHorizontalAlignment(JLabel.RIGHT);
        attributionLabel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 10));
        
        return attributionLabel;
    }
}