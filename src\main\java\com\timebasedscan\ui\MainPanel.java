package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IContextMenuFactory;
import burp.IContextMenuInvocation;
import burp.IMessageEditor;
import burp.IHttpService;
import burp.ITab;

import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.scanner.ParallelScanner;
import com.timebasedscan.utils.HttpParameterParser;


import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Main UI panel for the Time-Based Scanner extension
 */
public class MainPanel extends JPanel implements IContextMenuFactory, ITab {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private JTabbedPane tabbedPane;
    private RequestPanel requestPanel;
    private ResultsPanel resultsPanel;
    private ScanControlPanel scanControlPanel;
    private HistoryPanel historyPanel;
    private MassTestingPanel massTestingPanel;
    private SQLErrorDetectionPanel sqlErrorDetectionPanel;
    private IHttpRequestResponse currentRequest;
    private List<HttpParameter> detectedParameters;
    private static final String AUTHOR_CREDIT = "Created by Pankaj Kumar Thakur - Professional Time-Based SQL Injection Scanner";

    public MainPanel(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        
        // Register context menu
        callbacks.registerContextMenuFactory(this);
        
        // Initialize UI components
        initializeUI();
    }

    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Create tabbed pane with enhanced styling
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(new Font("SansSerif", Font.BOLD, 12));
        tabbedPane.setBackground(new Color(245, 245, 245));
        tabbedPane.setForeground(new Color(50, 50, 50));
        
        // Create request panel
        requestPanel = new RequestPanel(callbacks, helpers);
        
        // Create results panel
        resultsPanel = new ResultsPanel(callbacks);
        
        // Create history panel
        callbacks.printOutput("Creating history panel...");
        historyPanel = new HistoryPanel(callbacks);
        callbacks.printOutput("History panel created successfully.");
        
        // Create mass testing panel
        massTestingPanel = new MassTestingPanel(callbacks, helpers, this);

        // Create SQL error detection panel
        sqlErrorDetectionPanel = new SQLErrorDetectionPanel(callbacks, helpers, this);

        // Create scan control panel
        scanControlPanel = new ScanControlPanel(callbacks, helpers, this, requestPanel, resultsPanel);
        
        // Add tabs with icons for better UI
        
        // Try to add scan control tab with icon (first tab)
        try {
            Icon controlIcon = UIManager.getIcon("FileChooser.hardDriveIcon");
            if (controlIcon == null) {
                controlIcon = UIManager.getIcon("FileChooser.hardDriveIcon");
            }
            tabbedPane.addTab("🚀 Scan Controls", controlIcon, scanControlPanel,
                "Configure and Start Scan Operations - Set payloads, configure SQL error detection, start vulnerability scans - " + AUTHOR_CREDIT);
        } catch (Exception ex) {
            tabbedPane.addTab("Scan Controls", scanControlPanel);
            callbacks.printError("Could not load scan control tab icon: " + ex.getMessage());
        }
        
        // Try to add request tab with icon (second tab)
        try {
            Icon requestIcon = UIManager.getIcon("FileView.directoryIcon");
            if (requestIcon == null) {
                requestIcon = UIManager.getIcon("FileView.directoryIcon");
            }
            tabbedPane.addTab("📋 Request", requestIcon, requestPanel,
                "Request Analysis & Parameter Selection - Load requests, view detected parameters (JSON, XML, GraphQL), select parameters for testing - " + AUTHOR_CREDIT);
        } catch (Exception ex) {
            tabbedPane.addTab("Request", requestPanel);
            callbacks.printError("Could not load request tab icon: " + ex.getMessage());
        }
        
        // Try to add results tab with icon (third tab)
        try {
            // Use a system icon if custom icon is not available
            Icon resultIcon = UIManager.getIcon("Table.descendingSortIcon");
            if (resultIcon == null) {
                resultIcon = UIManager.getIcon("Table.descendingSortIcon");
            }
            tabbedPane.addTab("📊 Results", resultIcon, resultsPanel,
                "Scan Results & Vulnerability Analysis - View time-based and SQL error detection results, export reports, send to Repeater - " + AUTHOR_CREDIT);
        } catch (Exception ex) {
            tabbedPane.addTab("Results", resultsPanel);
            callbacks.printError("Could not load result tab icon: " + ex.getMessage());
        }
        
        // Try to add history tab with icon (fourth tab)
        try {
            callbacks.printOutput("Adding history tab to tabbed pane...");
            // Use a system icon if custom icon is not available
            Icon historyIcon = UIManager.getIcon("FileChooser.listViewIcon");
            if (historyIcon == null) {
                historyIcon = UIManager.getIcon("FileChooser.listViewIcon");
            }
            tabbedPane.addTab("📚 History", historyIcon, historyPanel,
                "Scan History & Previous Results - Review past scan sessions, compare findings, generate historical reports - " + AUTHOR_CREDIT);
            callbacks.printOutput("History tab added successfully. Total tabs: " + tabbedPane.getTabCount());
        } catch (Exception ex) {
            callbacks.printError("Error adding history tab with icon: " + ex.getMessage());
            ex.printStackTrace();
            try {
                tabbedPane.addTab("History", historyPanel);
                callbacks.printOutput("History tab added without icon. Total tabs: " + tabbedPane.getTabCount());
            } catch (Exception ex2) {
                callbacks.printError("Failed to add history tab at all: " + ex2.getMessage());
                ex2.printStackTrace();
            }
        }
        
        // Try to add mass testing tab with icon (fifth tab)
        try {
            // Use a system icon if custom icon is not available
            Icon massTestingIcon = UIManager.getIcon("FileView.computerIcon");
            if (massTestingIcon == null) {
                massTestingIcon = UIManager.getIcon("OptionPane.informationIcon");
            }
            tabbedPane.addTab("⚡ Mass Testing", massTestingIcon, massTestingPanel,
                "Bulk Request Testing & Automation - Test multiple requests simultaneously, batch vulnerability assessment, enterprise-scale testing - " + AUTHOR_CREDIT);
        } catch (Exception ex) {
            tabbedPane.addTab("Mass Testing", massTestingPanel);
            callbacks.printError("Could not load mass testing tab icon: " + ex.getMessage());
        }

        // Try to add SQL error detection tab with icon (sixth tab)
        try {
            // Use a system icon if custom icon is not available
            Icon sqlErrorIcon = UIManager.getIcon("OptionPane.errorIcon");
            if (sqlErrorIcon == null) {
                sqlErrorIcon = UIManager.getIcon("OptionPane.warningIcon");
            }
            tabbedPane.addTab("🚨 SQL Errors", sqlErrorIcon, sqlErrorDetectionPanel,
                "SQL Error Detection & Database Testing - Quick SQL error discovery with built-in payloads for all database types - " + AUTHOR_CREDIT);
        } catch (Exception ex) {
            tabbedPane.addTab("SQL Errors", sqlErrorDetectionPanel);
            callbacks.printError("Could not load SQL error detection tab icon: " + ex.getMessage());
        }
        
        // Create professional header with author attribution
        JPanel headerPanel = createProfessionalHeader();

        // Add components to the main panel
        add(headerPanel, BorderLayout.NORTH);
        add(tabbedPane, BorderLayout.CENTER);
    }

    /**
     * Create a professional header with author attribution and scanner info
     */
    private JPanel createProfessionalHeader() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(45, 45, 45)); // Dark background
        headerPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));

        // Left side - Scanner title and version
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        leftPanel.setOpaque(false);

        JLabel titleLabel = new JLabel("🛡️ Time-Based SQL Injection Scanner");
        titleLabel.setFont(new Font("SansSerif", Font.BOLD, 18));
        titleLabel.setForeground(new Color(100, 200, 255)); // Light blue

        JLabel versionLabel = new JLabel("v1.0.0 Professional Edition");
        versionLabel.setFont(new Font("SansSerif", Font.PLAIN, 12));
        versionLabel.setForeground(new Color(180, 180, 180)); // Light gray

        leftPanel.add(titleLabel);
        leftPanel.add(Box.createHorizontalStrut(15));
        leftPanel.add(versionLabel);

        // Right side - Author attribution
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        rightPanel.setOpaque(false);

        JLabel authorLabel = new JLabel("Created by Pankaj Kumar Thakur");
        authorLabel.setFont(new Font("SansSerif", Font.BOLD, 14));
        authorLabel.setForeground(new Color(255, 215, 0)); // Gold color

        JLabel expertiseLabel = new JLabel("Security Research & Development");
        expertiseLabel.setFont(new Font("SansSerif", Font.ITALIC, 11));
        expertiseLabel.setForeground(new Color(200, 200, 200)); // Light gray

        // Create vertical layout for right side
        JPanel rightVertical = new JPanel();
        rightVertical.setLayout(new BoxLayout(rightVertical, BoxLayout.Y_AXIS));
        rightVertical.setOpaque(false);

        authorLabel.setAlignmentX(Component.RIGHT_ALIGNMENT);
        expertiseLabel.setAlignmentX(Component.RIGHT_ALIGNMENT);

        rightVertical.add(authorLabel);
        rightVertical.add(expertiseLabel);

        rightPanel.add(rightVertical);

        // Center - Feature highlights
        JPanel centerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 20, 0));
        centerPanel.setOpaque(false);

        String[] features = {"🚀 Time-Based", "🔍 All Formats", "🚨 SQL Errors", "⚡ Mass Testing", "📊 Reports"};
        for (String feature : features) {
            JLabel featureLabel = new JLabel(feature);
            featureLabel.setFont(new Font("SansSerif", Font.PLAIN, 11));
            featureLabel.setForeground(new Color(150, 255, 150)); // Light green
            featureLabel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(100, 100, 100), 1),
                BorderFactory.createEmptyBorder(2, 6, 2, 6)
            ));
            centerPanel.add(featureLabel);
        }

        headerPanel.add(leftPanel, BorderLayout.WEST);
        headerPanel.add(centerPanel, BorderLayout.CENTER);
        headerPanel.add(rightPanel, BorderLayout.EAST);

        return headerPanel;
    }

    /**
     * Method to load a request from the context menu or other sources
     * This is the public method called by our extension
     */
    public void loadRequest(IHttpRequestResponse requestResponse) {
        // Set the current request
        setCurrentRequest(requestResponse);
        
        // Optionally switch to the Request tab after loading (index 1 since we added Scan Controls tab)
        tabbedPane.setSelectedIndex(1);
        
        // Log that we've received a request
        if (requestResponse != null) {
            IHttpService service = requestResponse.getHttpService();
            String host = service.getHost();
            int port = service.getPort();
            String protocol = service.getProtocol();
            String url = protocol + "://" + host + ":" + port;
            
            callbacks.printOutput("Request loaded from: " + url);
            callbacks.printOutput("Detected " + detectedParameters.size() + " parameters");
            
            // If parameters were detected, provide some information about them
            if (detectedParameters.size() > 0) {
                StringBuilder paramInfo = new StringBuilder("Parameters by type: ");
                java.util.Map<String, Integer> typeCount = new java.util.HashMap<>();
                
                // Count parameter types
                for (HttpParameter param : detectedParameters) {
                    String type = param.getType().toString();
                    typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
                }
                
                // Report counts
                for (java.util.Map.Entry<String, Integer> entry : typeCount.entrySet()) {
                    paramInfo.append(entry.getKey()).append("(").append(entry.getValue()).append(") ");
                }
                
                callbacks.printOutput(paramInfo.toString());
            }
        }
    }
    
    /**
     * Internal method to update the current request and UI
     */
    public void setCurrentRequest(IHttpRequestResponse requestResponse) {
        this.currentRequest = requestResponse;
        
        if (requestResponse != null) {
            // Set the request in the request panel
            requestPanel.setRequest(requestResponse);
            
            // Parse parameters
            HttpParameterParser parser = new HttpParameterParser(helpers);
            this.detectedParameters = parser.parseParameters(requestResponse);
            
            // Update parameter list in request panel (with filtering based on checkboxes)
            boolean includeNameInjection = scanControlPanel.isParameterNameInjectionEnabled();
            boolean includeUrlPathInjection = scanControlPanel.isUrlPathInjectionEnabled();
            boolean includeHeaderParameters = scanControlPanel.isHeaderParametersEnabled();
            requestPanel.setParameters(detectedParameters, includeNameInjection, includeUrlPathInjection, includeHeaderParameters);

            // Update UI in scan control panel
            scanControlPanel.requestLoaded(requestResponse, detectedParameters);
        } else {
            // Update UI in scan control panel for null request
            scanControlPanel.requestLoaded(null, null);
        }
    }

    /**
     * Make the help dialog accessible to the ScanControlPanel
     */
    public void showHelpDialog() {
        JTextPane helpText = new JTextPane();
        helpText.setContentType("text/html");
        helpText.setText(
            "<html><body style='font-family:Arial; width:400px; padding:10px;'>" +
            "<h2>Time-Based Scanner Help</h2>" +
            "<h3>Quick Start Guide:</h3>" +
            "<ol>" +
            "<li><b>Select Parameters</b>: Choose which parameters to test from the table</li>" +
            "<li><b>Configure Payload</b>: Select or enter a time-based payload</li>" +
            "<li><b>Set Parallel Requests</b>: Adjust concurrency level based on target</li>" +
            "<li><b>Start Scan</b>: Click the green button to begin testing</li>" +
            "</ol>" +
            "<h3>Tips for Better Results:</h3>" +
            "<ul>" +
            "<li>Start with low concurrency (3-5) for stable results</li>" +
            "<li>For slower applications, increase the timeout in your payloads</li>" +
            "<li>Empty parameters are often more prone to injection</li>" +
            "<li>Check 'Potentially Vulnerable' results by examining the request/response</li>" +
            "</ul>" +
            "<p><b>Note:</b> This scanner detects time-based vulnerabilities by measuring response times and applying " +
            "intelligent thresholds. False positives are possible in unstable networks.</p>" +
            "<p style='margin-top:20px; border-top:1px solid #ccc; padding-top:10px;'><b>Author:</b> Pankaj Kumar Thakur (<EMAIL>)</p>" +
            "</body></html>"
        );
        helpText.setEditable(false);
        helpText.setCaretPosition(0);
        
        JScrollPane scrollPane = new JScrollPane(helpText);
        scrollPane.setPreferredSize(new Dimension(500, 400));
        
        JOptionPane.showMessageDialog(
            this,
            scrollPane,
            "Time-Based Scanner - Help",
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    /**
     * Switch to the request tab
     */
    public void showRequestTab() {
        tabbedPane.setSelectedComponent(requestPanel);
    }
    
    /**
     * Switch to the results tab
     */
    public void showResultsTab() {
        tabbedPane.setSelectedComponent(resultsPanel);
    }
    
    /**
     * Switch to the history tab
     */
    public void showHistoryTab() {
        tabbedPane.setSelectedComponent(historyPanel);
    }
    
    /**
     * Get the history panel
     */
    public HistoryPanel getHistoryPanel() {
        return historyPanel;
    }
    
    /**
     * Get the results panel
     */
    public ResultsPanel getResultsPanel() {
        return resultsPanel;
    }
    
    /**
     * Get the current request
     * Used by the Mass Testing panel
     */
    public IHttpRequestResponse getCurrentRequest() {
        return currentRequest;
    }
    
    /**
     * Add a request directly to the Mass Testing panel
     * Used when requests are sent from context menu
     */
    public void addRequestToMassTesting(IHttpRequestResponse request) {
        if (request != null && massTestingPanel != null) {
            massTestingPanel.addRequest(request);
        }
    }

    /**
     * Add a request directly to the SQL Error Detection panel
     * Used when requests are sent from context menu
     */
    public void addRequestToSQLErrorDetection(IHttpRequestResponse request) {
        if (request == null) {
            callbacks.printError("MainPanel: Cannot add null request to SQL Error Detection");
            return;
        }

        if (sqlErrorDetectionPanel == null) {
            callbacks.printError("MainPanel: SQL Error Detection panel is null");
            return;
        }

        try {
            callbacks.printOutput("MainPanel: Sending request to SQL Error Detection panel");
            sqlErrorDetectionPanel.addRequest(request);
            callbacks.printOutput("MainPanel: Request sent successfully to SQL Error Detection");
        } catch (Exception e) {
            callbacks.printError("MainPanel: Error sending request to SQL Error Detection - " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Switch to the Mass Testing tab
     * Used when requests are sent from context menu
     */
    public void showMassTestingTab() {
        if (tabbedPane != null && massTestingPanel != null) {
            tabbedPane.setSelectedComponent(massTestingPanel);
        }
    }

    /**
     * Switch to the SQL Error Detection tab
     * Used when requests are sent from context menu
     */
    public void showSQLErrorDetectionTab() {
        if (tabbedPane != null && sqlErrorDetectionPanel != null) {
            tabbedPane.setSelectedComponent(sqlErrorDetectionPanel);
        }
    }
    
    /**
     * Start scanning all selected parameters
     */
    public void startScan() {
        if (currentRequest == null || detectedParameters.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "No request selected or no parameters found.", 
                "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Get user inputs from the ScanControlPanel
        List<String> payloads = scanControlPanel.getSelectedPayloads();
        int concurrencyLevel = scanControlPanel.getConcurrencyLevel();
        List<HttpParameter> selectedParameters = requestPanel.getSelectedParameters();

        if (selectedParameters.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please select at least one parameter to test.",
                "No Parameters Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (payloads.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please provide at least one valid payload.",
                "No Payloads", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Clear previous results
        resultsPanel.clearResults();

        // Switch to results tab
        tabbedPane.setSelectedComponent(resultsPanel);

        // Start scanning with multiple payloads
        startMultiPayloadScan(selectedParameters, payloads, concurrencyLevel);
    }

    /**
     * Start scanning with multiple payloads
     */
    private void startMultiPayloadScan(List<HttpParameter> parameters, List<String> payloads, int concurrencyLevel) {
        // Run the scanner in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    callbacks.printOutput("Starting multi-payload scan with " + payloads.size() + " payloads and " +
                                        parameters.size() + " parameters");

                    // Test with each payload
                    for (int payloadIndex = 0; payloadIndex < payloads.size(); payloadIndex++) {
                        String currentPayload = payloads.get(payloadIndex);

                        callbacks.printOutput("Testing payload " + (payloadIndex + 1) + "/" + payloads.size() +
                            ": " + currentPayload.substring(0, Math.min(50, currentPayload.length())) + "...");

                        // Create scanner for this payload with SQL error detection settings
                        boolean sqlErrorDetection = scanControlPanel.isSqlErrorDetectionEnabled();
                        boolean showSqlErrors = scanControlPanel.shouldShowSqlErrorsInResults();
                        ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
                            currentRequest, parameters, currentPayload, concurrencyLevel, MainPanel.this,
                            sqlErrorDetection, showSqlErrors);

                        // Configure rate limiting bypass
                        scanner.configureRateLimitBypass(
                            scanControlPanel.isRateLimitBypassEnabled(),
                            scanControlPanel.getRateLimitBypassMethod(),
                            scanControlPanel.getRateLimitBaseDelay(),
                            scanControlPanel.isUserAgentRandomizationEnabled(),
                            scanControlPanel.isXForwardedForRandomizationEnabled(),
                            scanControlPanel.isProxyRotationEnabled()
                        );

                        // Configure advanced payload injection
                        scanner.configureAdvancedPayloadInjection(
                            scanControlPanel.isEncodingBypassEnabled(),
                            scanControlPanel.isWafEvasionEnabled(),
                            scanControlPanel.isContextAwareInjectionEnabled(),
                            scanControlPanel.isMultipleEncodingLayersEnabled()
                        );

                        // Configure custom header injection
                        scanner.configureCustomHeaderInjection(
                            scanControlPanel.isCustomHeaderInjectionEnabled(),
                            scanControlPanel.getCustomHeaderName(),
                            scanControlPanel.getCustomHeaderValue()
                        );

                        // Start the scan for this specific payload
                        scanner.startScan();

                        // Add a small delay between payloads to avoid overwhelming the server
                        if (payloadIndex < payloads.size() - 1) {
                            Thread.sleep(1000); // 1 second delay between payloads
                        }
                    }

                    callbacks.printOutput("Multi-payload scan completed. Tested " + payloads.size() + " payloads.");

                } catch (Exception ex) {
                    callbacks.printError("Error during multi-payload scan: " + ex.getMessage());
                }
            }
        }).start();
    }

    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        List<JMenuItem> menuItems = new ArrayList<>();
        
        // Only show menu if right-clicking on a request
        if (invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_PROXY_HISTORY ||
            invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_MESSAGE_EDITOR_REQUEST ||
            invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TABLE) {
            
            JMenuItem sendToScanner = new JMenuItem("Send to Time-Based Scanner");
            sendToScanner.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    IHttpRequestResponse[] messages = invocation.getSelectedMessages();
                    if (messages != null && messages.length > 0) {
                        setCurrentRequest(messages[0]);

                        // Switch to our extension tab
                        Component tabComponent = getSelfComponent();
                        callbacks.customizeUiComponent(tabComponent);
                        callbacks.addSuiteTab(new ITab() {
                            @Override
                            public String getTabCaption() {
                                return "Time-Based Scanner";
                            }

                            @Override
                            public Component getUiComponent() {
                                return tabComponent;
                            }
                        });
                    }
                }
            });

            JMenuItem sendToMassTesting = new JMenuItem("Send to Mass Testing");
            sendToMassTesting.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    IHttpRequestResponse[] messages = invocation.getSelectedMessages();
                    if (messages != null && messages.length > 0) {
                        for (IHttpRequestResponse message : messages) {
                            addRequestToMassTesting(message);
                        }
                        showMassTestingTab();
                    }
                }
            });

            JMenuItem sendToSQLErrorDetection = new JMenuItem("🚨 Send to SQL Error Testing");
            sendToSQLErrorDetection.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    try {
                        callbacks.printOutput("Context Menu: SQL Error Testing option selected");
                        IHttpRequestResponse[] messages = invocation.getSelectedMessages();

                        if (messages == null) {
                            callbacks.printError("Context Menu: No messages selected (null)");
                            return;
                        }

                        if (messages.length == 0) {
                            callbacks.printError("Context Menu: No messages selected (empty array)");
                            return;
                        }

                        callbacks.printOutput("Context Menu: Processing " + messages.length + " selected messages");

                        for (int i = 0; i < messages.length; i++) {
                            IHttpRequestResponse message = messages[i];
                            callbacks.printOutput("Context Menu: Processing message " + (i + 1) + "/" + messages.length);
                            addRequestToSQLErrorDetection(message);
                        }

                        showSQLErrorDetectionTab();

                        // Show notification
                        String messageText = messages.length == 1 ?
                            "1 request sent to SQL Error Testing" :
                            messages.length + " requests sent to SQL Error Testing";
                        callbacks.printOutput(messageText);

                    } catch (Exception ex) {
                        callbacks.printError("Context Menu: Error in SQL Error Testing action - " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            });

            menuItems.add(sendToScanner);
            menuItems.add(sendToMassTesting);
            menuItems.add(sendToSQLErrorDetection);
        }
        
        return menuItems;
    }

    private Component getSelfComponent() {
        return this;
    }
    
    @Override
    public String getTabCaption() {
        return "Time-Based Scanner";
    }
    
    @Override
    public Component getUiComponent() {
        return this;
    }
    
    /**
     * Scan all parameters in the current request
     * This is called from the context menu
     */
    public void scanAllParameters() {
        if (currentRequest == null || detectedParameters == null || detectedParameters.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "No request loaded or no parameters detected.", 
                "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Get payload and concurrency level from scan control panel
        String payload = scanControlPanel.getPayload();
        int concurrencyLevel = scanControlPanel.getConcurrencyLevel();
        
        // Select all parameters in the UI for better UX
        requestPanel.selectAllParameters();
        
        // Clear previous results
        resultsPanel.clearResults();
        
        // Start scanning with all parameters and SQL error detection settings
        boolean sqlErrorDetection = scanControlPanel.isSqlErrorDetectionEnabled();
        boolean showSqlErrors = scanControlPanel.shouldShowSqlErrorsInResults();
        ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
            currentRequest, new ArrayList<>(detectedParameters), payload,
            concurrencyLevel, this, sqlErrorDetection, showSqlErrors);

        // Configure rate limiting bypass
        scanner.configureRateLimitBypass(
            scanControlPanel.isRateLimitBypassEnabled(),
            scanControlPanel.getRateLimitBypassMethod(),
            scanControlPanel.getRateLimitBaseDelay(),
            scanControlPanel.isUserAgentRandomizationEnabled(),
            scanControlPanel.isXForwardedForRandomizationEnabled(),
            scanControlPanel.isProxyRotationEnabled()
        );

        // Configure advanced payload injection
        scanner.configureAdvancedPayloadInjection(
            scanControlPanel.isEncodingBypassEnabled(),
            scanControlPanel.isWafEvasionEnabled(),
            scanControlPanel.isContextAwareInjectionEnabled(),
            scanControlPanel.isMultipleEncodingLayersEnabled()
        );

        // Configure custom header injection
        scanner.configureCustomHeaderInjection(
            scanControlPanel.isCustomHeaderInjectionEnabled(),
            scanControlPanel.getCustomHeaderName(),
            scanControlPanel.getCustomHeaderValue()
        );

        // Switch to results tab
        tabbedPane.setSelectedComponent(resultsPanel);
        
        // Show a notification
        callbacks.printOutput("Started scanning all " + detectedParameters.size() + " parameters");
        
        // Run the scanner in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                scanner.startScan();
            }
        }).start();
    }
    
    /**
     * Scan only empty parameters in the current request
     * Empty parameters are often more vulnerable to injection
     * This is called from the context menu
     */
    /**
     * Scan a single selected parameter from the parameter table
     * This is called from the Scan Selected Parameter button
     */
    public void scanSelectedParameter() {
        if (currentRequest == null || detectedParameters == null || detectedParameters.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "No request loaded or no parameters detected.", 
                "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Get the single selected parameter from the request panel
        HttpParameter selectedParam = requestPanel.getHighlightedParameter();
        
        if (selectedParam == null) {
            JOptionPane.showMessageDialog(this, 
                "Please select a single parameter in the table to test.", 
                "No Parameter Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        // Get payload and concurrency level from scan control panel
        String payload = scanControlPanel.getPayload();
        int concurrencyLevel = scanControlPanel.getConcurrencyLevel();
        
        // Clear previous results
        resultsPanel.clearResults();
        
        // Start scanning with only the selected parameter
        List<HttpParameter> singleParamList = new ArrayList<>();
        singleParamList.add(selectedParam);
        
        // Get SQL error detection settings from scan control panel
        boolean sqlErrorDetection = scanControlPanel.isSqlErrorDetectionEnabled();
        boolean showSqlErrors = scanControlPanel.shouldShowSqlErrorsInResults();
        ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
            currentRequest, singleParamList, payload, concurrencyLevel, this,
            sqlErrorDetection, showSqlErrors);

        // Configure rate limiting bypass
        scanner.configureRateLimitBypass(
            scanControlPanel.isRateLimitBypassEnabled(),
            scanControlPanel.getRateLimitBypassMethod(),
            scanControlPanel.getRateLimitBaseDelay(),
            scanControlPanel.isUserAgentRandomizationEnabled(),
            scanControlPanel.isXForwardedForRandomizationEnabled(),
            scanControlPanel.isProxyRotationEnabled()
        );

        // Configure advanced payload injection
        scanner.configureAdvancedPayloadInjection(
            scanControlPanel.isEncodingBypassEnabled(),
            scanControlPanel.isWafEvasionEnabled(),
            scanControlPanel.isContextAwareInjectionEnabled(),
            scanControlPanel.isMultipleEncodingLayersEnabled()
        );

        // Configure custom header injection
        scanner.configureCustomHeaderInjection(
            scanControlPanel.isCustomHeaderInjectionEnabled(),
            scanControlPanel.getCustomHeaderName(),
            scanControlPanel.getCustomHeaderValue()
        );

        // Switch to results tab
        tabbedPane.setSelectedComponent(resultsPanel);
        
        // Show a notification
        callbacks.printOutput("Started scanning selected parameter: " + selectedParam.getName() + " (" + selectedParam.getType() + ")");
        
        // Run the scanner in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                scanner.startScan();
            }
        }).start();
    }
    
    public void scanEmptyParameters() {
        if (currentRequest == null || detectedParameters == null || detectedParameters.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "No request loaded or no parameters detected.", 
                "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Get payload and concurrency level from scan control panel
        String payload = scanControlPanel.getPayload();
        int concurrencyLevel = scanControlPanel.getConcurrencyLevel();
        
        // Filter only empty parameters
        List<HttpParameter> emptyParams = new ArrayList<>();
        for (HttpParameter param : detectedParameters) {
            if (param.isEmpty()) {
                emptyParams.add(param);
            }
        }
        
        if (emptyParams.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "No empty parameters found in this request.", 
                "Information", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        // Select empty parameters in the UI for better UX
        requestPanel.selectOnlyEmptyParameters();
        
        // Clear previous results
        resultsPanel.clearResults();
        
        // Start scanning with empty parameters and SQL error detection settings
        boolean sqlErrorDetection = scanControlPanel.isSqlErrorDetectionEnabled();
        boolean showSqlErrors = scanControlPanel.shouldShowSqlErrorsInResults();
        ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
            currentRequest, emptyParams, payload, concurrencyLevel, this,
            sqlErrorDetection, showSqlErrors);

        // Configure rate limiting bypass
        scanner.configureRateLimitBypass(
            scanControlPanel.isRateLimitBypassEnabled(),
            scanControlPanel.getRateLimitBypassMethod(),
            scanControlPanel.getRateLimitBaseDelay(),
            scanControlPanel.isUserAgentRandomizationEnabled(),
            scanControlPanel.isXForwardedForRandomizationEnabled(),
            scanControlPanel.isProxyRotationEnabled()
        );

        // Configure advanced payload injection
        scanner.configureAdvancedPayloadInjection(
            scanControlPanel.isEncodingBypassEnabled(),
            scanControlPanel.isWafEvasionEnabled(),
            scanControlPanel.isContextAwareInjectionEnabled(),
            scanControlPanel.isMultipleEncodingLayersEnabled()
        );

        // Configure custom header injection
        scanner.configureCustomHeaderInjection(
            scanControlPanel.isCustomHeaderInjectionEnabled(),
            scanControlPanel.getCustomHeaderName(),
            scanControlPanel.getCustomHeaderValue()
        );

        // Switch to results tab
        tabbedPane.setSelectedComponent(resultsPanel);
        
        // Show a notification
        callbacks.printOutput("Started scanning " + emptyParams.size() + " empty parameters");
        
        // Run the scanner in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                scanner.startScan();
            }
        }).start();
    }

    /**
     * Get the currently selected parameters for scanning
     */
    public List<HttpParameter> getSelectedParameters() {
        return requestPanel.getSelectedParameters();
    }

    /**
     * Refresh the parameter list based on current checkbox settings
     */
    public void refreshParameterList() {
        if (detectedParameters != null) {
            boolean includeNameInjection = scanControlPanel.isParameterNameInjectionEnabled();
            boolean includeUrlPathInjection = scanControlPanel.isUrlPathInjectionEnabled();
            boolean includeHeaderParameters = scanControlPanel.isHeaderParametersEnabled();
            requestPanel.setParameters(detectedParameters, includeNameInjection, includeUrlPathInjection, includeHeaderParameters);
        }
    }

    /**
     * Start mass testing with configuration from scan control panel
     */
    public void startMassTestingWithConfig(List<String> payloads, int concurrencyLevel,
                                         boolean sqlErrorDetection, boolean paramNameInjection,
                                         boolean urlPathInjection, boolean headerParameters) {
        if (massTestingPanel != null) {
            massTestingPanel.startMassTestingWithConfig(payloads, concurrencyLevel, sqlErrorDetection,
                                                      paramNameInjection, urlPathInjection, headerParameters);
        }
    }
}
