package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IMessageEditor;
import com.timebasedscan.model.ResultTableModel;
import com.timebasedscan.model.ResultTableModel.ResultStatistics;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.utils.IconUtils;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableRowSorter;
import javax.swing.RowFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Enhanced panel for displaying scan results with summary statistics
 * and improved filtering and visualization
 */
public class ResultsPanel extends JPanel {

    private IBurpExtenderCallbacks callbacks;
    private JTable resultsTable;
    private ResultTableModel tableModel;
    private IMessageEditor requestViewer;
    private IMessageEditor responseViewer;
    private JLabel statusLabel;
    private JLabel progressLabel;
    private JProgressBar progressBar;
    private JEditorPane summaryPane;
    private JPanel summaryPanel;
    private JButton exportButton;
    private JToggleButton showOnlyVulnerableButton;
    private int totalTests = 0;
    private int completedTests = 0;

    public ResultsPanel(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        
        initializeUI();
    }

    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Create top panel for controls (removed extra attribution)
        JPanel topPanel = new JPanel(new BorderLayout());
        
        // Add the top panel to the main layout
        add(topPanel, BorderLayout.NORTH);
        
        // Create results table
        tableModel = new ResultTableModel();
        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.setAutoCreateRowSorter(true);
        
        // Custom cell renderer for status column
        resultsTable.getColumnModel().getColumn(3).setCellRenderer(new StatusCellRenderer());

        // Custom cell renderer for SQL error column
        resultsTable.getColumnModel().getColumn(7).setCellRenderer(new SqlErrorCellRenderer());
        
        // Add row sorter
        TableRowSorter<ResultTableModel> sorter = new TableRowSorter<>(tableModel);
        resultsTable.setRowSorter(sorter);
        
        // Create viewers
        requestViewer = callbacks.createMessageEditor(null, false);
        responseViewer = callbacks.createMessageEditor(null, false);
        
        // Set up result selection listener and context menu
        resultsTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = resultsTable.getSelectedRow();
                if (row != -1) {
                    row = resultsTable.convertRowIndexToModel(row);
                    TestResult result = tableModel.getResultAt(row);
                    if (result != null) {
                        requestViewer.setMessage(result.getRequest(), true);
                        responseViewer.setMessage(result.getResponse(), false);
                    }
                }
            }

            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showContextMenu(e);
                }
            }
        });
        
        // Create summary panel
        summaryPanel = new JPanel();
        summaryPanel.setLayout(new BoxLayout(summaryPanel, BoxLayout.Y_AXIS));
        summaryPanel.setBorder(BorderFactory.createTitledBorder("Scan Summary"));
        
        // Create a JEditorPane that properly renders HTML content
        summaryPane = new JEditorPane();
        summaryPane.setContentType("text/html");
        summaryPane.setEditable(false);
        summaryPane.setText("<html><body><div style='font-family: Arial, sans-serif; padding: 10px;'>No scan results available</div></body></html>");
        summaryPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Add scrollpane to handle overflow content
        JScrollPane summaryScrollPane = new JScrollPane(summaryPane);
        summaryScrollPane.setBorder(BorderFactory.createEmptyBorder());
        summaryPanel.add(summaryScrollPane);
        
        // Create control buttons panel with toolbar styling
        JPanel controlPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        controlPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0));
        
        // Filter button for showing only vulnerable params with icon
        showOnlyVulnerableButton = new JToggleButton("Show Only Vulnerable");
        showOnlyVulnerableButton.setToolTipText("Show only potentially vulnerable parameters in the results table");
        
        // Add filter icon
        try {
            Icon filterIcon = UIManager.getIcon("Table.descendingSortIcon");
            if (filterIcon == null) {
                // Try alternative system icon
                filterIcon = UIManager.getIcon("FileChooser.viewMenuIcon");
            }
            if (filterIcon != null) {
                showOnlyVulnerableButton.setIcon(filterIcon);
            }
        } catch (Exception ex) {
            callbacks.printError("Could not load filter button icon: " + ex.getMessage());
        }
        
        showOnlyVulnerableButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                applyFilters();
            }
        });
        
        // Export results button with icon
        exportButton = new JButton("Export Results");
        exportButton.setToolTipText("Export scan results to a file");
        
        // Add export icon
        try {
            Icon exportIcon = UIManager.getIcon("FileView.floppyDriveIcon");
            if (exportIcon == null) {
                // Try alternative system icon
                exportIcon = UIManager.getIcon("FileChooser.saveIcon");
            }
            if (exportIcon != null) {
                exportButton.setIcon(exportIcon);
            }
        } catch (Exception ex) {
            callbacks.printError("Could not load export button icon: " + ex.getMessage());
        }
        
        exportButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                exportResults();
            }
        });
        
        // Add refresh button with icon
        JButton refreshButton = new JButton("Refresh View");
        refreshButton.setToolTipText("Refresh the results view and statistics");
        
        // Add refresh icon
        try {
            Icon refreshIcon = UIManager.getIcon("FileChooser.refreshIcon");
            if (refreshIcon == null) {
                // Try alternative system icon
                refreshIcon = UIManager.getIcon("FileChooser.upFolderIcon");
            }
            if (refreshIcon != null) {
                refreshButton.setIcon(refreshIcon);
            }
        } catch (Exception ex) {
            callbacks.printError("Could not load refresh button icon: " + ex.getMessage());
        }
        
        refreshButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                updateSummary();
                tableModel.fireTableDataChanged();
            }
        });
        
        // Add buttons to panel with spacing
        controlPanel.add(showOnlyVulnerableButton);
        controlPanel.add(Box.createHorizontalStrut(5));
        controlPanel.add(exportButton);
        controlPanel.add(Box.createHorizontalStrut(5));
        controlPanel.add(refreshButton);
        
        // Create combined content panel
        JPanel contentTopPanel = new JPanel(new BorderLayout());
        contentTopPanel.add(summaryPanel, BorderLayout.CENTER);
        contentTopPanel.add(controlPanel, BorderLayout.SOUTH);
        
        // Add the summary and controls to our main top panel
        topPanel.add(contentTopPanel, BorderLayout.CENTER);
        
        // Create status panel
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Status label
        statusLabel = new JLabel("Ready");
        
        // Progress bar and label
        JPanel progressPanel = new JPanel(new BorderLayout(5, 0));
        progressLabel = new JLabel("0/0 tests completed");
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        
        progressPanel.add(progressLabel, BorderLayout.WEST);
        progressPanel.add(progressBar, BorderLayout.CENTER);
        
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(progressPanel, BorderLayout.EAST);
        
        // Create split panes for request/response viewers
        JSplitPane viewerSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        
        // Add labels to request/response tabs with icons
        JTabbedPane requestTab = new JTabbedPane();
        
        // Try to add icon to request tab
        try {
            Icon requestIcon = UIManager.getIcon("FileView.directoryIcon");
            if (requestIcon == null) {
                // Try alternative icon
                requestIcon = IconUtils.getTabIcon("/icons/time_scanner_icon.svg");
            }
            if (requestIcon != null) {
                requestTab.addTab("Request", requestIcon, requestViewer.getComponent(), "HTTP request details");
            } else {
                requestTab.addTab("Request", requestViewer.getComponent());
            }
        } catch (Exception ex) {
            requestTab.addTab("Request", requestViewer.getComponent());
            callbacks.printError("Could not load request tab icon: " + ex.getMessage());
        }
        
        JTabbedPane responseTab = new JTabbedPane();
        
        // Try to add icon to response tab
        try {
            Icon responseIcon = UIManager.getIcon("FileView.fileIcon");
            if (responseIcon == null) {
                // Try alternative icon
                responseIcon = IconUtils.getTabIcon("/icons/time_scanner_icon.svg");
            }
            if (responseIcon != null) {
                responseTab.addTab("Response", responseIcon, responseViewer.getComponent(), "HTTP response details");
            } else {
                responseTab.addTab("Response", responseViewer.getComponent());
            }
        } catch (Exception ex) {
            responseTab.addTab("Response", responseViewer.getComponent());
            callbacks.printError("Could not load response tab icon: " + ex.getMessage());
        }
        
        viewerSplitPane.setLeftComponent(requestTab);
        viewerSplitPane.setRightComponent(responseTab);
        viewerSplitPane.setResizeWeight(0.5);
        
        // Create main content area with table and viewers
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.add(new JScrollPane(resultsTable), BorderLayout.CENTER);
        
        // Create the main split pane
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        mainSplitPane.setTopComponent(contentPanel);
        mainSplitPane.setBottomComponent(viewerSplitPane);
        mainSplitPane.setResizeWeight(0.6);
        
        // Add components to panel
        add(topPanel, BorderLayout.NORTH);
        add(mainSplitPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    /**
     * Show context menu for results table
     */
    private void showContextMenu(MouseEvent e) {
        int row = resultsTable.rowAtPoint(e.getPoint());
        if (row >= 0) {
            // Select the row that was right-clicked
            resultsTable.setRowSelectionInterval(row, row);

            // Convert to model index
            int modelRow = resultsTable.convertRowIndexToModel(row);
            TestResult result = tableModel.getResultAt(modelRow);

            if (result != null && result.getRequest() != null) {
                // Create context menu
                JPopupMenu contextMenu = new JPopupMenu();

                // Send to Repeater menu item
                JMenuItem sendToRepeaterItem = new JMenuItem("Send to Repeater");
                sendToRepeaterItem.setToolTipText("Send this request to Burp's Repeater tool for manual testing");

                // Add icon if available
                try {
                    Icon repeaterIcon = UIManager.getIcon("FileView.directoryIcon");
                    if (repeaterIcon != null) {
                        sendToRepeaterItem.setIcon(repeaterIcon);
                    }
                } catch (Exception ex) {
                    // Icon not available, continue without it
                }

                sendToRepeaterItem.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent ae) {
                        sendToRepeater(result);
                    }
                });

                contextMenu.add(sendToRepeaterItem);

                // Add separator
                contextMenu.addSeparator();

                // Copy request menu item
                JMenuItem copyRequestItem = new JMenuItem("Copy Request");
                copyRequestItem.setToolTipText("Copy the HTTP request to clipboard");
                copyRequestItem.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent ae) {
                        copyRequestToClipboard(result);
                    }
                });

                contextMenu.add(copyRequestItem);

                // Copy response menu item (if response exists)
                if (result.getResponse() != null && result.getResponse().length > 0) {
                    JMenuItem copyResponseItem = new JMenuItem("Copy Response");
                    copyResponseItem.setToolTipText("Copy the HTTP response to clipboard");
                    copyResponseItem.addActionListener(new ActionListener() {
                        @Override
                        public void actionPerformed(ActionEvent ae) {
                            copyResponseToClipboard(result);
                        }
                    });

                    contextMenu.add(copyResponseItem);
                }

                // Add separator
                contextMenu.addSeparator();

                // Copy evidence menu item
                JMenuItem copyEvidenceItem = new JMenuItem("Copy Evidence");
                copyEvidenceItem.setToolTipText("Copy the vulnerability evidence to clipboard");
                copyEvidenceItem.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent ae) {
                        copyEvidenceToClipboard(result);
                    }
                });

                contextMenu.add(copyEvidenceItem);

                // Show the context menu
                contextMenu.show(resultsTable, e.getX(), e.getY());
            }
        }
    }

    /**
     * Send the selected request to Burp's Repeater tool
     */
    private void sendToRepeater(TestResult result) {
        try {
            if (result.getRequest() != null) {
                // Extract service information from the request
                byte[] request = result.getRequest();

                // Parse the request to get the service details
                String requestStr = new String(request, "UTF-8");
                String[] lines = requestStr.split("\r?\n");

                String host = "localhost";
                int port = 80;
                boolean useHttps = false;

                // Look for Host header
                for (String line : lines) {
                    if (line.toLowerCase().startsWith("host:")) {
                        String hostHeader = line.substring(5).trim();
                        if (hostHeader.contains(":")) {
                            String[] hostParts = hostHeader.split(":");
                            host = hostParts[0];
                            try {
                                port = Integer.parseInt(hostParts[1]);
                            } catch (NumberFormatException ex) {
                                port = useHttps ? 443 : 80;
                            }
                        } else {
                            host = hostHeader;
                            port = useHttps ? 443 : 80;
                        }
                        break;
                    }
                }

                // Check if it's HTTPS by looking at the first line or port
                if (port == 443 || (lines.length > 0 && lines[0].toLowerCase().contains("https"))) {
                    useHttps = true;
                    if (port == 80) port = 443;
                }

                // Send to Repeater with a descriptive tab name
                String tabName = "Time-Based: " + result.getParameterName();
                callbacks.sendToRepeater(host, port, useHttps, request, tabName);

                // Show success message
                statusLabel.setText("Request sent to Repeater: " + tabName);

                // Log the action
                callbacks.printOutput("Sent request to Repeater - Parameter: " + result.getParameterName() +
                                    ", Host: " + host + ":" + port + (useHttps ? " (HTTPS)" : " (HTTP)"));

            } else {
                JOptionPane.showMessageDialog(this,
                    "No request data available for this result.",
                    "Send to Repeater",
                    JOptionPane.WARNING_MESSAGE);
            }
        } catch (Exception e) {
            callbacks.printError("Error sending request to Repeater: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "Error sending request to Repeater: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Copy request to clipboard
     */
    private void copyRequestToClipboard(TestResult result) {
        try {
            if (result.getRequest() != null) {
                String requestStr = new String(result.getRequest(), "UTF-8");
                java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(requestStr);
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(stringSelection, null);

                statusLabel.setText("Request copied to clipboard");
            }
        } catch (Exception e) {
            callbacks.printError("Error copying request to clipboard: " + e.getMessage());
        }
    }

    /**
     * Copy response to clipboard
     */
    private void copyResponseToClipboard(TestResult result) {
        try {
            if (result.getResponse() != null) {
                String responseStr = new String(result.getResponse(), "UTF-8");
                java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(responseStr);
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(stringSelection, null);

                statusLabel.setText("Response copied to clipboard");
            }
        } catch (Exception e) {
            callbacks.printError("Error copying response to clipboard: " + e.getMessage());
        }
    }

    /**
     * Copy evidence to clipboard
     */
    private void copyEvidenceToClipboard(TestResult result) {
        try {
            StringBuilder evidence = new StringBuilder();
            evidence.append("Parameter: ").append(result.getParameterName()).append("\n");
            evidence.append("Type: ").append(result.getParameterType()).append("\n");
            evidence.append("Payload: ").append(result.getParameterValue()).append("\n");
            evidence.append("Status: ").append(result.getResult()).append("\n");
            evidence.append("Evidence: ").append(result.getEvidence()).append("\n");
            evidence.append("Response Time: ").append(result.getResponseTime()).append("ms\n");
            evidence.append("Baseline Time: ").append(result.getBaselineTime()).append("ms\n");
            evidence.append("Delay Factor: ").append(String.format("%.2fx", result.getDelayFactor())).append("\n");
            evidence.append("Time Difference: ").append(result.getTimeDifference()).append("ms\n");

            java.awt.datatransfer.StringSelection stringSelection =
                new java.awt.datatransfer.StringSelection(evidence.toString());
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(stringSelection, null);

            statusLabel.setText("Evidence copied to clipboard");
        } catch (Exception e) {
            callbacks.printError("Error copying evidence to clipboard: " + e.getMessage());
        }
    }

    /**
     * Add a test result to the results panel
     */
    public void addResult(TestResult result) {
        SwingUtilities.invokeLater(() -> {
            tableModel.addResult(result);
            completedTests++;
            updateProgress();
            
            // Update summary whenever a new result is added
            updateSummary();
            
            // Re-apply filters if they are active
            if (showOnlyVulnerableButton.isSelected()) {
                applyFilters();
            }
        });
    }

    /**
     * Clear all results
     */
    public void clearResults() {
        SwingUtilities.invokeLater(() -> {
            tableModel.clearResults();
            requestViewer.setMessage(new byte[0], true);
            responseViewer.setMessage(new byte[0], false);
            totalTests = 0;
            completedTests = 0;
            updateProgress();
            updateSummary();
            statusLabel.setText("Ready");
            
            // Reset filters
            showOnlyVulnerableButton.setSelected(false);
            applyFilters();
        });
    }

    /**
     * Set the total number of tests to be performed
     */
    public void setTotalTests(int total) {
        SwingUtilities.invokeLater(() -> {
            totalTests = total;
            completedTests = 0;
            updateProgress();
            statusLabel.setText("Scanning in progress...");
        });
    }

    /**
     * Update the progress indicators
     */
    private void updateProgress() {
        if (totalTests > 0) {
            int percentage = (int)((double)completedTests / totalTests * 100);
            progressBar.setValue(percentage);
            progressLabel.setText(completedTests + "/" + totalTests + " tests completed");
            
            if (completedTests >= totalTests) {
                statusLabel.setText("Scan completed at " + 
                    new SimpleDateFormat("HH:mm:ss").format(new Date()));
            }
        } else {
            progressBar.setValue(0);
            progressLabel.setText("0/0 tests completed");
        }
    }
    
    /**
     * Update the summary information with enhanced visual styling
     */
    private void updateSummary() {
        ResultStatistics stats = tableModel.getStatistics();
        
        StringBuilder summary = new StringBuilder("<html>");
        summary.append("<div style='font-family: Arial, sans-serif; width: 100%;'>");
        
        if (stats.getTotalTests() == 0) {
            summary.append("<div style='text-align: center; padding: 15px; color: #666;'>");
            summary.append("<span style='font-size: 14pt;'>No scan results available</span><br>");
            summary.append("<span style='font-size: 10pt;'>Use \"Send to Time-Based Scanner\" from the context menu to test requests</span>");
            summary.append("</div>");
        } else {
            // Create boxed summary with gradient background
            summary.append("<div style='background: linear-gradient(to bottom, #f5f5f5, #e5e5e5); ")
                   .append("border-radius: 5px; padding: 10px; margin-bottom: 10px; ")
                   .append("border: 1px solid #ddd;'>");
            
            // Main statistics
            summary.append("<div style='font-size: 12pt; font-weight: bold; margin-bottom: 5px; color: #333;'>")
                   .append("Scan Summary")
                   .append("</div>");
            
            // Statistics in a clean grid layout
            summary.append("<table style='width: 100%; border-collapse: collapse;'>");
            
            // Total parameters row
            summary.append("<tr>");
            summary.append("<td style='padding: 3px; width: 65%; font-weight: bold;'>Total Parameters Tested:</td>");
            summary.append("<td style='padding: 3px; color: #0066cc; font-weight: bold;'>")
                   .append(stats.getTotalTests())
                   .append("</td>");
            summary.append("</tr>");
            
            // Vulnerable parameters row
            summary.append("<tr>");
            summary.append("<td style='padding: 3px; font-weight: bold;'>Potentially Vulnerable Parameters:</td>");
            
            // Using stats.getVulnerableCount() directly to avoid variable duplication
            String vulnColor = (stats.getVulnerableCount() > 0) ? "#cc0000" : "#009933";
            
            summary.append("<td style='padding: 3px; color: ")
                   .append(vulnColor)
                   .append("; font-weight: bold;'>")
                   .append(stats.getVulnerableCount());
            
            // Add percentage if we have vulnerabilities
            if (stats.getVulnerableCount() > 0) {
                summary.append(" (")
                       .append(String.format("%.1f", stats.getVulnerablePercentage()))
                       .append("%)");
            }
            
            summary.append("</td>");
            summary.append("</tr>");
            
            // Average response time row
            summary.append("<tr>");
            summary.append("<td style='padding: 3px; font-weight: bold;'>Average Response Time:</td>");
            summary.append("<td style='padding: 3px; color: #666;'>")
                   .append(String.format("%.2f ms", stats.getAverageResponseTime()))
                   .append("</td>");
            summary.append("</tr>");
            
            // Close main stats table
            summary.append("</table>");
            summary.append("</div>");
            
            // Add breakdown by parameter type if we have results
            if (!stats.getTypeStats().isEmpty()) {
                // Parameter type breakdown with lighter box style
                summary.append("<div style='background-color: #f9f9f9; border-radius: 5px; ")
                       .append("padding: 10px; border: 1px solid #ddd;'>");
                
                summary.append("<div style='font-size: 11pt; font-weight: bold; margin-bottom: 5px; color: #333;'>")
                       .append("Parameter Types Breakdown")
                       .append("</div>");
                
                // Create a table for parameter types
                summary.append("<table style='width: 100%; border-collapse: collapse; font-size: 10pt;'>");
                summary.append("<tr style='background-color: #eaeaea;'>")
                       .append("<th style='text-align: left; padding: 3px;'>Type</th>")
                       .append("<th style='text-align: center; padding: 3px;'>Count</th>")
                       .append("<th style='text-align: center; padding: 3px;'>Vulnerable</th>")
                       .append("</tr>");
                
                // Alternate row colors for better readability
                int rowCount = 0;
                for (Map.Entry<String, Integer> entry : stats.getTypeStats().entrySet()) {
                    String type = entry.getKey();
                    int count = entry.getValue();
                    int vulnCount = stats.getVulnerableTypeStats().getOrDefault(type, 0);
                    
                    // Alternate row background
                    String rowStyle = (rowCount % 2 == 0) ? 
                        "background-color: #f5f5f5;" : "background-color: #ffffff;";
                    rowCount++;
                    
                    summary.append("<tr style='").append(rowStyle).append("'>");
                    
                    // Parameter type column
                    summary.append("<td style='padding: 3px; text-align: left;'>")
                           .append(formatParameterType(type))
                           .append("</td>");
                    
                    // Count column
                    summary.append("<td style='padding: 3px; text-align: center;'>")
                           .append(count)
                           .append("</td>");
                    
                    // Vulnerable column with color coding
                    String vulnCellColor = (vulnCount > 0) ? "#ffebeb" : "#ffffff";
                    String vulnTextColor = (vulnCount > 0) ? "#cc0000" : "#333333";
                    String vulnTextWeight = (vulnCount > 0) ? "bold" : "normal";
                    
                    summary.append("<td style='padding: 3px; text-align: center; background-color: ")
                           .append(vulnCellColor)
                           .append("; color: ")
                           .append(vulnTextColor)
                           .append("; font-weight: ")
                           .append(vulnTextWeight)
                           .append(";'>")
                           .append(vulnCount)
                           .append("</td>");
                    
                    summary.append("</tr>");
                }
                
                // Close the table
                summary.append("</table>");
                summary.append("</div>");
            }
        }
        
        // Add scan timestamp if available
        if (stats.getTotalTests() > 0) {
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            summary.append("<div style='margin-top: 5px; font-size: 8pt; color: #666; text-align: right;'>")
                   .append("Last updated: ")
                   .append(timestamp)
                   .append("</div>");
        }
        
        summary.append("</div>");
        summary.append("</html>");
        summaryPane.setText(summary.toString());
        // Scroll to the top of the summary pane
        summaryPane.setCaretPosition(0);
    }
    
    /**
     * Format parameter type for better readability in summary
     */
    private String formatParameterType(String paramType) {
        if (paramType == null) return "Unknown";
        
        switch (paramType) {
            case "REST_ID": return "REST API ID";
            case "RESOURCE_ID": return "Resource ID";
            case "HEADER_AUTH": return "Auth Header";
            case "ENCODED": return "Encoded Data";
            case "NESTED_JSON": return "Nested JSON";
            default: return paramType;
        }
    }
    
    /**
     * Apply filters to the results table with enhanced filtering capabilities
     */
    private void applyFilters() {
        TableRowSorter<ResultTableModel> sorter = 
            (TableRowSorter<ResultTableModel>) resultsTable.getRowSorter();
        
        // Create appropriate filter based on current filter settings
        if (showOnlyVulnerableButton.isSelected()) {
            // Show only vulnerable items with special highlighting
            sorter.setRowFilter(RowFilter.regexFilter("Potentially Vulnerable", 3));
            
            // Apply special styling to the button when active
            showOnlyVulnerableButton.setBackground(new Color(255, 235, 235)); // Light red
            showOnlyVulnerableButton.setForeground(new Color(204, 0, 0)); // Dark red
            showOnlyVulnerableButton.setToolTipText("Click to show all results");
            
            // Update result count on the button label
            int vulnCount = tableModel.getStatistics().getVulnerableCount();
            showOnlyVulnerableButton.setText("Showing " + vulnCount + " Vulnerable Parameters");
        } else {
            // Reset the filter to show all results
            sorter.setRowFilter(null);
            
            // Reset button styling
            showOnlyVulnerableButton.setBackground(null); // Default background
            showOnlyVulnerableButton.setForeground(null); // Default text color
            showOnlyVulnerableButton.setToolTipText("Click to show only potentially vulnerable parameters");
            showOnlyVulnerableButton.setText("Show Only Vulnerable");
        }
        
        // Update UI to reflect the changes
        resultsTable.repaint();
        
        // Show a status message with count information
        int totalVisible = resultsTable.getRowCount();
        int totalResults = tableModel.getRowCount();
        
        if (totalVisible != totalResults) {
            String statusText = "Showing " + totalVisible + " of " + totalResults + " results";
            statusLabel.setText(statusText);
        } else {
            statusLabel.setText("Showing all " + totalResults + " results");
        }
    }
    
    /**
     * Export the results to a file
     */
    /**
     * Export scan results to a file with multiple format options
     */
    private void exportResults() {
        if (tableModel.getRowCount() == 0) {
            JOptionPane.showMessageDialog(this, 
                "No results to export. Run a scan first.", 
                "No Results", 
                JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        // Create a popup menu with export format options
        JPopupMenu exportMenu = new JPopupMenu();
        exportMenu.setBorder(BorderFactory.createTitledBorder("Export Format"));
        
        // CSV export option
        JMenuItem csvItem = new JMenuItem("CSV (Comma Separated Values)");
        csvItem.setToolTipText("Export as CSV file for spreadsheet applications");
        
        // Try to add CSV icon
        try {
            Icon csvIcon = UIManager.getIcon("FileView.fileIcon");
            if (csvIcon != null) {
                csvItem.setIcon(csvIcon);
            }
        } catch (Exception ex) {
            // Ignore icon errors
        }
        
        csvItem.addActionListener(e -> exportAsCSV());
        
        // HTML report option
        JMenuItem htmlItem = new JMenuItem("HTML Report");
        htmlItem.setToolTipText("Export as formatted HTML report");
        
        // Try to add HTML icon
        try {
            Icon htmlIcon = UIManager.getIcon("FileView.computerIcon");
            if (htmlIcon != null) {
                htmlItem.setIcon(htmlIcon);
            }
        } catch (Exception ex) {
            // Ignore icon errors
        }
        
        htmlItem.addActionListener(e -> exportAsHTML());
        
        // XML option
        JMenuItem xmlItem = new JMenuItem("XML");
        xmlItem.setToolTipText("Export as XML for integration with other tools");
        
        // Try to add XML icon
        try {
            Icon xmlIcon = UIManager.getIcon("Tree.leafIcon");
            if (xmlIcon != null) {
                xmlItem.setIcon(xmlIcon);
            }
        } catch (Exception ex) {
            // Ignore icon errors
        }
        
        xmlItem.addActionListener(e -> exportAsXML());
        
        // Add options to menu
        exportMenu.add(csvItem);
        exportMenu.add(htmlItem);
        exportMenu.add(xmlItem);
        
        // Show the menu near the export button
        exportMenu.show(exportButton, 0, exportButton.getHeight());
    }
    
    /**
     * Export results as CSV file
     */
    private void exportAsCSV() {
        List<TestResult> results = tableModel.getAllResults();
        ResultStatistics stats = tableModel.getStatistics();
        
        // In a real implementation, this would use a file chooser
        // For now, create the CSV content and print a sample
        StringBuilder csv = new StringBuilder();
        
        // Add CSV header
        csv.append("Parameter Name,Parameter Type,URL,Method,Vulnerable,Baseline (ms),Time with Payload (ms),Delay Factor\n");
        
        // Add data rows
        for (TestResult result : results) {
            csv.append(escapeCSV(result.getParameterName())).append(",");
            csv.append(escapeCSV(result.getParameterType())).append(",");
            csv.append(escapeCSV(result.getUrl())).append(",");
            csv.append(escapeCSV(result.getMethod())).append(",");
            csv.append(result.isVulnerable() ? "Yes" : "No").append(",");
            csv.append(result.getBaselineTime()).append(",");
            csv.append(result.getTimeWithPayload()).append(",");
            csv.append(String.format("%.2f", result.getDelayFactor())).append("\n");
        }
        
        // Log sample and info
        callbacks.printOutput("=== Time-Based Scanner CSV Export ===");
        callbacks.printOutput("Scan completed at: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        callbacks.printOutput("Total Parameters Tested: " + stats.getTotalTests());
        callbacks.printOutput("Potentially Vulnerable Parameters: " + stats.getVulnerableCount());
        callbacks.printOutput("\nSample of CSV export (first 5 rows):");
        
        // Display the header and up to 5 rows
        String[] lines = csv.toString().split("\n");
        int displayLines = Math.min(lines.length, 6); // Header + 5 rows
        for (int i = 0; i < displayLines; i++) {
            callbacks.printOutput(lines[i]);
        }
        
        if (lines.length > displayLines) {
            callbacks.printOutput("... (more rows not shown)");
        }
        
        callbacks.printOutput("\nIn a complete implementation, this would save to a CSV file using a file chooser dialog.");
    }
    
    /**
     * Export results as HTML report
     */
    private void exportAsHTML() {
        List<TestResult> results = tableModel.getAllResults();
        List<TestResult> vulnerable = tableModel.getVulnerableResults();
        ResultStatistics stats = tableModel.getStatistics();
        
        // Create HTML content
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n<html>\n<head>\n");
        html.append("<title>Time-Based Scanner Report</title>\n");
        html.append("<style>\n");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }\n");
        html.append("h1 { color: #0066cc; }\n");
        html.append("h2 { color: #333; margin-top: 20px; }\n");
        html.append("table { border-collapse: collapse; width: 100%; margin-top: 10px; }\n");
        html.append("th { background-color: #f2f2f2; text-align: left; padding: 8px; border: 1px solid #ddd; }\n");
        html.append("td { padding: 8px; border: 1px solid #ddd; }\n");
        html.append("tr:nth-child(even) { background-color: #f9f9f9; }\n");
        html.append(".vulnerable { color: #cc0000; font-weight: bold; }\n");
        html.append(".safe { color: #009933; }\n");
        html.append(".summary-box { background-color: #f5f5f5; border: 1px solid #ddd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n");
        html.append("</style>\n");
        html.append("</head>\n<body>\n");
        
        // Report header
        html.append("<h1>Time-Based Vulnerability Scan Report</h1>\n");
        html.append("<p>Report generated on: ").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</p>\n");
        
        // Summary section
        html.append("<div class='summary-box'>\n");
        html.append("<h2>Scan Summary</h2>\n");
        html.append("<table>\n");
        html.append("<tr><td><strong>Total Parameters Tested:</strong></td><td>").append(stats.getTotalTests()).append("</td></tr>\n");
        html.append("<tr><td><strong>Potentially Vulnerable Parameters:</strong></td><td");
        if (stats.getVulnerableCount() > 0) {
            html.append(" class='vulnerable'");
        }
        html.append(">").append(stats.getVulnerableCount());
        if (stats.getVulnerableCount() > 0 && stats.getTotalTests() > 0) {
            html.append(" (").append(String.format("%.1f", stats.getVulnerablePercentage())).append("%)");
        }
        html.append("</td></tr>\n");
        html.append("<tr><td><strong>Average Response Time:</strong></td><td>").append(String.format("%.2f ms", stats.getAverageResponseTime())).append("</td></tr>\n");
        html.append("</table>\n");
        html.append("</div>\n");
        
        // Vulnerable parameters section
        if (!vulnerable.isEmpty()) {
            html.append("<h2>Potentially Vulnerable Parameters</h2>\n");
            html.append("<table>\n");
            html.append("<tr><th>Parameter</th><th>Type</th><th>URL</th><th>Method</th><th>Delay Factor</th></tr>\n");
            
            for (TestResult result : vulnerable) {
                html.append("<tr class='vulnerable'>\n");
                html.append("<td>").append(escapeHTML(result.getParameterName())).append("</td>\n");
                html.append("<td>").append(escapeHTML(result.getParameterType())).append("</td>\n");
                html.append("<td>").append(escapeHTML(result.getUrl())).append("</td>\n");
                html.append("<td>").append(escapeHTML(result.getMethod())).append("</td>\n");
                html.append("<td>").append(String.format("%.2f", result.getDelayFactor())).append("</td>\n");
                html.append("</tr>\n");
            }
            
            html.append("</table>\n");
        }
        
        // All parameters section
        html.append("<h2>All Tested Parameters</h2>\n");
        html.append("<table>\n");
        html.append("<tr><th>Parameter</th><th>Type</th><th>URL</th><th>Method</th><th>Status</th><th>Baseline (ms)</th><th>Time with Payload (ms)</th><th>Delay Factor</th></tr>\n");
        
        for (TestResult result : results) {
            html.append("<tr");
            if (result.isVulnerable()) {
                html.append(" class='vulnerable'");
            }
            html.append(">\n");
            html.append("<td>").append(escapeHTML(result.getParameterName())).append("</td>\n");
            html.append("<td>").append(escapeHTML(result.getParameterType())).append("</td>\n");
            html.append("<td>").append(escapeHTML(result.getUrl())).append("</td>\n");
            html.append("<td>").append(escapeHTML(result.getMethod())).append("</td>\n");
            html.append("<td>").append(result.isVulnerable() ? "<span class='vulnerable'>Vulnerable</span>" : "<span class='safe'>Not Vulnerable</span>").append("</td>\n");
            html.append("<td>").append(result.getBaselineTime()).append("</td>\n");
            html.append("<td>").append(result.getTimeWithPayload()).append("</td>\n");
            html.append("<td>").append(String.format("%.2f", result.getDelayFactor())).append("</td>\n");
            html.append("</tr>\n");
        }
        
        html.append("</table>\n");
        html.append("</body>\n</html>");
        
        // Log information about the export
        callbacks.printOutput("=== Time-Based Scanner HTML Export ===");
        callbacks.printOutput("Scan completed at: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        callbacks.printOutput("HTML report generated with " + results.size() + " results and " + vulnerable.size() + " potentially vulnerable parameters.");
        callbacks.printOutput("In a complete implementation, this would save to an HTML file using a file chooser dialog.");
    }
    
    /**
     * Export results as XML file
     */
    private void exportAsXML() {
        List<TestResult> results = tableModel.getAllResults();
        ResultStatistics stats = tableModel.getStatistics();
        
        // Create XML content
        StringBuilder xml = new StringBuilder();
        xml.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xml.append("<time-based-scan-results>\n");
        
        // Add scan metadata
        xml.append("  <metadata>\n");
        xml.append("    <timestamp>").append(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").format(new Date())).append("</timestamp>\n");
        xml.append("    <summary>\n");
        xml.append("      <total-parameters>").append(stats.getTotalTests()).append("</total-parameters>\n");
        xml.append("      <vulnerable-parameters>").append(stats.getVulnerableCount()).append("</vulnerable-parameters>\n");
        xml.append("      <average-response-time>").append(String.format("%.2f", stats.getAverageResponseTime())).append("</average-response-time>\n");
        xml.append("    </summary>\n");
        xml.append("  </metadata>\n");
        
        // Add results
        xml.append("  <results>\n");
        for (TestResult result : results) {
            xml.append("    <result>\n");
            xml.append("      <parameter>").append(escapeXML(result.getParameterName())).append("</parameter>\n");
            xml.append("      <type>").append(escapeXML(result.getParameterType())).append("</type>\n");
            xml.append("      <url>").append(escapeXML(result.getUrl())).append("</url>\n");
            xml.append("      <method>").append(escapeXML(result.getMethod())).append("</method>\n");
            xml.append("      <vulnerable>").append(result.isVulnerable()).append("</vulnerable>\n");
            xml.append("      <baseline-time>").append(result.getBaselineTime()).append("</baseline-time>\n");
            xml.append("      <time-with-payload>").append(result.getTimeWithPayload()).append("</time-with-payload>\n");
            xml.append("      <delay-factor>").append(String.format("%.2f", result.getDelayFactor())).append("</delay-factor>\n");
            xml.append("      <evidence>").append(escapeXML(result.getEvidence())).append("</evidence>\n");
            xml.append("    </result>\n");
        }
        xml.append("  </results>\n");
        xml.append("</time-based-scan-results>");
        
        // Log information about the export
        callbacks.printOutput("=== Time-Based Scanner XML Export ===");
        callbacks.printOutput("Scan completed at: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        callbacks.printOutput("XML report generated with " + results.size() + " results.");
        callbacks.printOutput("In a complete implementation, this would save to an XML file using a file chooser dialog.");
    }
    
    /**
     * Escape special characters for CSV format
     */
    private String escapeCSV(String input) {
        if (input == null) {
            return "";
        }
        
        // If the input contains quotes, commas, or newlines, it needs special handling
        if (input.contains("\"") || input.contains(",") || input.contains("\n")) {
            // Replace any quotes with double quotes
            input = input.replace("\"", "\"\"");
            // Wrap in quotes
            return "\"" + input + "\"";
        }
        
        return input;
    }
    
    /**
     * Escape special characters for HTML
     */
    private String escapeHTML(String input) {
        if (input == null) {
            return "";
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }
    
    /**
     * Escape special characters for XML
     */
    private String escapeXML(String input) {
        if (input == null) {
            return "";
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&apos;");
    }
    
    /**
     * Enhanced custom cell renderer for the Status column
     * Provides visual indicators of vulnerability status with colors and styling
     */
    private class StatusCellRenderer extends DefaultTableCellRenderer {
        // Cache the border styles for better performance
        private final Border normalBorder = BorderFactory.createEmptyBorder(4, 4, 4, 4);
        private final Border vulnerableBorder = BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(0, 0, 0, 3, new Color(204, 0, 0)),
            BorderFactory.createEmptyBorder(4, 4, 4, 4)
        );
        
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, 
                                                     boolean isSelected, boolean hasFocus, 
                                                     int row, int column) {
            
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
            
            // Default to clean styling
            setHorizontalAlignment(SwingConstants.CENTER);
            setBorder(normalBorder);
            
            // Style based on status value
            if (value != null) {
                String status = value.toString();
                
                if ("Potentially Vulnerable".equals(status)) {
                    // Enhanced visual styling for vulnerable parameters
                    if (!isSelected) {
                        // Use gradient paint background for better visual appeal
                        c.setBackground(new Color(255, 240, 240)); // Softer light red
                        c.setForeground(new Color(180, 0, 0)); // Slightly darker red
                    } else {
                        c.setForeground(new Color(255, 220, 220)); // Light red for selected text
                    }
                    setFont(getFont().deriveFont(Font.BOLD));
                    setText("⚠ VULNERABLE");
                    
                    // Add a custom border with red outline - only one border setting
                    setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createMatteBorder(1, 1, 1, 1, new Color(255, 150, 150)),
                        BorderFactory.createEmptyBorder(3, 3, 3, 3)
                    ));
                    
                } else if ("Indeterminate".equals(status)) {
                    // Yellow/orange for unclear results
                    if (!isSelected) {
                        c.setBackground(new Color(255, 252, 204)); // Light yellow
                        c.setForeground(new Color(204, 153, 0)); // Dark orange
                    } else {
                        c.setForeground(new Color(255, 204, 0)); // Brighter orange for selected
                    }
                    setFont(getFont().deriveFont(Font.PLAIN));
                    setText("? " + status);
                    
                } else if ("Not Vulnerable".equals(status)) {
                    // Green for secure parameters
                    if (!isSelected) {
                        c.setBackground(new Color(240, 255, 240)); // Very light green
                        c.setForeground(new Color(0, 153, 0)); // Dark green
                    } else {
                        c.setForeground(new Color(200, 255, 200)); // Light green for selected
                    }
                    setFont(getFont().deriveFont(Font.PLAIN));
                    setText("✓ " + status);
                    
                } else {
                    // Default styling for other statuses
                    if (!isSelected) {
                        c.setBackground(isSelected ? table.getSelectionBackground() : 
                            (row % 2 == 0 ? Color.WHITE : new Color(245, 245, 250)));
                        c.setForeground(table.getForeground());
                    }
                    setFont(getFont().deriveFont(Font.PLAIN));
                }
            } else {
                // Null values get default styling
                if (!isSelected) {
                    c.setBackground(isSelected ? table.getSelectionBackground() : 
                        (row % 2 == 0 ? Color.WHITE : new Color(245, 245, 250)));
                    c.setForeground(table.getForeground());
                }
                setFont(getFont().deriveFont(Font.PLAIN));
            }
            
            return c;
        }
    }

    /**
     * Custom cell renderer for SQL error column
     */
    private class SqlErrorCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                                                     boolean hasFocus, int row, int column) {
            super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            String sqlErrorText = value != null ? value.toString() : "No SQL Error";
            setText(sqlErrorText);

            // Set colors based on SQL error status
            if (!isSelected) {
                if (sqlErrorText.startsWith("SQL Error Detected")) {
                    setBackground(new Color(255, 240, 240)); // Light red background
                    setForeground(new Color(139, 0, 0)); // Dark red text
                    setFont(getFont().deriveFont(Font.BOLD));
                } else {
                    setBackground(Color.WHITE);
                    setForeground(Color.GRAY);
                    setFont(getFont().deriveFont(Font.PLAIN));
                }
            } else {
                // Keep default selection colors when selected
                setBackground(table.getSelectionBackground());
                setForeground(table.getSelectionForeground());
            }

            // Set tooltip with full SQL error information
            if (sqlErrorText.startsWith("SQL Error Detected")) {
                setToolTipText("SQL Error Detected! This indicates a potential SQL injection vulnerability. Details: " + sqlErrorText);
            } else {
                setToolTipText("No SQL error detected in response");
            }

            return this;
        }
    }


}
