package com.timebasedscan.utils;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Advanced rate limiting bypass techniques for stealth scanning
 */
public class RateLimitBypass {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final Random random;
    
    // Rate limiting bypass configuration
    private boolean enabled;
    private String bypassMethod;
    private int baseDelay;
    private boolean randomizeUserAgents;
    private boolean randomizeXForwardedFor;
    private boolean useProxyRotation;
    
    // State tracking
    private long lastRequestTime;
    private int consecutiveRequests;
    private int currentDelayMultiplier;
    private List<String> userAgents;
    
    public RateLimitBypass(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.random = new Random();
        this.lastRequestTime = 0;
        this.consecutiveRequests = 0;
        this.currentDelayMultiplier = 1;
        
        initializeUserAgents();
    }
    
    /**
     * Configure rate limiting bypass settings
     */
    public void configure(boolean enabled, String bypassMethod, int baseDelay,
                         boolean randomizeUserAgents, boolean randomizeXForwardedFor,
                         boolean useProxyRotation) {
        this.enabled = enabled;
        this.bypassMethod = bypassMethod;
        this.baseDelay = baseDelay;
        this.randomizeUserAgents = randomizeUserAgents;
        this.randomizeXForwardedFor = randomizeXForwardedFor;
        this.useProxyRotation = useProxyRotation;
        
        callbacks.printOutput("Rate Limiting Bypass configured: " + 
                            (enabled ? "ENABLED" : "DISABLED") + 
                            " | Method: " + bypassMethod + 
                            " | Base Delay: " + baseDelay + "ms");
    }
    
    /**
     * Calculate the delay before the next request based on the selected bypass method
     */
    public long calculateDelay() {
        if (!enabled) {
            return 0;
        }
        
        long delay = 0;
        
        switch (bypassMethod) {
            case "Adaptive Delay (Recommended)":
                delay = calculateAdaptiveDelay();
                break;
            case "Random Jitter":
                delay = calculateRandomJitterDelay();
                break;
            case "Exponential Backoff":
                delay = calculateExponentialBackoffDelay();
                break;
            case "Burst with Cooldown":
                delay = calculateBurstCooldownDelay();
                break;
            case "Time-Window Distribution":
                delay = calculateTimeWindowDelay();
                break;
            case "Custom Pattern":
                delay = calculateCustomPatternDelay();
                break;
            default:
                delay = baseDelay;
        }
        
        callbacks.printOutput("Rate Limiting Bypass: Calculated delay = " + delay + "ms (Method: " + bypassMethod + ")");
        return delay;
    }
    
    /**
     * Adaptive delay that adjusts based on request patterns
     */
    private long calculateAdaptiveDelay() {
        consecutiveRequests++;
        
        // Base delay with adaptive scaling
        long adaptiveDelay = baseDelay;
        
        // Increase delay for consecutive requests
        if (consecutiveRequests > 5) {
            adaptiveDelay *= 1.5;
        }
        if (consecutiveRequests > 10) {
            adaptiveDelay *= 2.0;
        }
        
        // Add random jitter (±25%)
        double jitterFactor = 0.75 + (random.nextDouble() * 0.5);
        adaptiveDelay = (long) (adaptiveDelay * jitterFactor);
        
        return Math.max(adaptiveDelay, 100); // Minimum 100ms
    }
    
    /**
     * Random jitter delay with controlled variance
     */
    private long calculateRandomJitterDelay() {
        // Random delay between 50% and 150% of base delay
        double jitterFactor = 0.5 + (random.nextDouble() * 1.0);
        return (long) (baseDelay * jitterFactor);
    }
    
    /**
     * Exponential backoff delay
     */
    private long calculateExponentialBackoffDelay() {
        consecutiveRequests++;
        
        // Exponential backoff: delay = baseDelay * 2^(requests/5)
        int backoffFactor = Math.min(consecutiveRequests / 5, 4); // Cap at 2^4 = 16x
        long exponentialDelay = baseDelay * (1L << backoffFactor);
        
        // Add some randomness
        double jitterFactor = 0.8 + (random.nextDouble() * 0.4);
        return (long) (exponentialDelay * jitterFactor);
    }
    
    /**
     * Burst with cooldown delay
     */
    private long calculateBurstCooldownDelay() {
        consecutiveRequests++;
        
        // Allow bursts of 3-5 requests with minimal delay, then longer cooldown
        int burstSize = 3 + random.nextInt(3); // 3-5 requests
        
        if (consecutiveRequests % burstSize == 0) {
            // Cooldown period after burst
            return baseDelay * 3 + random.nextInt(baseDelay * 2);
        } else {
            // Minimal delay during burst
            return 100 + random.nextInt(200);
        }
    }
    
    /**
     * Time-window distribution delay
     */
    private long calculateTimeWindowDelay() {
        // Distribute requests evenly across a time window
        long timeWindow = 60000; // 1 minute window
        int maxRequestsPerWindow = 10;
        
        long currentTime = System.currentTimeMillis();
        long timeSinceLastRequest = currentTime - lastRequestTime;
        
        // Calculate ideal spacing
        long idealSpacing = timeWindow / maxRequestsPerWindow;
        
        if (timeSinceLastRequest < idealSpacing) {
            return idealSpacing - timeSinceLastRequest + random.nextInt(500);
        }
        
        return random.nextInt(1000); // Small random delay
    }
    
    /**
     * Custom pattern delay (mimics human behavior)
     */
    private long calculateCustomPatternDelay() {
        consecutiveRequests++;
        
        // Simulate human-like patterns
        if (consecutiveRequests % 7 == 0) {
            // Occasional longer pause (thinking time)
            return baseDelay * 4 + random.nextInt(baseDelay * 3);
        } else if (consecutiveRequests % 3 == 0) {
            // Medium pause
            return baseDelay * 2 + random.nextInt(baseDelay);
        } else {
            // Short pause with variation
            return baseDelay / 2 + random.nextInt(baseDelay);
        }
    }
    
    /**
     * Apply rate limiting bypass delay
     */
    public void applyDelay() {
        if (!enabled) {
            return;
        }
        
        long delay = calculateDelay();
        if (delay > 0) {
            try {
                callbacks.printOutput("Rate Limiting Bypass: Applying delay of " + delay + "ms...");
                Thread.sleep(delay);
                lastRequestTime = System.currentTimeMillis();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                callbacks.printError("Rate limiting delay interrupted: " + e.getMessage());
            }
        }
    }
    
    /**
     * Get a randomized User-Agent header
     */
    public String getRandomUserAgent() {
        if (!enabled || !randomizeUserAgents || userAgents.isEmpty()) {
            return null;
        }
        
        return userAgents.get(random.nextInt(userAgents.size()));
    }
    
    /**
     * Get a randomized X-Forwarded-For header
     */
    public String getRandomXForwardedFor() {
        if (!enabled || !randomizeXForwardedFor) {
            return null;
        }
        
        // Generate random IP address
        return String.format("%d.%d.%d.%d",
            1 + random.nextInt(254),
            1 + random.nextInt(254),
            1 + random.nextInt(254),
            1 + random.nextInt(254)
        );
    }
    
    /**
     * Reset consecutive request counter (call when switching targets)
     */
    public void resetRequestCounter() {
        consecutiveRequests = 0;
        currentDelayMultiplier = 1;
        callbacks.printOutput("Rate Limiting Bypass: Request counter reset");
    }
    
    /**
     * Initialize common User-Agent strings
     */
    private void initializeUserAgents() {
        userAgents = new ArrayList<>();
        
        // Modern browsers
        userAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        userAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0");
        userAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        userAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15");
        userAgents.add("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        userAgents.add("Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0");
        
        // Mobile browsers
        userAgents.add("Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1");
        userAgents.add("Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0");
        userAgents.add("Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36");
        
        // Edge and other browsers
        userAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0");
        userAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********");
    }
    
    /**
     * Check if rate limiting bypass is enabled
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Get current bypass method
     */
    public String getBypassMethod() {
        return bypassMethod;
    }
    
    /**
     * Get base delay
     */
    public int getBaseDelay() {
        return baseDelay;
    }
}
