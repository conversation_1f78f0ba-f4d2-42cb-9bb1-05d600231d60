package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IMessageEditor;
import burp.IMessageEditorController;

import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.scanner.ParallelScanner;
import com.timebasedscan.utils.HttpParameterParser;


import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Panel for mass testing multiple requests with the same configuration
 */
public class MassTestingPanel extends JPanel implements IMessageEditorController {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private MainPanel mainPanel;
    
    private JTable requestsTable;
    private DefaultTableModel requestsTableModel;
    private IMessageEditor requestViewer;
    private JPanel configPanel;
    private JButton scanButton;
    private JComboBox<String> payloadComboBox;
    private JTextField customPayloadField;
    private JSpinner concurrencySpinner;
    private JSpinner timeoutSpinner;
    private JCheckBox includeParameterNamesCheckBox;
    private JCheckBox includeUrlPathInjectionCheckBox;

    // Multi-payload testing components
    private JCheckBox multiPayloadCheckbox;
    private JList<String> payloadList;
    private DefaultListModel<String> payloadListModel;
    private JButton addPayloadButton;
    private JButton removePayloadButton;
    private JTextField customPayloadInputField;

    // SQL error detection components
    private JCheckBox enableSqlErrorDetectionCheckbox;
    private JCheckBox showSqlErrorsInResultsCheckbox;

    // Enhanced parameter filtering components (header parameters only - others already exist)
    private JCheckBox includeHeaderParametersCheckBox;
    
    private List<IHttpRequestResponse> loadedRequests;
    private Map<IHttpRequestResponse, List<HttpParameter>> requestParametersMap;
    private Map<IHttpRequestResponse, Boolean> requestSelectionMap;
    private Map<IHttpRequestResponse, Map<HttpParameter, Boolean>> parameterSelectionMap;
    private IHttpRequestResponse currentlyDisplayedRequest;
    
    private static final String[] COLUMN_NAMES = new String[] {
        "Selected", "Host", "URL", "Method", "Parameters", "Status"
    };
    
    public MassTestingPanel(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, MainPanel mainPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.mainPanel = mainPanel;
        this.loadedRequests = new ArrayList<>();
        this.requestParametersMap = new HashMap<>();
        this.requestSelectionMap = new HashMap<>();
        this.parameterSelectionMap = new HashMap<>();
        
        initializeUI();
    }
    
    private void initializeUI() {
        setLayout(new BorderLayout());

        // Create main content area with better space management (no configuration panel)
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        mainSplitPane.setResizeWeight(0.7); // Give more space to the left side

        // Left side - Requests table and viewer
        JSplitPane leftSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        leftSplitPane.setResizeWeight(0.6); // More space for requests table

        JPanel requestsTablePanel = createRequestsTablePanel();
        JPanel requestViewerPanel = createRequestViewerPanel();

        leftSplitPane.setTopComponent(requestsTablePanel);
        leftSplitPane.setBottomComponent(requestViewerPanel);
        leftSplitPane.setDividerLocation(300); // More space for requests

        // Right side - Parameter selection (more compact)
        JPanel parameterPanel = createCompactParameterSelectionPanel();

        mainSplitPane.setLeftComponent(leftSplitPane);
        mainSplitPane.setRightComponent(parameterPanel);
        mainSplitPane.setDividerLocation(700); // More space for requests

        // Add info panel at top
        JPanel infoPanel = createInfoPanel();

        // Add components to main panel
        add(infoPanel, BorderLayout.NORTH);
        add(mainSplitPane, BorderLayout.CENTER);
    }
    
    private JPanel createRequestsTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Loaded Requests"));
        
        // Create requests table model
        requestsTableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public Class<?> getColumnClass(int column) {
                if (column == 0) {
                    return Boolean.class;
                }
                return String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // Only selection column is editable
            }
        };
        
        // Create requests table with enhanced styling
        requestsTable = new JTable(requestsTableModel);
        requestsTable.getColumnModel().getColumn(0).setMaxWidth(60);
        requestsTable.getColumnModel().getColumn(0).setMinWidth(60);
        requestsTable.getColumnModel().getColumn(4).setMaxWidth(100);
        requestsTable.getColumnModel().getColumn(4).setMinWidth(80);
        requestsTable.getColumnModel().getColumn(5).setMaxWidth(100);
        requestsTable.getColumnModel().getColumn(5).setMinWidth(80);
        requestsTable.setRowHeight(24); // Taller rows for better readability
        requestsTable.setShowGrid(true);
        requestsTable.setGridColor(new Color(230, 230, 230));
        requestsTable.getTableHeader().setReorderingAllowed(false);

        // Add alternating row colors
        requestsTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                if (!isSelected) {
                    if (row % 2 == 0) {
                        c.setBackground(new Color(248, 248, 248));
                    } else {
                        c.setBackground(Color.WHITE);
                    }
                }
                return c;
            }
        });
        
        // Add selection listener to show request in viewer
        requestsTable.getSelectionModel().addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = requestsTable.getSelectedRow();
                    if (selectedRow >= 0 && selectedRow < loadedRequests.size()) {
                        currentlyDisplayedRequest = loadedRequests.get(selectedRow);
                        requestViewer.setMessage(currentlyDisplayedRequest.getRequest(), true);
                    }
                }
            }
        });
        
        // Create tools panel with buttons
        JPanel toolsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        JButton addButton = new JButton("Add Current Request");
        addButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                IHttpRequestResponse currentRequest = mainPanel.getCurrentRequest();
                if (currentRequest != null) {
                    addRequest(currentRequest);
                } else {
                    JOptionPane.showMessageDialog(MassTestingPanel.this, 
                        "No request currently loaded. Load a request first in the main scanner tab.",
                        "No Request Loaded", JOptionPane.WARNING_MESSAGE);
                }
            }
        });
        toolsPanel.add(addButton);
        
        JButton removeButton = new JButton("Remove Selected");
        removeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = requestsTable.getSelectedRow();
                if (selectedRow >= 0) {
                    IHttpRequestResponse requestToRemove = loadedRequests.get(selectedRow);
                    loadedRequests.remove(selectedRow);
                    requestParametersMap.remove(requestToRemove);
                    requestSelectionMap.remove(requestToRemove);
                    requestsTableModel.removeRow(selectedRow);
                }
            }
        });
        toolsPanel.add(removeButton);
        
        JButton selectAllButton = new JButton("Select All");
        selectAllButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                for (int i = 0; i < requestsTableModel.getRowCount(); i++) {
                    requestsTableModel.setValueAt(Boolean.TRUE, i, 0);
                    requestSelectionMap.put(loadedRequests.get(i), Boolean.TRUE);
                }
                // Force table refresh
                requestsTable.revalidate();
                requestsTable.repaint();
            }
        });
        toolsPanel.add(selectAllButton);
        
        JButton deselectAllButton = new JButton("Deselect All");
        deselectAllButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                for (int i = 0; i < requestsTableModel.getRowCount(); i++) {
                    requestsTableModel.setValueAt(Boolean.FALSE, i, 0);
                    requestSelectionMap.put(loadedRequests.get(i), Boolean.FALSE);
                }
                // Force table refresh
                requestsTable.revalidate();
                requestsTable.repaint();
            }
        });
        toolsPanel.add(deselectAllButton);
        
        JButton clearButton = new JButton("Clear All");
        clearButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                loadedRequests.clear();
                requestParametersMap.clear();
                requestSelectionMap.clear();
                while (requestsTableModel.getRowCount() > 0) {
                    requestsTableModel.removeRow(0);
                }
            }
        });
        toolsPanel.add(clearButton);
        
        // Add components to panel
        panel.add(new JScrollPane(requestsTable), BorderLayout.CENTER);
        panel.add(toolsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createRequestViewerPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Request Details"));

        // Just the request viewer - parameter selection moved to right panel
        requestViewer = callbacks.createMessageEditor(this, true);
        panel.add(requestViewer.getComponent(), BorderLayout.CENTER);

        return panel;
    }
    
    private JPanel createParameterSelectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Parameter Selection"));
        panel.setPreferredSize(new Dimension(450, 400)); // Set preferred size for better layout

        // Create parameter selection options with better layout
        JPanel optionsPanel = new JPanel();
        optionsPanel.setLayout(new BoxLayout(optionsPanel, BoxLayout.Y_AXIS));
        optionsPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // Add title for selection mode
        JLabel selectionModeLabel = new JLabel("Selection Mode:");
        selectionModeLabel.setFont(selectionModeLabel.getFont().deriveFont(Font.BOLD, 12f));
        selectionModeLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        optionsPanel.add(selectionModeLabel);
        optionsPanel.add(Box.createVerticalStrut(5));

        JRadioButton allParamsRadio = new JRadioButton("Test All Parameters");
        JRadioButton selectedParamsRadio = new JRadioButton("Test Selected Parameters");
        JRadioButton queryParamsRadio = new JRadioButton("Test Query Parameters Only");
        JRadioButton bodyParamsRadio = new JRadioButton("Test Body Parameters Only");
        JRadioButton cookieParamsRadio = new JRadioButton("Test Cookie Parameters Only");

        // Set alignment for radio buttons
        allParamsRadio.setAlignmentX(Component.LEFT_ALIGNMENT);
        selectedParamsRadio.setAlignmentX(Component.LEFT_ALIGNMENT);
        queryParamsRadio.setAlignmentX(Component.LEFT_ALIGNMENT);
        bodyParamsRadio.setAlignmentX(Component.LEFT_ALIGNMENT);
        cookieParamsRadio.setAlignmentX(Component.LEFT_ALIGNMENT);
        
        // Group radio buttons
        ButtonGroup paramGroup = new ButtonGroup();
        paramGroup.add(allParamsRadio);
        paramGroup.add(selectedParamsRadio);
        paramGroup.add(queryParamsRadio);
        paramGroup.add(bodyParamsRadio);
        paramGroup.add(cookieParamsRadio);

        // Set default selection
        allParamsRadio.setSelected(true);

        // Add to panel with spacing
        optionsPanel.add(allParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(3));
        optionsPanel.add(selectedParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(3));
        optionsPanel.add(queryParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(3));
        optionsPanel.add(bodyParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(3));
        optionsPanel.add(cookieParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(10));
        
        // Add separator and title for parameter table
        JLabel parameterTableLabel = new JLabel("Individual Parameter Selection:");
        parameterTableLabel.setFont(parameterTableLabel.getFont().deriveFont(Font.BOLD, 12f));
        parameterTableLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        optionsPanel.add(parameterTableLabel);
        optionsPanel.add(Box.createVerticalStrut(5));

        // Create enhanced table for individual parameter selection with vulnerability assessment
        String[] columnNames = {"✓", "Parameter Name", "Type", "Value", "Empty", "Likelihood"};
        DefaultTableModel paramTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public Class<?> getColumnClass(int column) {
                if (column == 0) {
                    return Boolean.class;
                }
                return String.class;
            }

            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // Only selection column is editable
            }
        };

        JTable paramTable = new JTable(paramTableModel);
        paramTable.getColumnModel().getColumn(0).setMaxWidth(40);  // ✓ column
        paramTable.getColumnModel().getColumn(0).setMinWidth(40);
        paramTable.getColumnModel().getColumn(2).setMaxWidth(80);  // Type column
        paramTable.getColumnModel().getColumn(4).setMaxWidth(60);  // Empty column
        paramTable.getColumnModel().getColumn(5).setMaxWidth(100); // Likelihood column
        paramTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        paramTable.setRowHeight(22); // Slightly taller rows for better readability
        paramTable.setShowGrid(true);
        paramTable.setGridColor(new Color(230, 230, 230));
        
        // Remove any existing cell renderers
        
        // Single table model listener for checkbox handling
        paramTableModel.addTableModelListener(e -> {
            if (e.getColumn() == 0 && e.getFirstRow() >= 0 && currentlyDisplayedRequest != null) {
                int row = e.getFirstRow();
                Boolean selected = (Boolean) paramTableModel.getValueAt(row, 0);
                
                // Get the parameter for this row
                List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
                if (parameters != null && row < parameters.size()) {
                    HttpParameter param = parameters.get(row);
                    
                    // Update selection map
                    Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(currentlyDisplayedRequest);
                    if (paramSelections == null) {
                        paramSelections = new HashMap<>();
                        parameterSelectionMap.put(currentlyDisplayedRequest, paramSelections);
                    }
                    
                    // Set the selection status
                    paramSelections.put(param, selected);
                    
                    // Debug information
                    callbacks.printOutput("Parameter selection changed via model listener: " + param.getName() + " = " + selected);
                }
            }
        });
        
        JScrollPane scrollPane = new JScrollPane(paramTable);
        scrollPane.setPreferredSize(new Dimension(420, 180));
        scrollPane.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // Enable/disable table based on radio button selection
        selectedParamsRadio.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                paramTable.setEnabled(true);
                // Update visual appearance to show it's active
                paramTable.setBackground(new Color(255, 255, 220)); // Light yellow
            }
        });
        
        allParamsRadio.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                paramTable.setEnabled(false);
                paramTable.setBackground(UIManager.getColor("Table.background"));
            }
        });
        
        queryParamsRadio.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                paramTable.setEnabled(false);
                paramTable.setBackground(UIManager.getColor("Table.background"));
            }
        });
        
        bodyParamsRadio.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                paramTable.setEnabled(false);
                paramTable.setBackground(UIManager.getColor("Table.background"));
            }
        });
        
        cookieParamsRadio.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                paramTable.setEnabled(false);
                paramTable.setBackground(UIManager.getColor("Table.background"));
            }
        });
        
        // Initially disable table
        paramTable.setEnabled(false);
        
        // Add table model listener to handle checkbox changes
        paramTableModel.addTableModelListener(e -> {
            if (e.getColumn() == 0 && e.getFirstRow() >= 0 && paramTable.isEnabled() && currentlyDisplayedRequest != null) {
                int row = e.getFirstRow();
                Boolean selected = (Boolean) paramTableModel.getValueAt(row, 0);
                
                // Update parameter selection map
                List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
                if (parameters != null && row < parameters.size()) {
                    HttpParameter param = parameters.get(row);
                    Map<HttpParameter, Boolean> selections = parameterSelectionMap.get(currentlyDisplayedRequest);
                    if (selections == null) {
                        selections = new HashMap<>();
                        parameterSelectionMap.put(currentlyDisplayedRequest, selections);
                    }
                    selections.put(param, selected);
                    
                    // Debug - show message confirming selection change
                    callbacks.printOutput("Parameter selection state updated: " + param.getName() + " = " + selected);
                }
            }
        });
        
        // Add listeners to request table selection to update parameter table
        requestsTable.getSelectionModel().addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = requestsTable.getSelectedRow();
                    if (selectedRow >= 0 && selectedRow < loadedRequests.size()) {
                        IHttpRequestResponse selectedRequest = loadedRequests.get(selectedRow);
                        currentlyDisplayedRequest = selectedRequest;
                        requestViewer.setMessage(selectedRequest.getRequest(), true);
                        
                        // Update parameter table
                        updateParameterTable(paramTableModel, selectedRequest);
                    }
                }
            }
        });
        
        // Create selection button panel with better styling
        JPanel selectionButtonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
        selectionButtonPanel.setBorder(new EmptyBorder(5, 0, 5, 0));

        JButton selectAllParamsButton = new JButton("✓ Select All");
        selectAllParamsButton.setPreferredSize(new Dimension(100, 28));
        selectAllParamsButton.setToolTipText("Select all parameters for testing");
        selectAllParamsButton.addActionListener(e -> {
            if (currentlyDisplayedRequest != null) {
                List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
                if (parameters != null) {
                    Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(currentlyDisplayedRequest);
                    if (paramSelections == null) {
                        paramSelections = new HashMap<>();
                        parameterSelectionMap.put(currentlyDisplayedRequest, paramSelections);
                    }

                    // Set all to selected
                    for (int i = 0; i < parameters.size(); i++) {
                        HttpParameter param = parameters.get(i);
                        paramSelections.put(param, Boolean.TRUE);
                        paramTableModel.setValueAt(Boolean.TRUE, i, 0);
                    }
                    // Force table refresh
                    paramTable.revalidate();
                    paramTable.repaint();
                }
            }
        });

        JButton deselectAllParamsButton = new JButton("✗ Clear All");
        deselectAllParamsButton.setPreferredSize(new Dimension(100, 28));
        deselectAllParamsButton.setToolTipText("Deselect all parameters");
        deselectAllParamsButton.addActionListener(e -> {
            if (currentlyDisplayedRequest != null) {
                List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
                if (parameters != null) {
                    Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(currentlyDisplayedRequest);
                    if (paramSelections == null) {
                        paramSelections = new HashMap<>();
                        parameterSelectionMap.put(currentlyDisplayedRequest, paramSelections);
                    }

                    // Set all to deselected
                    for (int i = 0; i < parameters.size(); i++) {
                        HttpParameter param = parameters.get(i);
                        paramSelections.put(param, Boolean.FALSE);
                        paramTableModel.setValueAt(Boolean.FALSE, i, 0);
                    }
                    // Force table refresh
                    paramTable.revalidate();
                    paramTable.repaint();
                }
            }
        });

        // Add enhanced selection buttons
        JButton smartSelectButton = new JButton("🧠 Smart");
        smartSelectButton.setPreferredSize(new Dimension(80, 28));
        smartSelectButton.setToolTipText("Smart selection based on vulnerability likelihood");
        smartSelectButton.addActionListener(e -> bulkSelectParameters("smart"));

        JButton emptySelectButton = new JButton("🔍 Empty");
        emptySelectButton.setPreferredSize(new Dimension(80, 28));
        emptySelectButton.setToolTipText("Select only empty parameters");
        emptySelectButton.addActionListener(e -> bulkSelectParameters("empty"));

        JButton highRiskButton = new JButton("⚠️ High Risk");
        highRiskButton.setPreferredSize(new Dimension(100, 28));
        highRiskButton.setToolTipText("Select high-risk parameters");
        highRiskButton.addActionListener(e -> bulkSelectParameters("high-risk"));

        selectionButtonPanel.add(selectAllParamsButton);
        selectionButtonPanel.add(deselectAllParamsButton);
        selectionButtonPanel.add(new JSeparator(JSeparator.VERTICAL));
        selectionButtonPanel.add(smartSelectButton);
        selectionButtonPanel.add(emptySelectButton);
        selectionButtonPanel.add(highRiskButton);
        
        // Create main panel layout with better organization
        JPanel paramContentPanel = new JPanel(new BorderLayout());
        paramContentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
        paramContentPanel.add(scrollPane, BorderLayout.CENTER);
        paramContentPanel.add(selectionButtonPanel, BorderLayout.SOUTH);

        // Add to panel with proper layout
        panel.add(optionsPanel, BorderLayout.NORTH);
        panel.add(paramContentPanel, BorderLayout.CENTER);

        // Add a status panel at the bottom
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBorder(new EmptyBorder(5, 10, 5, 10));
        JLabel statusLabel = new JLabel("Select a request to view parameters");
        statusLabel.setFont(statusLabel.getFont().deriveFont(Font.ITALIC, 11f));
        statusLabel.setForeground(Color.GRAY);
        statusPanel.add(statusLabel);
        panel.add(statusPanel, BorderLayout.SOUTH);
        
        // Store references for access in other methods
        paramTable.setName("parameterTable");
        allParamsRadio.setName("allParamsRadio");
        selectedParamsRadio.setName("selectedParamsRadio");
        queryParamsRadio.setName("queryParamsRadio");
        bodyParamsRadio.setName("bodyParamsRadio");
        cookieParamsRadio.setName("cookieParamsRadio");
        
        return panel;
    }
    
    private void updateParameterTable(DefaultTableModel model, IHttpRequestResponse request) {
        // Clear existing rows
        while (model.getRowCount() > 0) {
            model.removeRow(0);
        }
        
        // Get parameters for this request
        List<HttpParameter> parameters = requestParametersMap.get(request);
        if (parameters != null) {
            Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(request);
            if (paramSelections == null) {
                // Create new selection map if none exists
                paramSelections = new HashMap<>();
                parameterSelectionMap.put(request, paramSelections);
                
                // Default all parameters to FALSE
                for (HttpParameter param : parameters) {
                    paramSelections.put(param, Boolean.FALSE);
                }
            }
            
            // Add parameters to table with their current selection state and vulnerability assessment
            for (HttpParameter param : parameters) {
                // Use existing selection state or default to FALSE for new parameters
                Boolean selected = paramSelections.getOrDefault(param, Boolean.FALSE);

                // Make sure the selection state is properly stored
                paramSelections.put(param, selected);

                // Assess vulnerability likelihood
                String isEmpty = param.isEmpty() ? "Yes" : "No";
                String likelihood = assessParameterVulnerabilityLikelihood(param);

                // Add parameter to the UI table with enhanced columns
                model.addRow(new Object[] {
                    selected,
                    param.getName(),
                    param.getType().toString(),
                    param.getValue(),
                    isEmpty,
                    likelihood
                });
            }
            
            // Print debug information about parameter selection counts
            int selectedCount = 0;
            for (Boolean isSelected : paramSelections.values()) {
                if (isSelected) selectedCount++;
            }
            callbacks.printOutput("Request has " + parameters.size() + " parameters, " + selectedCount + " selected");
        }
    }

    /**
     * Assess the vulnerability likelihood of a parameter based on its characteristics
     */
    private String assessParameterVulnerabilityLikelihood(HttpParameter param) {
        if (param == null) return "Unknown";

        String name = param.getName().toLowerCase();
        String type = param.getType().toString();
        boolean isEmpty = param.isEmpty();

        // High likelihood factors
        if (isEmpty) {
            return "High"; // Empty parameters often bypass validation
        }

        // Check for high-risk parameter names
        if (name.contains("id") || name.contains("user") || name.contains("search") ||
            name.contains("query") || name.contains("cmd") || name.contains("exec") ||
            name.contains("sql") || name.contains("db") || name.contains("admin")) {
            return "High";
        }

        // Check for high-risk parameter types
        if (type.equals("BODY") || type.equals("JSON")) {
            return "Medium-High";
        }

        if (type.equals("URL")) {
            return "Medium";
        }

        // Check for medium-risk parameter names
        if (name.contains("name") || name.contains("value") || name.contains("data") ||
            name.contains("input") || name.contains("param") || name.contains("field")) {
            return "Medium";
        }

        // Header parameters are generally lower risk
        if (type.equals("HEADER")) {
            return "Medium-Low";
        }

        // Cookie parameters
        if (type.equals("COOKIE")) {
            return "Low";
        }

        // Default for other parameters
        return "Low";
    }

    /**
     * Bulk parameter selection based on criteria
     */
    private void bulkSelectParameters(String selectionType) {
        if (currentlyDisplayedRequest == null) return;

        List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
        if (parameters == null) return;

        Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(currentlyDisplayedRequest);
        if (paramSelections == null) {
            paramSelections = new HashMap<>();
            parameterSelectionMap.put(currentlyDisplayedRequest, paramSelections);
        }

        // Get the parameter table model
        JTable paramTable = (JTable) findComponentByName(this, "parameterTable");
        if (paramTable == null) return;
        DefaultTableModel model = (DefaultTableModel) paramTable.getModel();

        int selectedCount = 0;

        for (int i = 0; i < parameters.size(); i++) {
            HttpParameter param = parameters.get(i);
            boolean shouldSelect = false;

            switch (selectionType.toLowerCase()) {
                case "all":
                    shouldSelect = true;
                    break;

                case "none":
                    shouldSelect = false;
                    break;

                case "smart":
                    // Smart selection: empty parameters first, then high-risk types and names
                    if (param.isEmpty()) {
                        shouldSelect = true;
                    } else {
                        String likelihood = assessParameterVulnerabilityLikelihood(param);
                        if (likelihood.equals("High") || likelihood.equals("Medium-High")) {
                            shouldSelect = true;
                        }
                    }
                    break;

                case "empty":
                    shouldSelect = param.isEmpty();
                    break;

                case "high-risk":
                    String likelihood = assessParameterVulnerabilityLikelihood(param);
                    shouldSelect = likelihood.equals("High") || likelihood.equals("Medium-High");
                    break;
            }

            paramSelections.put(param, shouldSelect);
            model.setValueAt(shouldSelect, i, 0);

            if (shouldSelect) selectedCount++;
        }

        callbacks.printOutput("Bulk selection (" + selectionType + "): " + selectedCount + "/" + parameters.size() + " parameters selected");
    }

    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Mass Testing"));
        panel.setPreferredSize(new Dimension(0, 60));

        JLabel infoLabel = new JLabel("Load requests here for mass testing. Configuration is done in the Scan Controls tab.");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.ITALIC, 12f));
        infoLabel.setForeground(Color.GRAY);
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));

        panel.add(infoLabel, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createCompactConfigPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Mass Testing Configuration"));
        panel.setPreferredSize(new Dimension(0, 120)); // Compact height

        // Create horizontal layout for compact design
        JPanel contentPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 5));

        // Payload selection - compact horizontal layout
        JLabel payloadLabel = new JLabel("Payload:");
        String[] commonPayloads = new String[] {
            "MSSQL (URL Encoded): %3bWAITFOR+DELAY+'00%3a00%3a5'--",
            "MSSQL (Space Encoded): ';%20waitfor%20delay%20'0:0:6'%20--%20",
            "PostgreSQL (Concat): ''||(select 1 from (select pg_sleep(6))x)||'",
            "MySQL (XOR Math): 0'XOR(if(now()=sysdate(),sleep(6-2),0))XOR'Z",
            "MySQL (IF Statement): ;SELECT IF((8303>8302),SLEEP(9),2356)#",
            "MySQL (XOR Sleep): 'XOR(if(now()=sysdate(),sleep(5*5),0))OR'",
            "MySQL (Simple XOR): XOR(if(now()=sysdate(),sleep(3),0))OR'",
            "MySQL (Complex): 14)%20AND%20(SELECT%207415%20FROM%20(SELECT(SLEEP(10)))CwkU)%20AND%20(7515=7515",
            "MySQL (URL Encoded): '+AND+(SELECT+2016+FROM+(SELECT(SLEEP(15)))SIfv)+AND+'vDZP'%3d'vDZP",
            "MySQL (OR Sleep):  ' AND '+OR+SLEEP(5)--+",
            "PostgreSQL (Comment):  '/**/and(select'1'from/**/pg_sleep(10))::text>'0",
            "MySQL (Original): 'XOR(if(now()=sysdate(),sleep(7),0))XOR'Z",
            "MSSQL (Original): ' WAITFOR DELAY '0:0:7'--"
        };
        payloadComboBox = new JComboBox<>(commonPayloads);
        payloadComboBox.setPreferredSize(new Dimension(300, 25));

        customPayloadField = new JTextField("%3bWAITFOR+DELAY+'00%3a00%3a5'--", 20);
        customPayloadField.setPreferredSize(new Dimension(250, 25));

        // Link dropdown to text field
        payloadComboBox.addActionListener(e -> {
            String selected = (String)payloadComboBox.getSelectedItem();
            if (selected != null && selected.contains(": ")) {
                customPayloadField.setText(selected.substring(selected.indexOf(": ") + 2));
            }
        });

        // Settings - compact horizontal layout
        JLabel concurrencyLabel = new JLabel("Parallel:");
        concurrencySpinner = new JSpinner(new SpinnerNumberModel(5, 1, 50, 1));
        concurrencySpinner.setPreferredSize(new Dimension(60, 25));

        JLabel timeoutLabel = new JLabel("Timeout:");
        timeoutSpinner = new JSpinner(new SpinnerNumberModel(10, 1, 60, 1));
        timeoutSpinner.setPreferredSize(new Dimension(60, 25));

        // Checkboxes - compact
        multiPayloadCheckbox = new JCheckBox("Multi-Payload");
        multiPayloadCheckbox.setToolTipText("Test with multiple payloads");

        enableSqlErrorDetectionCheckbox = new JCheckBox("SQL Errors");
        enableSqlErrorDetectionCheckbox.setToolTipText("Detect SQL error messages");
        enableSqlErrorDetectionCheckbox.setSelected(true);

        JCheckBox rateLimitBypassCheckbox = new JCheckBox("Rate Limit Bypass");
        rateLimitBypassCheckbox.setToolTipText("Enable advanced rate limiting bypass techniques");
        rateLimitBypassCheckbox.setSelected(false);

        includeParameterNamesCheckBox = new JCheckBox("Param Names");
        includeParameterNamesCheckBox.setToolTipText("Test parameter names");
        includeParameterNamesCheckBox.addActionListener(e -> refreshAllParameterLists());

        includeUrlPathInjectionCheckBox = new JCheckBox("URL Path");
        includeUrlPathInjectionCheckBox.setToolTipText("Test URL path injection");
        includeUrlPathInjectionCheckBox.addActionListener(e -> refreshAllParameterLists());

        includeHeaderParametersCheckBox = new JCheckBox("Headers");
        includeHeaderParametersCheckBox.setToolTipText("Include header parameters (may add noise)");
        includeHeaderParametersCheckBox.setSelected(false); // Default to false to reduce noise
        includeHeaderParametersCheckBox.addActionListener(e -> refreshAllParameterLists());

        // Scan button - compact
        scanButton = new JButton("🚀 Start Mass Testing");
        scanButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        scanButton.setBackground(new Color(34, 139, 34));
        scanButton.setForeground(Color.WHITE);
        scanButton.setPreferredSize(new Dimension(180, 35));
        scanButton.addActionListener(e -> {
            clearResultsBeforeMassScan();
            startMassTesting();
        });

        // Add all components in horizontal layout
        contentPanel.add(payloadLabel);
        contentPanel.add(payloadComboBox);
        contentPanel.add(new JLabel("Custom:"));
        contentPanel.add(customPayloadField);
        contentPanel.add(concurrencyLabel);
        contentPanel.add(concurrencySpinner);
        contentPanel.add(timeoutLabel);
        contentPanel.add(timeoutSpinner);
        contentPanel.add(multiPayloadCheckbox);
        contentPanel.add(enableSqlErrorDetectionCheckbox);
        contentPanel.add(rateLimitBypassCheckbox);
        contentPanel.add(includeParameterNamesCheckBox);
        contentPanel.add(includeUrlPathInjectionCheckBox);
        contentPanel.add(includeHeaderParametersCheckBox);
        contentPanel.add(scanButton);

        panel.add(contentPanel, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createCompactParameterSelectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Parameter Selection"));
        panel.setPreferredSize(new Dimension(350, 0)); // Fixed width, flexible height

        // Create parameter selection options - more compact
        JPanel optionsPanel = new JPanel();
        optionsPanel.setLayout(new BoxLayout(optionsPanel, BoxLayout.Y_AXIS));
        optionsPanel.setBorder(new EmptyBorder(5, 5, 5, 5));

        // Radio buttons for selection mode - enhanced with more options
        JRadioButton allParamsRadio = new JRadioButton("Test All Parameters");
        JRadioButton selectedParamsRadio = new JRadioButton("Test Selected Parameters");
        JRadioButton smartSelectRadio = new JRadioButton("Smart Selection (Recommended)");
        JRadioButton emptyParamsRadio = new JRadioButton("Empty Parameters Only");
        JRadioButton queryParamsRadio = new JRadioButton("Query Parameters Only");
        JRadioButton bodyParamsRadio = new JRadioButton("Body Parameters Only");
        JRadioButton jsonParamsRadio = new JRadioButton("JSON Parameters Only");
        JRadioButton headerParamsRadio = new JRadioButton("Header Parameters Only");

        // Group radio buttons
        ButtonGroup paramGroup = new ButtonGroup();
        paramGroup.add(allParamsRadio);
        paramGroup.add(selectedParamsRadio);
        paramGroup.add(smartSelectRadio);
        paramGroup.add(emptyParamsRadio);
        paramGroup.add(queryParamsRadio);
        paramGroup.add(bodyParamsRadio);
        paramGroup.add(jsonParamsRadio);
        paramGroup.add(headerParamsRadio);
        smartSelectRadio.setSelected(true); // Default to smart selection

        // Add radio buttons in logical groups
        optionsPanel.add(new JLabel("Quick Selection:"));
        optionsPanel.add(smartSelectRadio);
        optionsPanel.add(emptyParamsRadio);
        optionsPanel.add(allParamsRadio);
        optionsPanel.add(selectedParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(5));

        optionsPanel.add(new JLabel("Type-Specific:"));
        optionsPanel.add(queryParamsRadio);
        optionsPanel.add(bodyParamsRadio);
        optionsPanel.add(jsonParamsRadio);
        optionsPanel.add(headerParamsRadio);
        optionsPanel.add(Box.createVerticalStrut(10));

        // Parameter table - more compact
        String[] columnNames = {"✓", "Parameter", "Type", "Value"};
        DefaultTableModel paramTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public Class<?> getColumnClass(int column) {
                return column == 0 ? Boolean.class : String.class;
            }
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0;
            }
        };

        JTable paramTable = new JTable(paramTableModel);
        paramTable.getColumnModel().getColumn(0).setMaxWidth(40);
        paramTable.getColumnModel().getColumn(2).setMaxWidth(60);
        paramTable.setRowHeight(20);
        paramTable.setEnabled(false);

        JScrollPane scrollPane = new JScrollPane(paramTable);
        scrollPane.setPreferredSize(new Dimension(320, 200));

        // Enable/disable table based on radio selection
        selectedParamsRadio.addActionListener(e -> paramTable.setEnabled(true));
        allParamsRadio.addActionListener(e -> paramTable.setEnabled(false));
        smartSelectRadio.addActionListener(e -> paramTable.setEnabled(false));
        emptyParamsRadio.addActionListener(e -> paramTable.setEnabled(false));
        queryParamsRadio.addActionListener(e -> paramTable.setEnabled(false));
        bodyParamsRadio.addActionListener(e -> paramTable.setEnabled(false));
        jsonParamsRadio.addActionListener(e -> paramTable.setEnabled(false));
        headerParamsRadio.addActionListener(e -> paramTable.setEnabled(false));

        // Add listeners to update parameter table when request is selected
        requestsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = requestsTable.getSelectedRow();
                if (selectedRow >= 0 && selectedRow < loadedRequests.size()) {
                    IHttpRequestResponse selectedRequest = loadedRequests.get(selectedRow);
                    currentlyDisplayedRequest = selectedRequest;
                    updateParameterTable(paramTableModel, selectedRequest);
                }
            }
        });

        // Store references for component lookup
        paramTable.setName("parameterTable");
        allParamsRadio.setName("allParamsRadio");
        selectedParamsRadio.setName("selectedParamsRadio");
        smartSelectRadio.setName("smartSelectRadio");
        emptyParamsRadio.setName("emptyParamsRadio");
        queryParamsRadio.setName("queryParamsRadio");
        bodyParamsRadio.setName("bodyParamsRadio");
        jsonParamsRadio.setName("jsonParamsRadio");
        headerParamsRadio.setName("headerParamsRadio");

        panel.add(optionsPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createMultiPayloadPanel() {
        JPanel multiPanel = new JPanel();
        multiPanel.setLayout(new BoxLayout(multiPanel, BoxLayout.Y_AXIS));
        multiPanel.setBorder(BorderFactory.createTitledBorder("Multi-Payload Testing"));
        multiPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        // Multi-payload checkbox
        multiPayloadCheckbox = new JCheckBox("Enable Multi-Payload Testing");
        multiPayloadCheckbox.setToolTipText("Test with multiple payloads for comprehensive vulnerability detection");
        multiPayloadCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        multiPayloadCheckbox.setFont(multiPayloadCheckbox.getFont().deriveFont(Font.BOLD));
        multiPayloadCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        multiPanel.add(multiPayloadCheckbox);
        multiPanel.add(Box.createVerticalStrut(5));

        // Create payload list panel
        JPanel listPanel = new JPanel(new BorderLayout());
        listPanel.setMaximumSize(new Dimension(750, 120));

        // Initialize payload list model with default payloads
        payloadListModel = new DefaultListModel<>();
        // Enhanced default payloads for comprehensive testing
        payloadListModel.addElement("%3bWAITFOR+DELAY+'00%3a00%3a5'--");
        payloadListModel.addElement("';%20waitfor%20delay%20'0:0:6'%20--%20");
        payloadListModel.addElement("''||(select 1 from (select pg_sleep(6))x)||'");
        payloadListModel.addElement("0'XOR(if(now()=sysdate(),sleep(6-2),0))XOR'Z");
        payloadListModel.addElement(";SELECT IF((8303>8302),SLEEP(9),2356)#");
        payloadListModel.addElement("'XOR(if(now()=sysdate(),sleep(5*5),0))OR'");
        payloadListModel.addElement("XOR(if(now()=sysdate(),sleep(3),0))OR'");
        payloadListModel.addElement("14)%20AND%20(SELECT%207415%20FROM%20(SELECT(SLEEP(10)))CwkU)%20AND%20(7515=7515");
        payloadListModel.addElement("'+AND+(SELECT+2016+FROM+(SELECT(SLEEP(15)))SIfv)+AND+'vDZP'%3d'vDZP");
        payloadListModel.addElement(" ' AND '+OR+SLEEP(5)--+");
        payloadListModel.addElement(" '/**/and(select'1'from/**/pg_sleep(10))::text>'0");
        // Original payloads for backward compatibility
        payloadListModel.addElement("'XOR(if(now()=sysdate(),sleep(7),0))XOR'Z");
        payloadListModel.addElement("' AND SLEEP(7)--+");
        payloadListModel.addElement("' WAITFOR DELAY '0:0:7'--");

        payloadList = new JList<>(payloadListModel);
        payloadList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        payloadList.setVisibleRowCount(3);
        payloadList.setToolTipText("Select multiple payloads to test (Ctrl+Click for multiple selection)");

        JScrollPane listScrollPane = new JScrollPane(payloadList);
        listScrollPane.setPreferredSize(new Dimension(400, 80));

        // Create buttons panel
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        addPayloadButton = new JButton("Add");
        addPayloadButton.setToolTipText("Add custom payload to the list");
        addPayloadButton.addActionListener(e -> addCustomPayloadToMassTesting());

        removePayloadButton = new JButton("Remove");
        removePayloadButton.setToolTipText("Remove selected payloads from the list");
        removePayloadButton.addActionListener(e -> removeSelectedPayloadsFromMassTesting());

        // Custom payload input
        customPayloadInputField = new JTextField(15);
        customPayloadInputField.setToolTipText("Enter custom payload and click 'Add'");
        customPayloadInputField.addActionListener(e -> addCustomPayloadToMassTesting()); // Enter key support

        buttonsPanel.add(new JLabel("Custom:"));
        buttonsPanel.add(customPayloadInputField);
        buttonsPanel.add(addPayloadButton);
        buttonsPanel.add(removePayloadButton);

        // Assemble list panel
        listPanel.add(listScrollPane, BorderLayout.CENTER);
        listPanel.add(buttonsPanel, BorderLayout.SOUTH);

        // Add components to main panel
        multiPanel.add(listPanel);

        // Initially disable the list components
        updateMultiPayloadComponentsInMassTesting();

        // Add listener to checkbox
        multiPayloadCheckbox.addActionListener(e -> updateMultiPayloadComponentsInMassTesting());

        return multiPanel;
    }

    private void addCustomPayloadToMassTesting() {
        String payload = customPayloadInputField.getText().trim();
        if (!payload.isEmpty() && !payloadListModel.contains(payload)) {
            payloadListModel.addElement(payload);
            customPayloadInputField.setText("");
        }
    }

    private void removeSelectedPayloadsFromMassTesting() {
        int[] selectedIndices = payloadList.getSelectedIndices();
        // Remove from highest index to lowest to avoid index shifting issues
        for (int i = selectedIndices.length - 1; i >= 0; i--) {
            payloadListModel.removeElementAt(selectedIndices[i]);
        }
    }

    private void updateMultiPayloadComponentsInMassTesting() {
        boolean enabled = multiPayloadCheckbox.isSelected();
        payloadList.setEnabled(enabled);
        addPayloadButton.setEnabled(enabled);
        removePayloadButton.setEnabled(enabled);
        customPayloadInputField.setEnabled(enabled);
    }

    /**
     * Get the list of selected payloads for mass testing
     */
    private List<String> getSelectedPayloadsForMassTesting() {
        List<String> selectedPayloads = new ArrayList<>();

        if (multiPayloadCheckbox != null && multiPayloadCheckbox.isSelected() && payloadList != null) {
            List<String> selected = payloadList.getSelectedValuesList();
            if (!selected.isEmpty()) {
                selectedPayloads.addAll(selected);
            } else {
                // If no specific payloads are selected, use all payloads in the list
                for (int i = 0; i < payloadListModel.getSize(); i++) {
                    selectedPayloads.add(payloadListModel.getElementAt(i));
                }
            }
        } else {
            // Single payload mode - return the current payload
            String singlePayload = customPayloadField.getText().trim();
            if (!singlePayload.isEmpty()) {
                selectedPayloads.add(singlePayload);
            }
        }

        return selectedPayloads;
    }

    private JPanel createSqlErrorDetectionPanelForMassTesting() {
        JPanel sqlErrorPanel = new JPanel();
        sqlErrorPanel.setLayout(new BoxLayout(sqlErrorPanel, BoxLayout.Y_AXIS));
        sqlErrorPanel.setBorder(BorderFactory.createTitledBorder("SQL Error Detection"));
        sqlErrorPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        // Enable SQL error detection checkbox
        enableSqlErrorDetectionCheckbox = new JCheckBox("Enable SQL Error Detection");
        enableSqlErrorDetectionCheckbox.setToolTipText("Detect SQL error messages in responses alongside time-based detection");
        enableSqlErrorDetectionCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableSqlErrorDetectionCheckbox.setFont(enableSqlErrorDetectionCheckbox.getFont().deriveFont(Font.BOLD));
        enableSqlErrorDetectionCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        enableSqlErrorDetectionCheckbox.setSelected(true); // Default enabled
        sqlErrorPanel.add(enableSqlErrorDetectionCheckbox);

        // Show SQL errors in results checkbox
        showSqlErrorsInResultsCheckbox = new JCheckBox("Show SQL Error Details in Results");
        showSqlErrorsInResultsCheckbox.setToolTipText("Display SQL error information in the results table");
        showSqlErrorsInResultsCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        showSqlErrorsInResultsCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        showSqlErrorsInResultsCheckbox.setSelected(true); // Default enabled
        sqlErrorPanel.add(showSqlErrorsInResultsCheckbox);

        return sqlErrorPanel;
    }

    /**
     * Check if SQL error detection is enabled
     */
    public boolean isSqlErrorDetectionEnabled() {
        return enableSqlErrorDetectionCheckbox != null && enableSqlErrorDetectionCheckbox.isSelected();
    }

    /**
     * Check if SQL errors should be shown in results table
     */
    public boolean shouldShowSqlErrorsInResults() {
        return showSqlErrorsInResultsCheckbox != null && showSqlErrorsInResultsCheckbox.isSelected();
    }

    /**
     * Clear results before starting a new mass scan
     */
    private void clearResultsBeforeMassScan() {
        if (mainPanel != null) {
            // Clear results panel
            if (mainPanel.getResultsPanel() != null) {
                mainPanel.getResultsPanel().clearResults();
            }

            // Update status
            callbacks.printOutput("=== Starting New Mass Scan - Previous Results Cleared ===");
        }
    }

    /**
     * Add a request to the mass testing list
     * @param request The request to add
     */
    public void addRequest(IHttpRequestResponse request) {
        // Check if request is already added
        for (IHttpRequestResponse existingRequest : loadedRequests) {
            if (isRequestEqual(existingRequest, request)) {
                JOptionPane.showMessageDialog(this, 
                    "This request is already in the list.",
                    "Duplicate Request", JOptionPane.INFORMATION_MESSAGE);
                return;
            }
        }
        
        // Add request to list
        loadedRequests.add(request);
        
        // Parse parameters
        HttpParameterParser parser = new HttpParameterParser(helpers);
        List<HttpParameter> allParameters = parser.parseParameters(request);

        // Filter parameters based on checkbox settings
        List<HttpParameter> filteredParameters = filterParameters(allParameters);
        requestParametersMap.put(request, filteredParameters);
        
        // Initialize parameter selection map with all parameters FALSE by default
        Map<HttpParameter, Boolean> paramSelections = new HashMap<>();
        for (HttpParameter param : filteredParameters) {
            // Default all parameters to FALSE
            paramSelections.put(param, Boolean.FALSE);
        }
        parameterSelectionMap.put(request, paramSelections);

        // Default request to selected
        requestSelectionMap.put(request, Boolean.TRUE);

        // Add row to table
        IHttpService service = request.getHttpService();
        String host = service.getHost();
        String url = service.getProtocol() + "://" + host + ":" + service.getPort();
        String method = helpers.analyzeRequest(request).getMethod();
        String paramCount = filteredParameters.size() + " params";
        
        requestsTableModel.addRow(new Object[] {
            Boolean.TRUE, // Selected
            host,
            url,
            method,
            paramCount,
            "Pending" // Initial status
        });
    }
    


    /**
     * Check if two requests are the same (same URL and method)
     */
    private boolean isRequestEqual(IHttpRequestResponse req1, IHttpRequestResponse req2) {
        if (req1 == req2) return true;

        IHttpService service1 = req1.getHttpService();
        IHttpService service2 = req2.getHttpService();

        if (!service1.getHost().equals(service2.getHost())) return false;
        if (!service1.getProtocol().equals(service2.getProtocol())) return false;
        if (service1.getPort() != service2.getPort()) return false;

        String method1 = helpers.analyzeRequest(req1).getMethod();
        String method2 = helpers.analyzeRequest(req2).getMethod();

        return method1.equals(method2);
    }
    
    /**
     * Start testing all selected requests
     */
    private void startMassTesting() {
        // Get selected requests
        List<IHttpRequestResponse> selectedRequests = new ArrayList<>();
        for (int i = 0; i < loadedRequests.size(); i++) {
            IHttpRequestResponse request = loadedRequests.get(i);
            if (requestSelectionMap.get(request)) {
                selectedRequests.add(request);
                
                // Update status to 'Testing'
                requestsTableModel.setValueAt("Testing...", i, 5);
            }
        }
        
        if (selectedRequests.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "Please select at least one request to test.",
                "No Requests Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        // Get configuration values
        final List<String> payloads = getSelectedPayloadsForMassTesting();
        final int concurrencyLevel = (Integer) concurrencySpinner.getValue();

        if (payloads.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please provide at least one valid payload.",
                "No Payloads", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        // Get parameter selection mode - enhanced with new options
        final boolean testAllParams = findComponentByName(this, "allParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "allParamsRadio")).isSelected();
        final boolean testSelectedParams = findComponentByName(this, "selectedParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "selectedParamsRadio")).isSelected();
        final boolean testSmartSelect = findComponentByName(this, "smartSelectRadio") != null &&
            ((JRadioButton)findComponentByName(this, "smartSelectRadio")).isSelected();
        final boolean testEmptyParams = findComponentByName(this, "emptyParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "emptyParamsRadio")).isSelected();
        final boolean testQueryParams = findComponentByName(this, "queryParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "queryParamsRadio")).isSelected();
        final boolean testBodyParams = findComponentByName(this, "bodyParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "bodyParamsRadio")).isSelected();
        final boolean testJsonParams = findComponentByName(this, "jsonParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "jsonParamsRadio")).isSelected();
        final boolean testHeaderParams = findComponentByName(this, "headerParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "headerParamsRadio")).isSelected();
        
        // Disable scan button while testing
        scanButton.setEnabled(false);
        
        // Run tests in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                // Switch to the results tab
                mainPanel.showResultsTab();
                
                // Clear previous results
                mainPanel.getResultsPanel().clearResults();
                
                // Process each request
                for (int i = 0; i < selectedRequests.size(); i++) {
                    final IHttpRequestResponse request = selectedRequests.get(i);
                    final int requestIndex = loadedRequests.indexOf(request);
                    
                    // Get parameters for this request (already filtered by checkbox settings)
                    List<HttpParameter> allParameters = requestParametersMap.get(request);
                    List<HttpParameter> parametersToTest = new ArrayList<>();
                    
                    // Filter parameters based on selection criteria
                    if (testAllParams) {
                        // Test all parameters
                        parametersToTest.addAll(allParameters);
                    } else if (testSelectedParams) {
                        // Test only specifically selected parameters
                        Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(request);
                        if (paramSelections != null && !paramSelections.isEmpty()) {
                            // Selected parameters found
                            for (Map.Entry<HttpParameter, Boolean> entry : paramSelections.entrySet()) {
                                if (entry.getValue() != null && entry.getValue()) {
                                    parametersToTest.add(entry.getKey());
                                }
                            }
                        } else {
                            // No parameter selection data found, initialize with defaults
                            List<HttpParameter> parameters = requestParametersMap.get(request);
                            if (parameters != null) {
                                Map<HttpParameter, Boolean> newParamSelections = new HashMap<>();
                                parameterSelectionMap.put(request, newParamSelections);
                                
                                // Set all parameters to not selected by default
                                for (HttpParameter param : parameters) {
                                    newParamSelections.put(param, Boolean.FALSE);
                                }
                            }
                        }
                    } else if (testSmartSelect) {
                        // Smart selection: empty parameters first, then high-risk types
                        for (HttpParameter param : allParameters) {
                            boolean shouldSelect = false;

                            // Priority 1: Empty parameters
                            if (param.isEmpty()) {
                                shouldSelect = true;
                            }
                            // Priority 2: High-risk parameter types
                            else if (param.getType() == HttpParameter.Type.BODY ||
                                    param.getType() == HttpParameter.Type.JSON ||
                                    param.getType() == HttpParameter.Type.URL) {
                                shouldSelect = true;
                            }
                            // Priority 3: Common vulnerable parameter names
                            else if (param.getName().toLowerCase().contains("id") ||
                                    param.getName().toLowerCase().contains("user") ||
                                    param.getName().toLowerCase().contains("search") ||
                                    param.getName().toLowerCase().contains("query")) {
                                shouldSelect = true;
                            }

                            if (shouldSelect) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testEmptyParams) {
                        // Test only empty parameters
                        for (HttpParameter param : allParameters) {
                            if (param.isEmpty()) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testQueryParams) {
                        // Test only query parameters
                        for (HttpParameter param : allParameters) {
                            if (param.getType() == HttpParameter.Type.URL) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testBodyParams) {
                        // Test only body parameters
                        for (HttpParameter param : allParameters) {
                            if (param.getType() == HttpParameter.Type.BODY) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testJsonParams) {
                        // Test only JSON parameters
                        for (HttpParameter param : allParameters) {
                            if (param.getType() == HttpParameter.Type.JSON) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testHeaderParams) {
                        // Test only header parameters
                        for (HttpParameter param : allParameters) {
                            if (param.getType() == HttpParameter.Type.HEADER) {
                                parametersToTest.add(param);
                            }
                        }
                    } else {
                        // Default to smart selection
                        for (HttpParameter param : allParameters) {
                            if (param.isEmpty() || param.getType() == HttpParameter.Type.BODY ||
                                param.getType() == HttpParameter.Type.JSON || param.getType() == HttpParameter.Type.URL) {
                                parametersToTest.add(param);
                            }
                        }
                    }
                    
                    // Skip if no parameters to test
                    if (parametersToTest.isEmpty()) {
                        SwingUtilities.invokeLater(new Runnable() {
                            @Override
                            public void run() {
                                requestsTableModel.setValueAt("No params", requestIndex, 5);
                            }
                        });
                        continue;
                    }
                    
                    // Start scanner for this request - ensure we use the correct request object
                    final IHttpRequestResponse currentRequest = request; // Create a final reference to the current request
                    final int currentRequestIndex = requestIndex; // Store the current request index
                    
                    // Create a dedicated thread for this specific domain/request to ensure isolation
                    Thread requestThread = new Thread(() -> {
                        try {
                            // Log the domain being tested
                            callbacks.printOutput("Testing domain: " + currentRequest.getHttpService().getHost() +
                                " with " + parametersToTest.size() + " parameters and " + payloads.size() + " payloads");

                            // Test with each payload
                            for (int payloadIndex = 0; payloadIndex < payloads.size(); payloadIndex++) {
                                String currentPayload = payloads.get(payloadIndex);

                                callbacks.printOutput("Testing payload " + (payloadIndex + 1) + "/" + payloads.size() +
                                    ": " + currentPayload.substring(0, Math.min(50, currentPayload.length())) + "...");

                                // Update status to show current payload being tested
                                final int finalPayloadIndex = payloadIndex;
                                SwingUtilities.invokeLater(() -> {
                                    requestsTableModel.setValueAt("Testing " + (finalPayloadIndex + 1) + "/" + payloads.size(), currentRequestIndex, 5);
                                });

                                // Use optimized scanner with baseline caching and enhanced detection
                                callbacks.printOutput("=== Mass Testing: Request " + (currentRequestIndex + 1) + "/" + selectedRequests.size() +
                                                     ", Payload " + (payloadIndex + 1) + "/" + payloads.size() + " ===");
                                callbacks.printOutput("Testing " + parametersToTest.size() + " parameters with payload: " +
                                                     currentPayload.substring(0, Math.min(50, currentPayload.length())) + "...");

                                ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
                                    currentRequest, parametersToTest, currentPayload, concurrencyLevel, mainPanel);

                                // Start the optimized scan
                                scanner.startScan();

                                // Add a small delay between payloads to avoid overwhelming the server
                                if (payloadIndex < payloads.size() - 1) {
                                    Thread.sleep(1000); // 1 second delay between payloads
                                }
                            }

                            // Update status to 'Completed' for this specific request
                            SwingUtilities.invokeLater(() -> {
                                requestsTableModel.setValueAt("Completed", currentRequestIndex, 5);
                            });
                        } catch (Exception ex) {
                            // Handle any exceptions during scanning
                            callbacks.printError("Error scanning domain: " + currentRequest.getHttpService().getHost() +
                                " - " + ex.getMessage());
                            SwingUtilities.invokeLater(() -> {
                                requestsTableModel.setValueAt("Error", currentRequestIndex, 5);
                            });
                        }
                    });
                    
                    // Start the thread for this domain and wait for it to complete
                    requestThread.start();
                    try {
                        // Wait for this domain's scan to finish before proceeding to next domain
                        requestThread.join();
                    } catch (InterruptedException e) {
                        callbacks.printError("Interrupted while waiting for domain scan to complete: " + e.getMessage());
                    }
                }
                
                // Re-enable scan button when done
                SwingUtilities.invokeLater(new Runnable() {
                    @Override
                    public void run() {
                        scanButton.setEnabled(true);
                        JOptionPane.showMessageDialog(MassTestingPanel.this, 
                            "Mass testing completed for " + selectedRequests.size() + " requests.",
                            "Testing Complete", JOptionPane.INFORMATION_MESSAGE);
                    }
                });
            }
        }).start();
    }
    
    @Override
    public IHttpService getHttpService() {
        return currentlyDisplayedRequest != null ? currentlyDisplayedRequest.getHttpService() : null;
    }
    
    @Override
    public byte[] getRequest() {
        return currentlyDisplayedRequest != null ? currentlyDisplayedRequest.getRequest() : null;
    }
    
    @Override
    public byte[] getResponse() {
        return currentlyDisplayedRequest != null ? currentlyDisplayedRequest.getResponse() : null;
    }
    
    /**
     * Helper method to find a component by name
     * @param container The container to search in
     * @param name The name to search for
     * @return The found component or null
     */
    private Component findComponentByName(Container container, String name) {
        if (name.equals(container.getName())) {
            return container;
        }
        
        for (Component component : container.getComponents()) {
            if (name.equals(component.getName())) {
                return component;
            }
            
            if (component instanceof Container) {
                Component result = findComponentByName((Container) component, name);
                if (result != null) {
                    return result;
                }
            }
        }
        
        return null;
    }

    /**
     * Filter parameters based on checkbox settings
     */
    private List<HttpParameter> filterParameters(List<HttpParameter> allParameters) {
        List<HttpParameter> filteredParameters = new ArrayList<>();

        boolean includeNameInjection = includeParameterNamesCheckBox != null && includeParameterNamesCheckBox.isSelected();
        boolean includeUrlPathInjection = includeUrlPathInjectionCheckBox != null && includeUrlPathInjectionCheckBox.isSelected();
        boolean includeHeaderParameters = includeHeaderParametersCheckBox != null && includeHeaderParametersCheckBox.isSelected();

        for (HttpParameter param : allParameters) {
            boolean shouldInclude = true;

            // Filter out __NAME__ parameters if name injection is disabled
            if (param.getName().endsWith("__NAME__") && !includeNameInjection) {
                shouldInclude = false;
            }

            // Filter out URL_PATH parameters if URL path injection is disabled
            if (param.getType() == HttpParameter.Type.URL_PATH && !includeUrlPathInjection) {
                shouldInclude = false;
            }

            // Filter out HEADER parameters if header parameters are disabled
            if (param.getType() == HttpParameter.Type.HEADER && !includeHeaderParameters) {
                shouldInclude = false;
            }

            // Filter out problematic synthetic parameters that cause injection errors
            if (param.getName().startsWith("__EXTRACTED") ||
                (param.getName().equals("__BODY__") && param.getType() == HttpParameter.Type.BODY)) {
                shouldInclude = false;
                callbacks.printOutput("Filtering out problematic synthetic parameter: " + param.getName());
            }

            if (shouldInclude) {
                filteredParameters.add(param);
            }
        }

        return filteredParameters;
    }

    /**
     * Refresh parameter lists for all loaded requests when checkbox settings change
     */
    private void refreshAllParameterLists() {
        // Re-parse and filter parameters for all loaded requests
        HttpParameterParser parser = new HttpParameterParser(helpers);

        for (IHttpRequestResponse request : loadedRequests) {
            // Re-parse all parameters
            List<HttpParameter> allParameters = parser.parseParameters(request);

            // Filter based on current checkbox settings
            List<HttpParameter> filteredParameters = filterParameters(allParameters);

            // Update the parameter map
            requestParametersMap.put(request, filteredParameters);

            // Update parameter selection map - preserve existing selections where possible
            Map<HttpParameter, Boolean> existingSelections = parameterSelectionMap.get(request);
            Map<HttpParameter, Boolean> newSelections = new HashMap<>();

            for (HttpParameter param : filteredParameters) {
                // Try to preserve existing selection state, default to FALSE for new parameters
                Boolean existingSelection = existingSelections != null ? existingSelections.get(param) : null;
                newSelections.put(param, existingSelection != null ? existingSelection : Boolean.FALSE);
            }

            parameterSelectionMap.put(request, newSelections);
        }

        // Update the parameter count in the requests table
        for (int i = 0; i < loadedRequests.size(); i++) {
            IHttpRequestResponse request = loadedRequests.get(i);
            List<HttpParameter> parameters = requestParametersMap.get(request);
            String paramCount = parameters.size() + " params";
            requestsTableModel.setValueAt(paramCount, i, 4); // Column 4 is the parameter count
        }

        // Refresh the parameter table if a request is currently selected
        if (currentlyDisplayedRequest != null) {
            JTable paramTable = (JTable) findComponentByName(this, "parameterTable");
            if (paramTable != null) {
                DefaultTableModel paramTableModel = (DefaultTableModel) paramTable.getModel();
                updateParameterTable(paramTableModel, currentlyDisplayedRequest);
            }
        }
    }

    /**
     * Start mass testing with configuration from scan control panel
     */
    public void startMassTestingWithConfig(List<String> payloads, int concurrencyLevel,
                                         boolean sqlErrorDetection, boolean paramNameInjection,
                                         boolean urlPathInjection, boolean headerParameters) {
        // Get selected requests
        List<IHttpRequestResponse> selectedRequests = new ArrayList<>();
        for (int i = 0; i < loadedRequests.size(); i++) {
            IHttpRequestResponse request = loadedRequests.get(i);
            if (requestSelectionMap.get(request)) {
                selectedRequests.add(request);

                // Update status to 'Testing'
                requestsTableModel.setValueAt("Testing...", i, 5);
            }
        }

        if (selectedRequests.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please load and select at least one request in the Mass Testing tab.",
                "No Requests Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (payloads.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please configure at least one payload in the Scan Controls tab.",
                "No Payloads", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Clear previous results
        if (mainPanel != null) {
            if (mainPanel.getResultsPanel() != null) {
                mainPanel.getResultsPanel().clearResults();
            }
            callbacks.printOutput("=== Starting Mass Testing with Scan Control Configuration ===");
        }

        // Get parameter selection mode from the UI
        final boolean testAllParams = findComponentByName(this, "allParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "allParamsRadio")).isSelected();
        final boolean testSelectedParams = findComponentByName(this, "selectedParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "selectedParamsRadio")).isSelected();
        final boolean testQueryParams = findComponentByName(this, "queryParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "queryParamsRadio")).isSelected();
        final boolean testBodyParams = findComponentByName(this, "bodyParamsRadio") != null &&
            ((JRadioButton)findComponentByName(this, "bodyParamsRadio")).isSelected();

        // Run tests in a separate thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                // Switch to the results tab
                mainPanel.showResultsTab();

                // Process each request
                for (int i = 0; i < selectedRequests.size(); i++) {
                    final IHttpRequestResponse request = selectedRequests.get(i);
                    final int requestIndex = loadedRequests.indexOf(request);

                    // Get parameters for this request
                    List<HttpParameter> allParameters = requestParametersMap.get(request);
                    List<HttpParameter> parametersToTest = new ArrayList<>();

                    // Filter parameters based on selection criteria
                    if (testAllParams) {
                        parametersToTest.addAll(allParameters);
                    } else if (testSelectedParams) {
                        Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(request);
                        if (paramSelections != null) {
                            for (Map.Entry<HttpParameter, Boolean> entry : paramSelections.entrySet()) {
                                if (entry.getValue() != null && entry.getValue()) {
                                    parametersToTest.add(entry.getKey());
                                }
                            }
                        }
                    } else if (testQueryParams) {
                        for (HttpParameter param : allParameters) {
                            if (param.getType().toString().contains("URL")) {
                                parametersToTest.add(param);
                            }
                        }
                    } else if (testBodyParams) {
                        for (HttpParameter param : allParameters) {
                            if (param.getType().toString().contains("BODY")) {
                                parametersToTest.add(param);
                            }
                        }
                    }

                    if (parametersToTest.isEmpty()) {
                        callbacks.printOutput("No parameters to test for request " + (i + 1));
                        requestsTableModel.setValueAt("No Parameters", requestIndex, 5);
                        continue;
                    }

                    // Test with each payload
                    for (String payload : payloads) {
                        callbacks.printOutput("Testing request " + (i + 1) + "/" + selectedRequests.size() +
                                            " with payload: " + payload.substring(0, Math.min(50, payload.length())) + "...");

                        // Create scanner for this request and payload
                        ParallelScanner scanner = new ParallelScanner(callbacks, helpers,
                            request, parametersToTest, payload, concurrencyLevel, mainPanel);

                        // Start the scan
                        scanner.startScan();

                        // Small delay between payloads
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException ex) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }

                    // Update status
                    requestsTableModel.setValueAt("Completed", requestIndex, 5);
                }

                callbacks.printOutput("Mass testing completed for " + selectedRequests.size() + " requests.");
            }
        }).start();
    }
}