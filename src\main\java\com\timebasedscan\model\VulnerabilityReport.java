package com.timebasedscan.model;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Class to generate detailed vulnerability reports
 */
public class VulnerabilityReport {
    
    private final HistoryEntry historyEntry;
    private final List<TestResult> vulnerableResults;
    private final String timestamp;
    
    public VulnerabilityReport(HistoryEntry historyEntry, List<TestResult> vulnerableResults) {
        this.historyEntry = historyEntry;
        this.vulnerableResults = vulnerableResults;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.timestamp = sdf.format(new Date(historyEntry.getTimestamp()));
    }
    
    /**
     * Generate a full HTML vulnerability report with all details
     */
    public String generateFullReport() {
        StringBuilder html = new StringBuilder();
        html.append("<html><head>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append("h1 { color: #cc0000; }");
        html.append("h2 { color: #990000; margin-top: 20px; }");
        html.append("h3 { margin-top: 15px; }");
        html.append(".section { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }");
        html.append(".critical { background-color: #ffebeb; border-left: 5px solid #cc0000; }");
        html.append(".high { background-color: #fff0e0; border-left: 5px solid #ff6600; }");
        html.append(".medium { background-color: #fffbe0; border-left: 5px solid #ffcc00; }");
        html.append(".low { background-color: #f0f8ff; border-left: 5px solid #0066cc; }");
        html.append(".info { background-color: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; }");
        html.append(".code { font-family: monospace; background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd; border-radius: 3px; overflow-x: auto; }");
        html.append(".evidence { font-family: monospace; background-color: #fff8f8; padding: 10px; border: 1px solid #ffcccc; border-radius: 3px; margin: 10px 0; }");
        html.append("table { border-collapse: collapse; width: 100%; margin: 10px 0; }");
        html.append("th, td { text-align: left; padding: 8px; border: 1px solid #ddd; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append("tr:nth-child(even) { background-color: #f9f9f9; }");
        html.append(".recommendation { background-color: #e6ffe6; border-left: 5px solid #009900; padding: 10px; margin: 10px 0; }");
        html.append("</style>");
        html.append("</head><body>");
        
        // Report header
        html.append("<h1>Time-Based Vulnerability Report</h1>");
        html.append("<div style='text-align:right;font-size:11px;color:#666;margin-bottom:10px;'><em>Developed by Pankaj Kumar Thakur (<EMAIL>)</em></div>");
        html.append("<div class='info'>");
        html.append("<p><strong>URL:</strong> ").append(escapeHtml(historyEntry.getUrl())).append("</p>");
        html.append("<p><strong>Method:</strong> ").append(historyEntry.getMethod()).append("</p>");
        html.append("<p><strong>Timestamp:</strong> ").append(timestamp).append("</p>");
        html.append("<p><strong>Parameters Tested:</strong> ").append(historyEntry.getParamCount()).append("</p>");
        html.append("<p><strong>Vulnerable Parameters:</strong> ").append(historyEntry.getVulnerableCount())
                .append(" (").append(calculatePercentage(historyEntry.getVulnerableCount(), historyEntry.getParamCount())).append(")</p>");
        html.append("<p><strong>Total Scan Duration:</strong> ").append(historyEntry.getDuration()).append(" ms</p>");
        
        // Add triggered payload for quick reference if vulnerabilities found
        if (!vulnerableResults.isEmpty()) {
            TestResult firstVuln = vulnerableResults.get(0);
            html.append("<p><strong>Triggered Payload:</strong> <span class='code' style='display:inline-block;'>")
                .append(escapeHtml(firstVuln.getParameterValue())).append("</span></p>");
        }
        
        html.append("</div>");
        
        // Summary section with detailed vulnerability description
        html.append("<h2>Executive Summary</h2>");
        html.append("<div class='section critical'>");
        html.append("<p><strong>Severity:</strong> Critical</p>");

        // Generate detailed description with specific parameter information
        html.append("<p><strong>Description:</strong> Time-based SQL injection vulnerabilities were detected in the application endpoint <code>")
                .append(escapeHtml(historyEntry.getUrl())).append("</code>. ");

        if (!vulnerableResults.isEmpty()) {
            if (vulnerableResults.size() == 1) {
                TestResult vuln = vulnerableResults.get(0);
                html.append("The parameter <strong>'").append(escapeHtml(vuln.getParameterName()))
                    .append("'</strong> (").append(escapeHtml(vuln.getParameterType())).append(" parameter) ")
                    .append("is vulnerable to time-based SQL injection attacks. ");
            } else {
                html.append("Multiple parameters (");
                for (int i = 0; i < vulnerableResults.size(); i++) {
                    if (i > 0) html.append(", ");
                    html.append("<strong>'").append(escapeHtml(vulnerableResults.get(i).getParameterName())).append("'</strong>");
                }
                html.append(") are vulnerable to time-based SQL injection attacks. ");
            }
        }

        html.append("This type of vulnerability allows attackers to extract sensitive data from the database, " +
                "modify database content, or potentially gain unauthorized access to the underlying server.</p>");

        html.append("<p><strong>Technical Details:</strong> The vulnerability was confirmed by injecting time-delay payloads that caused measurable delays in server response times. ");
        if (!vulnerableResults.isEmpty()) {
            TestResult firstVuln = vulnerableResults.get(0);
            html.append("For example, the payload <code>").append(escapeHtml(firstVuln.getParameterValue()))
                .append("</code> caused a response delay of ").append(firstVuln.getResponseTime())
                .append("ms compared to the baseline of ").append(firstVuln.getBaselineTime())
                .append("ms, indicating successful SQL injection.</p>");
        }

        html.append("<p><strong>Impact:</strong> An attacker can exploit these vulnerabilities to:</p>");
        html.append("<ul>");
        html.append("<li>Extract sensitive data from the database (usernames, passwords, personal information)</li>");
        html.append("<li>Add, modify, or delete database records</li>");
        html.append("<li>Bypass authentication mechanisms</li>");
        html.append("<li>Escalate privileges within the application</li>");
        html.append("<li>In some cases, achieve server-level command execution</li>");
        html.append("<li>Cause denial of service through resource exhaustion</li>");
        html.append("</ul>");
        html.append("</div>");
        
        // Vulnerable parameters section
        html.append("<h2>Vulnerable Parameters</h2>");
        
        if (!vulnerableResults.isEmpty()) {
            html.append("<table>");
            html.append("<tr>");
            html.append("<th>Parameter</th>");
            html.append("<th>Type</th>");
            html.append("<th>Successful Payload</th>");
            html.append("<th>Response Time (ms)</th>");
            html.append("<th>Baseline Time (ms)</th>");
            html.append("<th>Time Difference</th>");
            html.append("</tr>");
            
            for (TestResult result : vulnerableResults) {
                html.append("<tr>");
                html.append("<td>").append(escapeHtml(result.getParameterName())).append("</td>");
                html.append("<td>").append(escapeHtml(result.getParameterType())).append("</td>");
                html.append("<td class='code'>").append(escapeHtml(truncateValue(result.getParameterValue(), 40))).append("</td>");
                html.append("<td>").append(result.getResponseTime()).append("</td>");
                html.append("<td>").append(result.getBaselineTime()).append("</td>");
                html.append("<td>").append(result.getTimeDifference()).append(" ms (")
                   .append(calculatePercentageIncrease(result.getResponseTime(), result.getBaselineTime())).append(")</td>");
                html.append("</tr>");
            }
            
            html.append("</table>");
        } else {
            html.append("<p>No vulnerable parameters found.</p>");
        }
        
        // Proof of Concept section
        html.append("<h2>Proof of Concept</h2>");
        if (!vulnerableResults.isEmpty()) {
            TestResult firstVuln = vulnerableResults.get(0);
            
            html.append("<div class='section'>");
            
            // Add triggered payload in a highlighted box
            html.append("<div style='background-color: #fff0f0; border: 2px solid #cc0000; padding: 10px; margin-bottom: 15px; border-radius: 5px;'>");
            html.append("<h3 style='margin-top: 0; color: #cc0000;'>Triggered Payload</h3>");
            html.append("<div class='code' style='background-color: #fff8f8; border-color: #ffcccc;'>");
            html.append("<strong>Parameter:</strong> ").append(escapeHtml(firstVuln.getParameterName())).append("<br/>");
            html.append("<strong>Payload:</strong> ").append(escapeHtml(firstVuln.getParameterValue()));
            html.append("</div>");
            html.append("</div>");
            
            html.append("<h3>Request Details</h3>");
            html.append("<p>The following request demonstrates exploitation of the '")
                  .append(escapeHtml(firstVuln.getParameterName()))
                  .append("' parameter:</p>");
            
            html.append("<div class='code'>");
            html.append(formatRequestAsPoc(firstVuln, historyEntry.getUrl(), historyEntry.getMethod()));
            html.append("</div>");
            
            html.append("<h3>Evidence</h3>");
            html.append("<p>The original request completed in approximately ")
                  .append(firstVuln.getBaselineTime())
                  .append(" ms, while the malicious payload caused a delay of ")
                  .append(firstVuln.getResponseTime())
                  .append(" ms - a ")
                  .append(calculatePercentageIncrease(firstVuln.getResponseTime(), firstVuln.getBaselineTime()))
                  .append(" increase.</p>");
            
            if (firstVuln.getEvidence() != null && !firstVuln.getEvidence().isEmpty()) {
                html.append("<div class='evidence'>");
                html.append(escapeHtml(firstVuln.getEvidence()).replace("\n", "<br/>"));
                html.append("</div>");
            }
            
            html.append("</div>");
            
            // Attack simulation
            html.append("<h3>Attack Simulation</h3>");
            html.append("<p>An attacker could exploit this vulnerability to extract sensitive data using similar techniques:</p>");
            
            StringBuilder attackSim = new StringBuilder();
            if (firstVuln.getParameterValue().toLowerCase().contains("sleep")) {
                attackSim.append("# Example: Extract user table data using conditional time delays<br/>");
                attackSim.append("Parameter: ").append(escapeHtml(firstVuln.getParameterName())).append("<br/>");
                attackSim.append("Value: ").append(escapeHtml(firstVuln.getParameterValue().replace("SLEEP(7)", "IF((SELECT COUNT(*) FROM users)>0,SLEEP(5),0)")));
            } else if (firstVuln.getParameterValue().toLowerCase().contains("waitfor")) {
                attackSim.append("# Example: Extract user table data using conditional time delays<br/>");
                attackSim.append("Parameter: ").append(escapeHtml(firstVuln.getParameterName())).append("<br/>");
                attackSim.append("Value: ").append(escapeHtml(firstVuln.getParameterValue().replace("WAITFOR DELAY '0:0:5'", "IF (SELECT COUNT(*) FROM users)>0 WAITFOR DELAY '0:0:5'")));
            } else {
                attackSim.append("# Example: Extract user table data using conditional time delays<br/>");
                attackSim.append("Parameter: ").append(escapeHtml(firstVuln.getParameterName())).append("<br/>");
                attackSim.append("Value: ' AND (SELECT CASE WHEN (SELECT count(*) FROM users)>0 THEN pg_sleep(5) ELSE pg_sleep(0) END)--");
            }
            
            html.append("<div class='code'>");
            html.append(attackSim);
            html.append("</div>");
        } else {
            html.append("<p>No vulnerable parameters found to demonstrate proof of concept.</p>");
        }
        
        // Remediation section
        html.append("<h2>Remediation Recommendations</h2>");
        html.append("<div class='recommendation'>");
        html.append("<h3>1. Implement Prepared Statements/Parameterized Queries</h3>");
        html.append("<p>Replace dynamic SQL construction with prepared statements:</p>");
        html.append("<div class='code'>");
        html.append("// Vulnerable code:<br/>");
        html.append("String query = \"SELECT * FROM users WHERE username = '\" + username + \"'\";<br/><br/>");
        html.append("// Secure code (Java example):<br/>");
        html.append("PreparedStatement stmt = conn.prepareStatement(\"SELECT * FROM users WHERE username = ?\");<br/>");
        html.append("stmt.setString(1, username);<br/>");
        html.append("ResultSet rs = stmt.executeQuery();");
        html.append("</div>");
        
        html.append("<h3>2. Input Validation</h3>");
        html.append("<p>Implement strict input validation for all parameters:</p>");
        html.append("<ul>");
        html.append("<li>Use whitelist validation wherever possible</li>");
        html.append("<li>Validate data types (e.g., integers, dates)</li>");
        html.append("<li>Limit input length and character sets</li>");
        html.append("</ul>");
        
        html.append("<h3>3. Use ORM Frameworks Properly</h3>");
        html.append("<p>When using Object-Relational Mapping frameworks, avoid raw SQL queries:</p>");
        html.append("<div class='code'>");
        html.append("// Vulnerable Hibernate/JPA code:<br/>");
        html.append("Query query = session.createQuery(\"from User where username = '\" + username + \"'\");<br/><br/>");
        html.append("// Secure Hibernate/JPA code:<br/>");
        html.append("Query query = session.createQuery(\"from User where username = :username\");<br/>");
        html.append("query.setParameter(\"username\", username);");
        html.append("</div>");
        
        html.append("<h3>4. Apply Principle of Least Privilege</h3>");
        html.append("<p>Ensure database connections use accounts with minimal required privileges:</p>");
        html.append("<ul>");
        html.append("<li>Different database accounts for different application functions</li>");
        html.append("<li>Read-only access where possible</li>");
        html.append("<li>No direct access to sensitive tables for web applications</li>");
        html.append("</ul>");
        
        html.append("<h3>5. Implement WAF Rules</h3>");
        html.append("<p>As an additional layer of defense, configure Web Application Firewall rules to block common SQL injection patterns.</p>");
        html.append("</div>");
        
        // References section
        html.append("<h2>References</h2>");
        html.append("<ul>");
        html.append("<li><a href=\"https://owasp.org/www-community/attacks/SQL_Injection\">OWASP: SQL Injection</a></li>");
        html.append("<li><a href=\"https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html\">OWASP: SQL Injection Prevention Cheat Sheet</a></li>");
        html.append("<li><a href=\"https://portswigger.net/web-security/sql-injection\">PortSwigger: SQL Injection</a></li>");
        html.append("<li><a href=\"https://cwe.mitre.org/data/definitions/89.html\">CWE-89: Improper Neutralization of Special Elements used in an SQL Command</a></li>");
        html.append("</ul>");
        
        // Footer with author attribution
        html.append("<hr style='margin-top:30px;'>");
        html.append("<div style='text-align:center;font-size:12px;color:#666;margin:20px 0;'>");
        html.append("Report generated by Time-Based Scanner Extension<br>");
        html.append("<strong>Developed by Pankaj Kumar Thakur (<EMAIL>)</strong>");
        html.append("</div>");
        
        html.append("</body></html>");
        return html.toString();
    }
    
    /**
     * Format a request as a proof of concept using raw HTTP request format with proper line breaks
     */
    private String formatRequestAsPoc(TestResult result, String url, String method) {
        StringBuilder poc = new StringBuilder();

        // Get parameter name and payload
        String paramName = result.getParameterName();
        String payload = result.getParameterValue();

        // Parse URL to get host and path
        String host = "example.com";
        String path = "/";
        String queryString = "";

        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                String urlWithoutProtocol = url.substring(url.indexOf("://") + 3);
                int slashIndex = urlWithoutProtocol.indexOf("/");
                if (slashIndex > 0) {
                    host = urlWithoutProtocol.substring(0, slashIndex);
                    String pathAndQuery = urlWithoutProtocol.substring(slashIndex);

                    // Separate path and query string
                    int questionIndex = pathAndQuery.indexOf("?");
                    if (questionIndex > 0) {
                        path = pathAndQuery.substring(0, questionIndex);
                        queryString = pathAndQuery.substring(questionIndex + 1);
                    } else {
                        path = pathAndQuery;
                    }
                } else {
                    host = urlWithoutProtocol;
                    path = "/";
                }
            }
        } catch (Exception e) {
            // Use defaults if parsing fails
        }

        // Format as raw HTTP request with proper line breaks
        if ("GET".equalsIgnoreCase(method)) {
            // For GET requests, add parameter to query string
            String fullPath = path;
            if (!queryString.isEmpty()) {
                fullPath += "?" + queryString + "&" + paramName + "=" + payload;
            } else {
                fullPath += "?" + paramName + "=" + payload;
            }

            poc.append(method).append(" ").append(fullPath).append(" HTTP/1.1<br/>");
            poc.append("Host: ").append(host).append("<br/>");
            poc.append("Content-Length: 0<br/>");
            poc.append("Cache-Control: max-age=0<br/>");
            poc.append("Upgrade-Insecure-Requests: 1<br/>");
            poc.append("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.112 Safari/537.36<br/>");
            poc.append("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7<br/>");
            poc.append("Accept-Encoding: gzip, deflate, br<br/>");
            poc.append("Accept-Language: en-US,en;q=0.9<br/>");
            poc.append("Connection: close<br/>");
            poc.append("<br/>");

        } else {
            // For POST/PUT/etc, add parameter to request body
            String postData;
            if (!queryString.isEmpty()) {
                // If there's existing query string, add it to the path
                path = path + "?" + queryString;
                postData = paramName + "=" + payload + "&goButton=go";
            } else {
                postData = paramName + "=" + payload + "&goButton=go";
            }

            poc.append(method).append(" ").append(path).append(" HTTP/1.1<br/>");
            poc.append("Host: ").append(host).append("<br/>");
            poc.append("Content-Length: ").append(postData.length()).append("<br/>");
            poc.append("Cache-Control: max-age=0<br/>");
            poc.append("Upgrade-Insecure-Requests: 1<br/>");
            poc.append("Origin: http://").append(host).append("<br/>");
            poc.append("Content-Type: application/x-www-form-urlencoded<br/>");
            poc.append("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.112 Safari/537.36<br/>");
            poc.append("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7<br/>");
            poc.append("Referer: http://").append(host).append(path.contains("/") ? path.substring(0, path.lastIndexOf("/")) : "").append("<br/>");
            poc.append("Accept-Encoding: gzip, deflate, br<br/>");
            poc.append("Accept-Language: en-US,en;q=0.9<br/>");
            poc.append("Connection: close<br/>");
            poc.append("<br/>");
            poc.append(postData);
        }

        return poc.toString();
    }

    /**
     * Simple URL encoding for special characters
     */
    private String urlEncode(String input) {
        if (input == null) return "";

        return input.replace(" ", "%20")
                   .replace("'", "%27")
                   .replace("\"", "%22")
                   .replace("+", "%2B")
                   .replace("&", "%26")
                   .replace("=", "%3D")
                   .replace("#", "%23")
                   .replace("?", "%3F");
    }
    
    /**
     * Calculate percentage (vulnerable/total)
     */
    private String calculatePercentage(int count, int total) {
        if (total == 0) return "0%";
        return String.format("%.1f%%", (count * 100.0) / total);
    }
    
    /**
     * Calculate percentage increase from baseline
     */
    private String calculatePercentageIncrease(long value, long baseline) {
        if (baseline == 0) return "N/A";
        return String.format("%.1f%% increase", ((value - baseline) * 100.0) / baseline);
    }
    
    /**
     * Truncate a string value for display
     */
    private String truncateValue(String value, int maxLength) {
        if (value == null) return "";
        if (value.length() <= maxLength) return value;
        return value.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * Escape HTML special characters
     */
    private String escapeHtml(String input) {
        if (input == null) {
            return "";
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }
}