package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IMessageEditor;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.scanner.ParallelScanner;
import com.timebasedscan.utils.HttpParameterParser;
import com.timebasedscan.utils.SqlErrorDetector;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dedicated SQL Error Detection Panel for quick SQL error discovery
 * Uses built-in payloads to test for SQL errors across all database types
 */
public class SQLErrorDetectionPanel extends JPanel {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private Object mainPanel;

    // UI Components
    private JTable requestsTable;
    private DefaultTableModel requestsTableModel;
    private IMessageEditor requestViewer;
    private IMessageEditor resultRequestViewer;
    private IMessageEditor resultResponseViewer;
    private JTextArea errorDetailsArea;
    private JTable parametersTable;
    private DefaultTableModel parametersTableModel;
    private JTable resultsTable;
    private DefaultTableModel resultsTableModel;
    private JButton scanButton;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    private JSpinner concurrencySpinner;
    private JCheckBox includeHeadersCheckbox;
    private JCheckBox includeUrlPathCheckbox;
    private JCheckBox includeParameterNamesCheckbox;

    // Database selection components
    private JCheckBox genericDbCheckbox;
    private JCheckBox mysqlCheckbox;
    private JCheckBox postgresqlCheckbox;
    private JCheckBox mssqlCheckbox;
    private JCheckBox oracleCheckbox;
    private JCheckBox sqliteCheckbox;

    // Data storage
    private List<IHttpRequestResponse> loadedRequests = new ArrayList<>();
    private Map<IHttpRequestResponse, List<HttpParameter>> requestParametersMap = new HashMap<>();
    private Map<IHttpRequestResponse, Map<HttpParameter, Boolean>> parameterSelectionMap = new HashMap<>();
    private List<TestResult> sqlErrorResults = new ArrayList<>();
    private IHttpRequestResponse currentlyDisplayedRequest;

    // Safe SQL error detection payloads organized by database type
    // NO DESTRUCTIVE OPERATIONS - Only syntax errors and safe information gathering

    private static final Map<String, String[]> DATABASE_PAYLOADS = new HashMap<String, String[]>() {{
        put("Generic", new String[] {
            // Basic syntax errors - safe for all databases
            "'", "\"", "`", "''", "\"\"", "``",
            "')", "\")", "'\"", "\"'", "'/*", "*/", "'#", "'--", "' --", "' #",
            "' OR '1'='1", "' OR 1=1--", "' OR 'a'='a", "' OR 'x'='x",
            "1' OR '1'='1", "1\" OR \"1\"=\"1", "1` OR `1`=`1",
            "admin'--", "admin\"--", "admin`--", "' OR 1=1#", "\" OR 1=1#",
            "') OR ('1'='1", "\") OR (\"1\"=\"1", "`) OR (`1`=`1",
            "' OR 1=1/*", "*/OR/**/1=1--"
        });

        put("MySQL", new String[] {
            // MySQL-specific safe error detection
            "'", "\"", "`", "')", "\")", "'#", "'--", "' --",
            "' OR '1'='1", "' OR 1=1--", "' OR 'a'='a",
            "' UNION SELECT NULL--", "' UNION SELECT 1--", "' UNION SELECT @@version--",
            "' AND 1=CONVERT(int, (SELECT @@version))--",
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "' AND 1=1/*", "*/AND/**/1=1--"
        });

        put("PostgreSQL", new String[] {
            // PostgreSQL-specific safe error detection
            "'", "\"", "'--", "' --", "';--",
            "' OR '1'='1", "' OR 1=1--", "' OR 'x'='x",
            "' UNION SELECT NULL::text--", "' UNION SELECT version()--",
            "' AND 1=CAST((SELECT version()) AS int)--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "' AND 1::int=1--", "' AND 'a'::text='a'--"
        });

        put("MSSQL", new String[] {
            // SQL Server-specific safe error detection
            "'", "\"", "'--", "' --", "';--",
            "' OR '1'='1", "' OR 1=1--", "' OR 'x'='x",
            "' UNION SELECT NULL--", "' UNION SELECT @@version--",
            "' AND 1=CAST((SELECT @@version) AS int)--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "' AND 1=1/*", "*/AND/**/1=1--"
        });

        put("Oracle", new String[] {
            // Oracle-specific safe error detection
            "'", "\"", "'--", "' --",
            "' OR '1'='1", "' OR 1=1--", "' OR 'x'='x",
            "' UNION SELECT NULL FROM dual--", "' UNION SELECT banner FROM v$version WHERE rownum=1--",
            "' AND 1=TO_NUMBER((SELECT banner FROM v$version WHERE rownum=1))--",
            "' AND (SELECT COUNT(*) FROM user_tables)>0--",
            "' AND ROWNUM=1--"
        });

        put("SQLite", new String[] {
            // SQLite-specific safe error detection
            "'", "\"", "'--", "' --",
            "' OR '1'='1", "' OR 1=1--", "' OR 'x'='x",
            "' UNION SELECT NULL--", "' UNION SELECT sqlite_version()--",
            "' AND 1=CAST((SELECT sqlite_version()) AS int)--",
            "' AND (SELECT COUNT(*) FROM sqlite_master)>0--"
        });
    }};

    public SQLErrorDetectionPanel(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, Object mainPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.mainPanel = mainPanel;
        
        initializeUI();
        setupEventHandlers();
    }

    private void initializeUI() {
        setLayout(new BorderLayout());
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // Create main split pane
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        mainSplitPane.setDividerLocation(400);

        // Left panel - Request selection and parameters
        JPanel leftPanel = createLeftPanel();
        mainSplitPane.setLeftComponent(leftPanel);

        // Right panel - Results
        JPanel rightPanel = createRightPanel();
        mainSplitPane.setRightComponent(rightPanel);

        add(mainSplitPane, BorderLayout.CENTER);

        // Bottom panel - Controls and status
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private JPanel createLeftPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("🔍 SQL Error Detection - Request Selection"));

        // Top section - Loaded requests
        JPanel requestsPanel = new JPanel(new BorderLayout());
        requestsPanel.setBorder(BorderFactory.createTitledBorder("Loaded Requests"));
        requestsPanel.setPreferredSize(new Dimension(0, 200));

        // Requests table
        String[] requestColumns = {"✓", "Host", "Method", "URL", "Parameters", "Status"};
        requestsTableModel = new DefaultTableModel(requestColumns, 0) {
            @Override
            public Class<?> getColumnClass(int column) {
                return column == 0 ? Boolean.class : String.class;
            }
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0;
            }
        };

        requestsTable = new JTable(requestsTableModel);
        requestsTable.getColumnModel().getColumn(0).setMaxWidth(30);
        requestsTable.getColumnModel().getColumn(2).setMaxWidth(80);
        requestsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        requestsTable.setRowHeight(22);

        JScrollPane requestsScrollPane = new JScrollPane(requestsTable);
        requestsPanel.add(requestsScrollPane, BorderLayout.CENTER);

        // Request viewer
        requestViewer = callbacks.createMessageEditor(null, false);
        JPanel viewerPanel = new JPanel(new BorderLayout());
        viewerPanel.setBorder(BorderFactory.createTitledBorder("Request Details"));
        viewerPanel.setPreferredSize(new Dimension(0, 150));
        viewerPanel.add(requestViewer.getComponent(), BorderLayout.CENTER);

        // Parameters section
        JPanel parametersPanel = createParametersPanel();

        // Combine sections
        JSplitPane leftSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        leftSplitPane.setTopComponent(requestsPanel);
        
        JSplitPane bottomSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        bottomSplitPane.setTopComponent(viewerPanel);
        bottomSplitPane.setBottomComponent(parametersPanel);
        bottomSplitPane.setDividerLocation(150);
        
        leftSplitPane.setBottomComponent(bottomSplitPane);
        leftSplitPane.setDividerLocation(200);

        panel.add(leftSplitPane, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createParametersPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Parameter Selection"));
        panel.setPreferredSize(new Dimension(0, 200));

        // Parameters table
        String[] paramColumns = {"✓", "Parameter", "Type", "Value", "Likelihood"};
        parametersTableModel = new DefaultTableModel(paramColumns, 0) {
            @Override
            public Class<?> getColumnClass(int column) {
                return column == 0 ? Boolean.class : String.class;
            }
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0;
            }
        };

        parametersTable = new JTable(parametersTableModel);
        parametersTable.getColumnModel().getColumn(0).setMaxWidth(30);
        parametersTable.getColumnModel().getColumn(2).setMaxWidth(80);
        parametersTable.getColumnModel().getColumn(4).setMaxWidth(100);
        parametersTable.setRowHeight(20);

        JScrollPane parametersScrollPane = new JScrollPane(parametersTable);
        panel.add(parametersScrollPane, BorderLayout.CENTER);

        // Enhanced selection buttons panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 5));
        buttonPanel.setBorder(BorderFactory.createTitledBorder("Smart Parameter Selection"));

        JButton selectAllBtn = new JButton("✅ All");
        selectAllBtn.setPreferredSize(new Dimension(70, 28));
        selectAllBtn.setToolTipText("Select all parameters for SQL error testing");
        selectAllBtn.addActionListener(e -> bulkSelectParameters("all"));

        JButton selectNoneBtn = new JButton("❌ None");
        selectNoneBtn.setPreferredSize(new Dimension(70, 28));
        selectNoneBtn.setToolTipText("Deselect all parameters");
        selectNoneBtn.addActionListener(e -> bulkSelectParameters("none"));

        JButton selectEmptyBtn = new JButton("🔍 Empty");
        selectEmptyBtn.setPreferredSize(new Dimension(80, 28));
        selectEmptyBtn.setToolTipText("Select only empty parameters (high SQL injection risk)");
        selectEmptyBtn.addActionListener(e -> bulkSelectParameters("empty"));

        JButton selectHighRiskBtn = new JButton("⚠️ High Risk");
        selectHighRiskBtn.setPreferredSize(new Dimension(100, 28));
        selectHighRiskBtn.setToolTipText("Select high-risk parameters for SQL injection");
        selectHighRiskBtn.addActionListener(e -> bulkSelectParameters("high-risk"));

        JButton selectSqlParamsBtn = new JButton("🗄️ SQL Names");
        selectSqlParamsBtn.setPreferredSize(new Dimension(100, 28));
        selectSqlParamsBtn.setToolTipText("Select parameters with SQL-related names");
        selectSqlParamsBtn.addActionListener(e -> bulkSelectParameters("sql-names"));

        JButton selectJsonBtn = new JButton("📋 JSON/API");
        selectJsonBtn.setPreferredSize(new Dimension(100, 28));
        selectJsonBtn.setToolTipText("Select JSON and API parameters");
        selectJsonBtn.addActionListener(e -> bulkSelectParameters("json-api"));

        buttonPanel.add(new JLabel("Quick:"));
        buttonPanel.add(selectAllBtn);
        buttonPanel.add(selectNoneBtn);
        buttonPanel.add(new JSeparator(JSeparator.VERTICAL));
        buttonPanel.add(selectEmptyBtn);
        buttonPanel.add(selectHighRiskBtn);
        buttonPanel.add(selectSqlParamsBtn);
        buttonPanel.add(selectJsonBtn);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    private JPanel createRightPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("🚨 SQL Error Detection Results"));

        // Create main split pane for results and request/response viewers
        JSplitPane rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        rightSplitPane.setDividerLocation(300);

        // Top section - Results table
        JPanel resultsPanel = new JPanel(new BorderLayout());
        resultsPanel.setBorder(BorderFactory.createTitledBorder("SQL Error Results"));

        // Results table
        String[] resultColumns = {"Parameter", "Type", "Payload", "Database", "Error Type", "Error Message", "Response Time"};
        resultsTableModel = new DefaultTableModel(resultColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        resultsTable = new JTable(resultsTableModel);
        resultsTable.setRowHeight(25);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Color coding for different database types
        resultsTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    String dbType = (String) table.getValueAt(row, 3);
                    if (dbType != null) {
                        switch (dbType.toLowerCase()) {
                            case "mysql":
                                c.setBackground(new Color(255, 240, 240)); // Light red
                                break;
                            case "postgresql":
                                c.setBackground(new Color(240, 240, 255)); // Light blue
                                break;
                            case "mssql":
                                c.setBackground(new Color(240, 255, 240)); // Light green
                                break;
                            case "oracle":
                                c.setBackground(new Color(255, 255, 240)); // Light yellow
                                break;
                            case "sqlite":
                                c.setBackground(new Color(255, 240, 255)); // Light magenta
                                break;
                            default:
                                c.setBackground(Color.WHITE);
                        }
                    }
                }
                return c;
            }
        });

        JScrollPane resultsScrollPane = new JScrollPane(resultsTable);
        resultsPanel.add(resultsScrollPane, BorderLayout.CENTER);

        // Bottom section - Request/Response viewers
        JPanel viewersPanel = createRequestResponseViewers();

        rightSplitPane.setTopComponent(resultsPanel);
        rightSplitPane.setBottomComponent(viewersPanel);

        panel.add(rightSplitPane, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createRequestResponseViewers() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("📋 Request & Response Details"));

        // Create tabbed pane for request and response
        JTabbedPane tabbedPane = new JTabbedPane();

        // Request viewer
        resultRequestViewer = callbacks.createMessageEditor(null, false);
        JPanel requestPanel = new JPanel(new BorderLayout());
        requestPanel.add(resultRequestViewer.getComponent(), BorderLayout.CENTER);
        tabbedPane.addTab("📤 Request", requestPanel);

        // Response viewer with error highlighting
        resultResponseViewer = callbacks.createMessageEditor(null, false);
        JPanel responsePanel = new JPanel(new BorderLayout());
        responsePanel.add(resultResponseViewer.getComponent(), BorderLayout.CENTER);

        // Add error highlighting info panel
        JPanel errorInfoPanel = new JPanel(new BorderLayout());
        errorInfoPanel.setBorder(BorderFactory.createTitledBorder("🚨 SQL Error Details"));
        errorInfoPanel.setPreferredSize(new Dimension(0, 80));

        errorDetailsArea = new JTextArea();
        errorDetailsArea.setEditable(false);
        errorDetailsArea.setFont(new Font("Monospaced", Font.PLAIN, 12));
        errorDetailsArea.setBackground(new Color(255, 245, 245));
        errorDetailsArea.setBorder(new EmptyBorder(5, 5, 5, 5));
        errorDetailsArea.setText("Select a SQL error result to view detailed error information...");
        errorInfoPanel.add(new JScrollPane(errorDetailsArea), BorderLayout.CENTER);

        JSplitPane responseSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        responseSplitPane.setTopComponent(responsePanel);
        responseSplitPane.setBottomComponent(errorInfoPanel);
        responseSplitPane.setDividerLocation(250);

        tabbedPane.addTab("📥 Response", responseSplitPane);

        panel.add(tabbedPane, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 0, 0, 0));

        // Main configuration panel with two sections
        JPanel mainConfigPanel = new JPanel(new BorderLayout());

        // Top section - Parameter and database configuration
        JPanel topConfigPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        topConfigPanel.setBorder(BorderFactory.createTitledBorder("🔧 SQL Error Detection Configuration"));

        // Parameter configuration
        topConfigPanel.add(new JLabel("Concurrency:"));
        concurrencySpinner = new JSpinner(new SpinnerNumberModel(10, 1, 50, 1));
        concurrencySpinner.setPreferredSize(new Dimension(60, 25));
        topConfigPanel.add(concurrencySpinner);

        includeHeadersCheckbox = new JCheckBox("Include Headers");
        includeHeadersCheckbox.setToolTipText("Test header parameters for SQL injection");
        topConfigPanel.add(includeHeadersCheckbox);

        includeUrlPathCheckbox = new JCheckBox("Include URL Path");
        includeUrlPathCheckbox.setToolTipText("Test URL path segments for SQL injection");
        topConfigPanel.add(includeUrlPathCheckbox);

        includeParameterNamesCheckbox = new JCheckBox("Include Parameter Names");
        includeParameterNamesCheckbox.setToolTipText("Test parameter names for SQL injection");
        includeParameterNamesCheckbox.addActionListener(e -> refreshParameterFiltering());
        topConfigPanel.add(includeParameterNamesCheckbox);

        // Add action listeners to other checkboxes for dynamic filtering
        includeHeadersCheckbox.addActionListener(e -> refreshParameterFiltering());
        includeUrlPathCheckbox.addActionListener(e -> refreshParameterFiltering());

        // Database selection panel
        JPanel databasePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        databasePanel.setBorder(BorderFactory.createTitledBorder("🗄️ Target Database Types (Safe Payloads Only)"));

        // Database checkboxes with default selections
        genericDbCheckbox = new JCheckBox("Generic", true);
        genericDbCheckbox.setToolTipText("Basic SQL syntax errors that work on all databases");
        databasePanel.add(genericDbCheckbox);

        mysqlCheckbox = new JCheckBox("MySQL", true);
        mysqlCheckbox.setToolTipText("MySQL-specific safe error detection payloads");
        databasePanel.add(mysqlCheckbox);

        postgresqlCheckbox = new JCheckBox("PostgreSQL", false);
        postgresqlCheckbox.setToolTipText("PostgreSQL-specific safe error detection payloads");
        databasePanel.add(postgresqlCheckbox);

        mssqlCheckbox = new JCheckBox("MSSQL", false);
        mssqlCheckbox.setToolTipText("Microsoft SQL Server-specific safe error detection payloads");
        databasePanel.add(mssqlCheckbox);

        oracleCheckbox = new JCheckBox("Oracle", false);
        oracleCheckbox.setToolTipText("Oracle-specific safe error detection payloads");
        databasePanel.add(oracleCheckbox);

        sqliteCheckbox = new JCheckBox("SQLite", false);
        sqliteCheckbox.setToolTipText("SQLite-specific safe error detection payloads");
        databasePanel.add(sqliteCheckbox);

        // Quick selection buttons
        JButton selectAllDbBtn = new JButton("✅ All");
        selectAllDbBtn.setPreferredSize(new Dimension(60, 25));
        selectAllDbBtn.setToolTipText("Select all database types");
        selectAllDbBtn.addActionListener(e -> {
            genericDbCheckbox.setSelected(true);
            mysqlCheckbox.setSelected(true);
            postgresqlCheckbox.setSelected(true);
            mssqlCheckbox.setSelected(true);
            oracleCheckbox.setSelected(true);
            sqliteCheckbox.setSelected(true);
        });
        databasePanel.add(selectAllDbBtn);

        JButton selectNoneDbBtn = new JButton("❌ None");
        selectNoneDbBtn.setPreferredSize(new Dimension(70, 25));
        selectNoneDbBtn.setToolTipText("Deselect all database types");
        selectNoneDbBtn.addActionListener(e -> {
            genericDbCheckbox.setSelected(false);
            mysqlCheckbox.setSelected(false);
            postgresqlCheckbox.setSelected(false);
            mssqlCheckbox.setSelected(false);
            oracleCheckbox.setSelected(false);
            sqliteCheckbox.setSelected(false);
        });
        databasePanel.add(selectNoneDbBtn);

        // Bottom section - Action buttons
        JPanel actionPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        // Scan button
        scanButton = new JButton("🚀 Start Safe SQL Error Detection");
        scanButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        scanButton.setBackground(new Color(34, 139, 34)); // Forest Green (safer color)
        scanButton.setForeground(Color.WHITE);
        scanButton.setPreferredSize(new Dimension(280, 35));
        scanButton.addActionListener(e -> startSQLErrorDetection());
        actionPanel.add(scanButton);

        // Add test button for debugging
        JButton testButton = new JButton("🧪 Test Add Request");
        testButton.setFont(new Font("SansSerif", Font.PLAIN, 12));
        testButton.setPreferredSize(new Dimension(150, 30));
        testButton.addActionListener(e -> testAddRequest());
        actionPanel.add(testButton);

        // Combine panels
        mainConfigPanel.add(topConfigPanel, BorderLayout.NORTH);
        mainConfigPanel.add(databasePanel, BorderLayout.CENTER);
        mainConfigPanel.add(actionPanel, BorderLayout.SOUTH);

        panel.add(mainConfigPanel, BorderLayout.CENTER);

        // Status panel
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusLabel = new JLabel("Ready for SQL error detection");
        statusLabel.setFont(statusLabel.getFont().deriveFont(Font.ITALIC));
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);

        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(progressBar, BorderLayout.CENTER);

        panel.add(statusPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void setupEventHandlers() {
        // Request table selection
        requestsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = requestsTable.getSelectedRow();
                if (selectedRow >= 0 && selectedRow < loadedRequests.size()) {
                    IHttpRequestResponse selectedRequest = loadedRequests.get(selectedRow);
                    currentlyDisplayedRequest = selectedRequest; // Set the currently displayed request
                    requestViewer.setMessage(selectedRequest.getRequest(), true);
                    updateParameterTable(selectedRequest);
                }
            }
        });

        // Results table selection - Show request/response details
        resultsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = resultsTable.getSelectedRow();
                if (selectedRow >= 0 && selectedRow < sqlErrorResults.size()) {
                    TestResult result = sqlErrorResults.get(selectedRow);

                    // Show the request that was sent for this test
                    if (result.getRequest() != null) {
                        resultRequestViewer.setMessage(result.getRequest(), true);
                    }

                    // Show the response with error highlighting
                    if (result.getResponse() != null) {
                        resultResponseViewer.setMessage(result.getResponse(), false);

                        // Update error details area with highlighted information
                        updateErrorDetails(result);

                        // Log the selection for debugging
                        callbacks.printOutput("SQL Error Result Selected: " +
                            result.getParameterName() + " -> " + result.getErrorType() +
                            " (" + result.getResponseTime() + "ms)");
                    }
                } else {
                    // Clear viewers when no selection
                    resultRequestViewer.setMessage(new byte[0], true);
                    resultResponseViewer.setMessage(new byte[0], false);
                    errorDetailsArea.setText("Select a SQL error result to view detailed error information...");
                }
            }
        });

        // Scan button
        scanButton.addActionListener(e -> startSQLErrorDetection());
    }

    /**
     * Update the error details area with highlighted SQL error information
     */
    private void updateErrorDetails(TestResult result) {
        if (result == null) {
            errorDetailsArea.setText("No error details available.");
            return;
        }

        StringBuilder details = new StringBuilder();

        // Header with parameter and payload info
        details.append("🚨 SQL ERROR DETECTED 🚨\n");
        details.append("═══════════════════════════════════════════════════════════════\n\n");

        details.append("📍 INJECTION POINT:\n");
        details.append("   Parameter: ").append(result.getParameterName()).append("\n");
        details.append("   Type: ").append(result.getParameterType()).append("\n");
        details.append("   Payload: ").append(result.getPayload()).append("\n\n");

        details.append("🗄️ DATABASE INFORMATION:\n");
        details.append("   Database Type: ").append(result.getDatabaseType() != null ? result.getDatabaseType() : "Unknown").append("\n");
        details.append("   Error Type: ").append(result.getErrorType() != null ? result.getErrorType() : "Generic SQL Error").append("\n");
        details.append("   Response Time: ").append(result.getResponseTime()).append("ms\n\n");

        details.append("💥 ERROR MESSAGE:\n");
        details.append("───────────────────────────────────────────────────────────────\n");
        String errorMsg = result.getErrorMessage();
        if (errorMsg != null && !errorMsg.isEmpty()) {
            // Highlight common SQL error keywords
            String highlightedError = highlightSQLErrorKeywords(errorMsg);
            details.append(highlightedError).append("\n");
        } else {
            details.append("Error message not captured or empty.\n");
        }

        details.append("───────────────────────────────────────────────────────────────\n\n");

        details.append("🔍 ANALYSIS:\n");
        details.append("   • This parameter is vulnerable to SQL injection\n");
        details.append("   • The database returned an error message revealing internal structure\n");
        details.append("   • Further exploitation may be possible\n");
        details.append("   • Consider testing with more advanced payloads\n\n");

        details.append("⚠️ RECOMMENDATION:\n");
        details.append("   • Implement proper input validation and sanitization\n");
        details.append("   • Use parameterized queries or prepared statements\n");
        details.append("   • Apply principle of least privilege to database connections\n");
        details.append("   • Consider implementing a Web Application Firewall (WAF)\n");

        errorDetailsArea.setText(details.toString());
        errorDetailsArea.setCaretPosition(0); // Scroll to top
    }

    /**
     * Highlight SQL error keywords in the error message
     */
    private String highlightSQLErrorKeywords(String errorMessage) {
        if (errorMessage == null) return "";

        // Add visual markers around common SQL error keywords
        String highlighted = errorMessage;

        // Common SQL error keywords to highlight
        String[] keywords = {
            "syntax error", "mysql", "postgresql", "oracle", "mssql", "sqlite",
            "table", "column", "database", "select", "insert", "update", "delete",
            "where", "from", "join", "union", "order by", "group by",
            "error", "exception", "warning", "fatal", "access denied",
            "unknown column", "unknown table", "duplicate entry",
            "constraint", "foreign key", "primary key", "index"
        };

        for (String keyword : keywords) {
            // Case-insensitive replacement with visual markers
            highlighted = highlighted.replaceAll("(?i)" + keyword, ">>> " + keyword.toUpperCase() + " <<<");
        }

        return highlighted;
    }

    /**
     * Get selected database payloads based on checkbox selections
     */
    private List<String> getSelectedPayloads() {
        List<String> selectedPayloads = new ArrayList<>();

        if (genericDbCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("Generic")) {
                selectedPayloads.add(payload);
            }
        }

        if (mysqlCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("MySQL")) {
                selectedPayloads.add(payload);
            }
        }

        if (postgresqlCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("PostgreSQL")) {
                selectedPayloads.add(payload);
            }
        }

        if (mssqlCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("MSSQL")) {
                selectedPayloads.add(payload);
            }
        }

        if (oracleCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("Oracle")) {
                selectedPayloads.add(payload);
            }
        }

        if (sqliteCheckbox.isSelected()) {
            for (String payload : DATABASE_PAYLOADS.get("SQLite")) {
                selectedPayloads.add(payload);
            }
        }

        // If no databases selected, default to Generic for safety
        if (selectedPayloads.isEmpty()) {
            callbacks.printOutput("No database types selected, defaulting to Generic safe payloads");
            for (String payload : DATABASE_PAYLOADS.get("Generic")) {
                selectedPayloads.add(payload);
            }
        }

        return selectedPayloads;
    }

    /**
     * Get count of selected payloads for progress calculation
     */
    private int getSelectedPayloadCount() {
        return getSelectedPayloads().size();
    }

    /**
     * Add a request for SQL error detection
     */
    public void addRequest(IHttpRequestResponse request) {
        if (request == null) {
            callbacks.printError("SQL Error Detection: Cannot add null request");
            return;
        }

        try {
            callbacks.printOutput("SQL Error Detection: Adding request from " + request.getHttpService().getHost());

            // Check for duplicates
            for (IHttpRequestResponse existingRequest : loadedRequests) {
                if (isRequestEqual(existingRequest, request)) {
                    callbacks.printOutput("SQL Error Detection: Duplicate request detected - skipping");
                    JOptionPane.showMessageDialog(this,
                        "This request is already loaded for SQL error detection.",
                        "Duplicate Request", JOptionPane.INFORMATION_MESSAGE);
                    return;
                }
            }

            loadedRequests.add(request);

            // Parse parameters with error handling
            HttpParameterParser parser = new HttpParameterParser(helpers);
            List<HttpParameter> parameters = parser.parseParameters(request);
            callbacks.printOutput("SQL Error Detection: Parsed " + parameters.size() + " parameters");

            // Filter parameters based on settings
            List<HttpParameter> filteredParameters = filterParameters(parameters);
            callbacks.printOutput("SQL Error Detection: Filtered to " + filteredParameters.size() + " parameters");
            requestParametersMap.put(request, filteredParameters);
        
        // Initialize parameter selections with smart defaults (like Mass Testing)
        Map<HttpParameter, Boolean> paramSelections = new HashMap<>();
        int highRiskCount = 0;
        for (HttpParameter param : filteredParameters) {
            // Smart selection: auto-select high-risk parameters and empty parameters
            String likelihood = assessSQLInjectionLikelihood(param);
            boolean autoSelect = likelihood.equals("High") || likelihood.equals("Medium-High") || param.isEmpty();
            paramSelections.put(param, autoSelect);
            if (autoSelect) highRiskCount++;
        }
        parameterSelectionMap.put(request, paramSelections);

        // Log parameter analysis
        callbacks.printOutput("Parameter analysis: " + filteredParameters.size() + " total, " +
                            highRiskCount + " auto-selected (high-risk/empty)");

        // Add to table
        IHttpService service = request.getHttpService();
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        
        requestsTableModel.addRow(new Object[] {
            Boolean.TRUE,
            service.getHost(),
            requestInfo.getMethod(),
            requestInfo.getUrl().getPath(),
            filteredParameters.size() + " params (" + highRiskCount + " high-risk)",
            "Ready"
        });

            callbacks.printOutput("Added request for SQL error detection: " +
                                requestInfo.getMethod() + " " + requestInfo.getUrl().getPath() +
                                " (" + filteredParameters.size() + " parameters, " + highRiskCount + " high-risk)");

            // Auto-select the newly added request in the table
            SwingUtilities.invokeLater(() -> {
                int lastRow = requestsTableModel.getRowCount() - 1;
                if (lastRow >= 0) {
                    requestsTable.setRowSelectionInterval(lastRow, lastRow);
                    requestsTable.scrollRectToVisible(requestsTable.getCellRect(lastRow, 0, true));
                }
            });

        } catch (Exception e) {
            callbacks.printError("SQL Error Detection: Error adding request - " + e.getMessage());
            e.printStackTrace();

            // Remove the request if it was partially added
            if (loadedRequests.contains(request)) {
                loadedRequests.remove(request);
                requestParametersMap.remove(request);
                parameterSelectionMap.remove(request);
            }

            JOptionPane.showMessageDialog(this,
                "Error adding request to SQL Error Detection:\n" + e.getMessage(),
                "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean isRequestEqual(IHttpRequestResponse req1, IHttpRequestResponse req2) {
        if (req1 == req2) return true;
        if (req1 == null || req2 == null) return false;
        
        IRequestInfo info1 = helpers.analyzeRequest(req1);
        IRequestInfo info2 = helpers.analyzeRequest(req2);
        
        return info1.getUrl().equals(info2.getUrl()) &&
               info1.getMethod().equals(info2.getMethod());
    }

    private List<HttpParameter> filterParameters(List<HttpParameter> parameters) {
        List<HttpParameter> filtered = new ArrayList<>();

        for (HttpParameter param : parameters) {
            // Filter out synthetic parameters that shouldn't be displayed
            if (param.getName().startsWith("__EXTRACTED_")) {
                continue; // Skip extracted parameters
            }

            // Always include core parameter types
            if (param.getType() == HttpParameter.Type.BODY ||
                param.getType() == HttpParameter.Type.URL ||
                param.getType() == HttpParameter.Type.JSON ||
                param.getType() == HttpParameter.Type.XML ||
                param.getType() == HttpParameter.Type.MULTIPART ||
                param.getType() == HttpParameter.Type.GRAPHQL ||
                param.getType() == HttpParameter.Type.WEBSOCKET ||
                param.getType() == HttpParameter.Type.GRPC ||
                param.getType() == HttpParameter.Type.PROTOBUF ||
                param.getType() == HttpParameter.Type.MESSAGEPACK ||
                param.getType() == HttpParameter.Type.CBOR ||
                param.getType() == HttpParameter.Type.AVRO ||
                param.getType() == HttpParameter.Type.SSE ||
                param.getType() == HttpParameter.Type.YAML ||
                param.getType() == HttpParameter.Type.TOML ||
                param.getType() == HttpParameter.Type.INI ||
                param.getType() == HttpParameter.Type.TEMPLATE ||
                param.getType() == HttpParameter.Type.HANDLEBARS ||
                param.getType() == HttpParameter.Type.JINJA2 ||
                param.getType() == HttpParameter.Type.JWT ||
                param.getType() == HttpParameter.Type.BASE64 ||
                param.getType() == HttpParameter.Type.BINARY ||
                param.getType() == HttpParameter.Type.REST_PATH ||
                param.getType() == HttpParameter.Type.AUTH ||
                param.getType() == HttpParameter.Type.STRUCTURED_HEADER ||
                param.getType() == HttpParameter.Type.NESTED_FORM ||
                param.getType() == HttpParameter.Type.FILE_UPLOAD) {
                filtered.add(param);
            }

            // Include headers if enabled (but filter out common non-injectable headers)
            if (includeHeadersCheckbox.isSelected() && param.getType() == HttpParameter.Type.HEADER) {
                String headerName = param.getName().toLowerCase();
                // Skip headers that are typically not injectable
                if (!headerName.equals("content-length") &&
                    !headerName.equals("content-type") &&
                    !headerName.equals("host") &&
                    !headerName.equals("connection") &&
                    !headerName.equals("accept-encoding") &&
                    !headerName.equals("cache-control") &&
                    !headerName.startsWith("sec-")) {
                    filtered.add(param);
                }
            }

            // Include URL path if enabled
            if (includeUrlPathCheckbox.isSelected() && param.getType() == HttpParameter.Type.URL_PATH) {
                filtered.add(param);
            }

            // Include parameter names if enabled
            if (includeParameterNamesCheckbox.isSelected()) {
                // Create a synthetic parameter for name injection
                HttpParameter nameParam = new HttpParameter(param.getName() + "__NAME__", param.getValue(), param.getType());
                filtered.add(nameParam);
            }
        }

        return filtered;
    }

    private void updateParameterTable(IHttpRequestResponse request) {
        parametersTableModel.setRowCount(0);

        List<HttpParameter> parameters = requestParametersMap.get(request);
        if (parameters == null) return;

        Map<HttpParameter, Boolean> selections = parameterSelectionMap.get(request);
        if (selections == null) return;

        for (HttpParameter param : parameters) {
            Boolean selected = selections.get(param);
            String likelihood = assessSQLInjectionLikelihood(param);

            parametersTableModel.addRow(new Object[] {
                selected,
                param.getName(),
                param.getType().toString(),
                param.getValue().length() > 50 ? param.getValue().substring(0, 50) + "..." : param.getValue(),
                likelihood
            });
        }
    }

    private String assessSQLInjectionLikelihood(HttpParameter param) {
        String name = param.getName().toLowerCase();
        String type = param.getType().toString();
        boolean isEmpty = param.isEmpty();

        // High likelihood factors for SQL injection
        if (isEmpty) {
            return "High"; // Empty parameters often bypass validation
        }

        // Check for SQL-related parameter names
        if (name.contains("id") || name.contains("user") || name.contains("search") ||
            name.contains("query") || name.contains("sql") || name.contains("db") ||
            name.contains("table") || name.contains("where") || name.contains("order") ||
            name.contains("sort") || name.contains("filter") || name.contains("select")) {
            return "High";
        }

        // Check for high-risk parameter types
        if (type.equals("BODY") || type.equals("JSON") || type.equals("URL")) {
            return "Medium-High";
        }

        // Check for medium-risk parameter names
        if (name.contains("name") || name.contains("value") || name.contains("data") ||
            name.contains("input") || name.contains("param") || name.contains("field") ||
            name.contains("text") || name.contains("content")) {
            return "Medium";
        }

        // Header and cookie parameters
        if (type.equals("HEADER") || type.equals("COOKIE")) {
            return "Low";
        }

        return "Low";
    }

    private void startSQLErrorDetection() {
        // Validate database selection
        if (!genericDbCheckbox.isSelected() && !mysqlCheckbox.isSelected() &&
            !postgresqlCheckbox.isSelected() && !mssqlCheckbox.isSelected() &&
            !oracleCheckbox.isSelected() && !sqliteCheckbox.isSelected()) {
            JOptionPane.showMessageDialog(this,
                "Please select at least one database type to test.\n\n" +
                "Recommended: Start with 'Generic' for basic SQL error detection.",
                "No Database Types Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Get selected requests
        List<IHttpRequestResponse> selectedRequests = new ArrayList<>();
        for (int i = 0; i < requestsTableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) requestsTableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                selectedRequests.add(loadedRequests.get(i));
            }
        }

        if (selectedRequests.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please select at least one request for SQL error detection.",
                "No Requests Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Clear previous results
        resultsTableModel.setRowCount(0);
        sqlErrorResults.clear();

        // Start detection in background thread
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                scanButton.setEnabled(false);
                progressBar.setVisible(true);
                progressBar.setIndeterminate(true);

                publish("Starting SQL error detection...");

                int totalTests = 0;
                int currentTest = 0;

                // Get selected payloads based on database selection
                List<String> selectedPayloads = getSelectedPayloads();
                callbacks.printOutput("Using " + selectedPayloads.size() + " safe SQL error detection payloads");

                // Calculate total tests
                for (IHttpRequestResponse request : selectedRequests) {
                    List<HttpParameter> parameters = getSelectedParameters(request);
                    totalTests += parameters.size() * selectedPayloads.size();
                }

                progressBar.setIndeterminate(false);
                progressBar.setMaximum(totalTests);

                // Test each request
                for (IHttpRequestResponse request : selectedRequests) {
                    List<HttpParameter> parameters = getSelectedParameters(request);

                    publish("Testing " + parameters.size() + " parameters in " +
                           helpers.analyzeRequest(request).getUrl().getPath());

                    // Test each parameter with each payload
                    for (HttpParameter parameter : parameters) {
                        for (String payload : selectedPayloads) {
                            if (isCancelled()) return null;

                            currentTest++;
                            progressBar.setValue(currentTest);

                            publish("Testing " + parameter.getName() + " with safe payload: " +
                                   (payload.length() > 20 ? payload.substring(0, 20) + "..." : payload));

                            // Test for SQL errors
                            TestResult result = testForSQLError(request, parameter, payload);
                            if (result != null && result.isVulnerable()) {
                                sqlErrorResults.add(result);

                                // Add to results table
                                SwingUtilities.invokeLater(() -> {
                                    resultsTableModel.addRow(new Object[] {
                                        result.getParameterName(),
                                        result.getParameterType(),
                                        payload.length() > 30 ? payload.substring(0, 30) + "..." : payload,
                                        result.getDatabaseType(),
                                        result.getErrorType(),
                                        result.getErrorMessage().length() > 100 ?
                                            result.getErrorMessage().substring(0, 100) + "..." : result.getErrorMessage(),
                                        result.getResponseTime() + "ms"
                                    });
                                });
                            }

                            // Rate limiting bypass is applied within testForSQLError method
                            // No additional delay needed here
                        }
                    }
                }

                return null;
            }

            @Override
            protected void process(List<String> chunks) {
                for (String message : chunks) {
                    statusLabel.setText(message);
                }
            }

            @Override
            protected void done() {
                scanButton.setEnabled(true);
                progressBar.setVisible(false);

                int errorCount = sqlErrorResults.size();
                statusLabel.setText("SQL error detection completed. Found " + errorCount + " SQL errors.");

                if (errorCount > 0) {
                    JOptionPane.showMessageDialog(SQLErrorDetectionPanel.this,
                        "SQL error detection completed!\n\n" +
                        "Found " + errorCount + " SQL errors.\n" +
                        "Check the results table for details.",
                        "SQL Errors Detected", JOptionPane.WARNING_MESSAGE);
                } else {
                    JOptionPane.showMessageDialog(SQLErrorDetectionPanel.this,
                        "SQL error detection completed.\n\n" +
                        "No SQL errors were detected in the tested parameters.",
                        "No SQL Errors Found", JOptionPane.INFORMATION_MESSAGE);
                }
            }
        };

        worker.execute();
    }

    private List<HttpParameter> getSelectedParameters(IHttpRequestResponse request) {
        List<HttpParameter> selectedParams = new ArrayList<>();
        List<HttpParameter> allParams = requestParametersMap.get(request);
        Map<HttpParameter, Boolean> selections = parameterSelectionMap.get(request);

        if (allParams != null && selections != null) {
            for (HttpParameter param : allParams) {
                Boolean selected = selections.get(param);
                if (selected != null && selected) {
                    selectedParams.add(param);
                }
            }
        }

        return selectedParams;
    }

    private TestResult testForSQLError(IHttpRequestResponse request, HttpParameter parameter, String payload) {
        try {
            // Create SQL error detector
            SqlErrorDetector detector = new SqlErrorDetector(callbacks, helpers);

            // Configure rate limiting bypass for SQL error detection
            // Use default settings for SQL error detection (conservative approach)
            detector.configureRateLimitBypass(
                true,  // Enable rate limiting bypass for SQL error detection
                "Adaptive Delay (Recommended)", // Use adaptive delay method
                500,   // Conservative 500ms base delay for SQL error testing
                true,  // Enable User-Agent randomization
                true,  // Enable X-Forwarded-For randomization
                false  // Proxy rotation not implemented
            );

            // Configure custom header injection for SQL error detection (disabled by default)
            detector.configureCustomHeaderInjection(
                false, // Disable custom header injection for SQL error detection by default
                "",    // No custom header name
                ""     // No custom header value
            );

            // Test the parameter with the payload
            return detector.testForSQLError(request, parameter, payload);

        } catch (Exception e) {
            callbacks.printError("Error testing for SQL error: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get the number of loaded requests
     */
    public int getRequestCount() {
        return loadedRequests.size();
    }

    /**
     * Get the number of SQL errors found
     */
    public int getErrorCount() {
        return sqlErrorResults.size();
    }

    /**
     * Clear all loaded requests and results
     */
    public void clearAll() {
        loadedRequests.clear();
        requestParametersMap.clear();
        parameterSelectionMap.clear();
        sqlErrorResults.clear();
        requestsTableModel.setRowCount(0);
        parametersTableModel.setRowCount(0);
        resultsTableModel.setRowCount(0);
        statusLabel.setText("Ready for SQL error detection");
    }

    /**
     * Export results to a report
     */
    public String generateReport() {
        StringBuilder report = new StringBuilder();
        report.append("SQL Error Detection Report\n");
        report.append("Generated: ").append(new java.util.Date()).append("\n");
        report.append("Total Requests Tested: ").append(loadedRequests.size()).append("\n");
        report.append("Total SQL Errors Found: ").append(sqlErrorResults.size()).append("\n\n");

        if (!sqlErrorResults.isEmpty()) {
            report.append("SQL ERRORS DETECTED:\n");
            report.append("===================\n\n");

            for (int i = 0; i < sqlErrorResults.size(); i++) {
                TestResult result = sqlErrorResults.get(i);
                report.append("Error #").append(i + 1).append(":\n");
                report.append("Parameter: ").append(result.getParameterName()).append("\n");
                report.append("Type: ").append(result.getParameterType()).append("\n");
                report.append("Database: ").append(result.getDatabaseType()).append("\n");
                report.append("Error Type: ").append(result.getErrorType()).append("\n");
                report.append("Error Message: ").append(result.getErrorMessage()).append("\n");
                report.append("Response Time: ").append(result.getResponseTime()).append("ms\n");
                report.append("Payload Used: ").append(result.getPayload()).append("\n");
                report.append("\n");
            }
        } else {
            report.append("No SQL errors were detected.\n");
        }

        return report.toString();
    }

    /**
     * Bulk parameter selection based on criteria (enhanced for SQL error detection)
     */
    private void bulkSelectParameters(String selectionType) {
        if (currentlyDisplayedRequest == null) return;

        List<HttpParameter> parameters = requestParametersMap.get(currentlyDisplayedRequest);
        if (parameters == null) return;

        Map<HttpParameter, Boolean> paramSelections = parameterSelectionMap.get(currentlyDisplayedRequest);
        if (paramSelections == null) {
            paramSelections = new HashMap<>();
            parameterSelectionMap.put(currentlyDisplayedRequest, paramSelections);
        }

        int selectedCount = 0;

        for (int i = 0; i < parameters.size(); i++) {
            HttpParameter param = parameters.get(i);
            boolean shouldSelect = false;

            switch (selectionType.toLowerCase()) {
                case "all":
                    shouldSelect = true;
                    break;

                case "none":
                    shouldSelect = false;
                    break;

                case "empty":
                    shouldSelect = param.isEmpty();
                    break;

                case "high-risk":
                    String likelihood = assessSQLInjectionLikelihood(param);
                    shouldSelect = likelihood.equals("High") || likelihood.equals("Medium-High");
                    break;

                case "sql-names":
                    String name = param.getName().toLowerCase();
                    shouldSelect = name.contains("id") || name.contains("user") || name.contains("search") ||
                                 name.contains("query") || name.contains("sql") || name.contains("db") ||
                                 name.contains("table") || name.contains("where") || name.contains("order") ||
                                 name.contains("sort") || name.contains("filter") || name.contains("select") ||
                                 name.contains("insert") || name.contains("update") || name.contains("delete");
                    break;

                case "json-api":
                    String type = param.getType().toString();
                    shouldSelect = type.equals("JSON") || type.equals("GRAPHQL") || type.equals("REST_PATH") ||
                                 type.equals("AUTH") || type.equals("JWT") || type.equals("GRPC") ||
                                 type.equals("PROTOBUF") || type.equals("MESSAGEPACK") || type.equals("CBOR");
                    break;
            }

            paramSelections.put(param, shouldSelect);

            // Update the table model
            if (i < parametersTableModel.getRowCount()) {
                parametersTableModel.setValueAt(shouldSelect, i, 0);
            }

            if (shouldSelect) selectedCount++;
        }

        // Force table refresh
        parametersTable.revalidate();
        parametersTable.repaint();

        callbacks.printOutput("SQL Error Detection - Bulk selection (" + selectionType + "): " +
                            selectedCount + "/" + parameters.size() + " parameters selected");
    }

    /**
     * Refresh parameter filtering when checkboxes change (like Mass Testing)
     */
    private void refreshParameterFiltering() {
        if (loadedRequests.isEmpty()) return;

        callbacks.printOutput("Refreshing parameter filtering based on current settings...");

        // Re-parse and filter all loaded requests
        for (IHttpRequestResponse request : loadedRequests) {
            // Parse parameters again
            HttpParameterParser parser = new HttpParameterParser(helpers);
            List<HttpParameter> allParameters = parser.parseParameters(request);

            // Apply current filtering
            List<HttpParameter> filteredParameters = filterParameters(allParameters);
            requestParametersMap.put(request, filteredParameters);

            // Update parameter selections (preserve existing selections where possible)
            Map<HttpParameter, Boolean> existingSelections = parameterSelectionMap.get(request);
            Map<HttpParameter, Boolean> newSelections = new HashMap<>();

            for (HttpParameter param : filteredParameters) {
                // Try to preserve existing selection, otherwise use smart default
                Boolean existingSelection = null;
                if (existingSelections != null) {
                    // Look for existing selection by name and type
                    for (Map.Entry<HttpParameter, Boolean> entry : existingSelections.entrySet()) {
                        if (entry.getKey().getName().equals(param.getName()) &&
                            entry.getKey().getType() == param.getType()) {
                            existingSelection = entry.getValue();
                            break;
                        }
                    }
                }

                if (existingSelection != null) {
                    newSelections.put(param, existingSelection);
                } else {
                    // Use smart default for new parameters
                    String likelihood = assessSQLInjectionLikelihood(param);
                    boolean autoSelect = likelihood.equals("High") || likelihood.equals("Medium-High") || param.isEmpty();
                    newSelections.put(param, autoSelect);
                }
            }

            parameterSelectionMap.put(request, newSelections);
        }

        // Update the requests table to show new parameter counts
        updateRequestsTable();

        // Update parameter table if a request is currently selected
        if (currentlyDisplayedRequest != null) {
            updateParameterTable(currentlyDisplayedRequest);
        }

        callbacks.printOutput("Parameter filtering refreshed. Updated " + loadedRequests.size() + " requests.");
    }

    /**
     * Update the requests table with current data
     */
    private void updateRequestsTable() {
        requestsTableModel.setRowCount(0);

        for (IHttpRequestResponse request : loadedRequests) {
            IHttpService service = request.getHttpService();
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<HttpParameter> parameters = requestParametersMap.get(request);

            if (parameters != null) {
                // Count high-risk parameters
                int highRiskCount = 0;
                for (HttpParameter param : parameters) {
                    String likelihood = assessSQLInjectionLikelihood(param);
                    if (likelihood.equals("High") || likelihood.equals("Medium-High") || param.isEmpty()) {
                        highRiskCount++;
                    }
                }

                requestsTableModel.addRow(new Object[] {
                    Boolean.TRUE,
                    service.getHost(),
                    requestInfo.getMethod(),
                    requestInfo.getUrl().getPath(),
                    parameters.size() + " params (" + highRiskCount + " high-risk)",
                    "Ready"
                });
            }
        }
    }

    /**
     * Helper method to find component by name (for table access)
     */
    private Component findComponentByName(Container parent, String name) {
        for (Component component : parent.getComponents()) {
            if (name.equals(component.getName())) {
                return component;
            }
            if (component instanceof Container) {
                Component found = findComponentByName((Container) component, name);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * Test method to add a dummy request for debugging
     */
    private void testAddRequest() {
        try {
            // Create a simple test request
            String testRequest = "GET /test?id=123&name=test HTTP/1.1\r\n" +
                               "Host: example.com\r\n" +
                               "User-Agent: Mozilla/5.0\r\n" +
                               "Accept: text/html\r\n" +
                               "\r\n";

            byte[] requestBytes = testRequest.getBytes();

            // Create a mock HTTP service
            IHttpService mockService = new IHttpService() {
                @Override
                public String getHost() { return "example.com"; }
                @Override
                public int getPort() { return 80; }
                @Override
                public String getProtocol() { return "http"; }
            };

            // Create a mock request response
            IHttpRequestResponse mockRequest = new IHttpRequestResponse() {
                @Override
                public byte[] getRequest() { return requestBytes; }
                @Override
                public void setRequest(byte[] message) {}
                @Override
                public byte[] getResponse() { return null; }
                @Override
                public void setResponse(byte[] message) {}
                @Override
                public String getComment() { return null; }
                @Override
                public void setComment(String comment) {}
                @Override
                public String getHighlight() { return null; }
                @Override
                public void setHighlight(String color) {}
                @Override
                public IHttpService getHttpService() { return mockService; }
                @Override
                public void setHttpService(IHttpService httpService) {}
            };

            // Add the test request
            addRequest(mockRequest);

            callbacks.printOutput("Test request added successfully!");

        } catch (Exception e) {
            callbacks.printError("Error adding test request: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
