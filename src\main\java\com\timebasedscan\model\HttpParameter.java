package com.timebasedscan.model;

import java.util.Objects;

/**
 * Model class for storing HTTP parameter information
 * Used to provide a consistent parameter model across the application
 */
public class HttpParameter {

    public enum Type {
        URL,
        BODY,
        COOKIE,
        HEADER,
        XML,
        JSON,
        MULTIPART,
        URL_PATH,
        GRAPHQL,
        WEBSOCKET,
        GRPC,
        PROTOBUF,
        MESSAGEPACK,
        CBOR,
        AVRO,
        SSE,
        YAML,
        TOML,
        INI,
        TEMPLATE,
        HANDLEBARS,
        JINJA2,
        JWT,
        BASE64,
        BINARY,
        REST_PATH,
        AUTH,
        STRUCTURED_HEADER,
        NESTED_FORM,
        FILE_UPLOAD
    }
    
    private String name;
    private String value;
    private Type type;
    
    public HttpParameter(String name, String value, Type type) {
        this.name = name;
        this.value = value;
        this.type = type;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public Type getType() {
        return type;
    }
    
    public void setType(Type type) {
        this.type = type;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HttpParameter that = (HttpParameter) o;
        return Objects.equals(name, that.name) &&
               Objects.equals(type, that.type);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, type);
    }
    
    /**
     * Check if parameter value is empty
     * @return true if value is null or empty
     */
    public boolean isEmpty() {
        return value == null || value.trim().isEmpty();
    }
    
    /**
     * Get Burp parameter type equivalent
     * @return Burp parameter type value
     */
    public byte getBurpType() {
        switch (type) {
            case URL:
                return burp.IParameter.PARAM_URL;
            case BODY:
                return burp.IParameter.PARAM_BODY;
            case COOKIE:
                return burp.IParameter.PARAM_COOKIE;
            case XML:
                return burp.IParameter.PARAM_XML;
            case JSON:
                return burp.IParameter.PARAM_JSON;
            case MULTIPART:
                return burp.IParameter.PARAM_MULTIPART_ATTR;
            case HEADER:
                // Headers don't have a direct Burp parameter type, use a special value
                return -1; // Special value to indicate header type
            case URL_PATH:
                // URL paths don't have a direct Burp parameter type, use a special value
                return -2; // Special value to indicate URL path type
            case GRAPHQL:
                // GraphQL doesn't have a direct Burp parameter type, use a special value
                return -3; // Special value to indicate GraphQL type
            case WEBSOCKET:
                return -4; // Special value for WebSocket
            case GRPC:
            case PROTOBUF:
                return -5; // Special value for gRPC/Protobuf
            case MESSAGEPACK:
            case CBOR:
            case AVRO:
            case BINARY:
                return -6; // Special value for binary formats
            case SSE:
                return -7; // Special value for Server-Sent Events
            case YAML:
            case TOML:
            case INI:
                return -8; // Special value for configuration formats
            case TEMPLATE:
            case HANDLEBARS:
            case JINJA2:
                return -9; // Special value for template formats
            case JWT:
                return -10; // Special value for JWT tokens
            case BASE64:
                return -11; // Special value for Base64 encoded data
            case REST_PATH:
                return -12; // Special value for REST path parameters
            case AUTH:
                return -13; // Special value for authentication parameters
            case STRUCTURED_HEADER:
                return -14; // Special value for structured headers
            case NESTED_FORM:
                return -15; // Special value for nested form data
            case FILE_UPLOAD:
                return -16; // Special value for file uploads
            default:
                return burp.IParameter.PARAM_BODY;
        }
    }
    
    @Override
    public String toString() {
        return name + " (" + type + ")";
    }
}