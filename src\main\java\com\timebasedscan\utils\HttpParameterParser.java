package com.timebasedscan.utils;

import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IParameter;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for parsing HTTP parameters from requests
 */
public class HttpParameterParser {

    private IExtensionHelpers helpers;
    
    // Regex patterns for JSON parameters
    private static final Pattern JSON_PARAM_PATTERN = Pattern.compile("\"([^\"]+)\"\\s*:\\s*([^,}\\]]+|\"[^\"]*\"|\\{[^}]*\\}|\\[[^\\]]*\\])");
    
    // Regex patterns for XML parameters
    private static final Pattern XML_TAG_PATTERN = Pattern.compile("<([^\\s>/]+)[^>]*>(.*?)</\\1>", Pattern.DOTALL);
    private static final Pattern XML_SELF_CLOSING_PATTERN = Pattern.compile("<([^\\s>/]+)[^>]*/>");
    
    public HttpParameterParser(IExtensionHelpers helpers) {
        this.helpers = helpers;
    }
    
    /**
     * Parse parameters from a request
     * @param requestResponse The request to parse
     * @return List of parameters
     */
    public List<HttpParameter> parseParameters(IHttpRequestResponse requestResponse) {
        List<HttpParameter> parameters = new ArrayList<>();
        
        byte[] request = requestResponse.getRequest();
        IRequestInfo requestInfo = helpers.analyzeRequest(requestResponse);
        
        // Get standard parameters (URL, body, cookie)
        List<IParameter> burpParams = requestInfo.getParameters();
        for (IParameter param : burpParams) {
            HttpParameter.Type type;

            switch (param.getType()) {
                case IParameter.PARAM_URL:
                    type = HttpParameter.Type.URL;
                    break;
                case IParameter.PARAM_BODY:
                    type = HttpParameter.Type.BODY;
                    break;
                case IParameter.PARAM_COOKIE:
                    type = HttpParameter.Type.COOKIE;
                    break;
                case IParameter.PARAM_XML:
                    type = HttpParameter.Type.XML;
                    break;
                case IParameter.PARAM_XML_ATTR:
                    type = HttpParameter.Type.XML;
                    break;
                case IParameter.PARAM_MULTIPART_ATTR:
                    type = HttpParameter.Type.MULTIPART;
                    break;
                case IParameter.PARAM_JSON:
                    type = HttpParameter.Type.JSON;
                    break;
                default:
                    type = HttpParameter.Type.BODY;
            }

            // Add parameter value for injection
            parameters.add(new HttpParameter(param.getName(), param.getValue(), type));

            // Add parameter name for injection (if not empty)
            if (param.getName() != null && !param.getName().trim().isEmpty()) {
                parameters.add(new HttpParameter(param.getName() + "__NAME__", param.getName(), type));
            }
        }
        
        // Add any JSON parameters that Burp might have missed
        if (isJsonRequest(requestInfo)) {
            parameters.addAll(parseJsonParameters(request, requestInfo));
        }

        // Add any XML parameters that Burp might have missed
        if (isXmlRequest(requestInfo)) {
            parameters.addAll(parseXmlParameters(request, requestInfo));
        }

        // Add any multipart parameters that Burp might have missed
        if (isMultipartRequest(requestInfo)) {
            parameters.addAll(parseMultipartParameters(request, requestInfo));
        }

        // Parse any body content as a single parameter for injection testing
        parameters.addAll(parseBodyAsParameter(request, requestInfo));

        // Parse URL path segments for injection testing
        parameters.addAll(parseUrlPathParameters(requestInfo));

        // Parse headers for potential injection points
        parameters.addAll(parseHeaderParameters(requestInfo));

        // Parse WebSocket upgrade requests
        if (isWebSocketRequest(requestInfo)) {
            parameters.addAll(parseWebSocketParameters(request, requestInfo));
        }

        // Parse JWT tokens from headers
        parameters.addAll(parseJWTTokens(requestInfo));

        // Parse Base64 encoded data
        parameters.addAll(parseBase64EncodedData(request, requestInfo));

        // Parse binary formats (MessagePack, CBOR, etc.)
        parameters.addAll(parseBinaryFormats(request, requestInfo));

        // Parse configuration formats (YAML, TOML, INI)
        parameters.addAll(parseConfigurationFormats(request, requestInfo));

        // Parse template formats (Handlebars, Jinja2)
        parameters.addAll(parseTemplateFormats(request, requestInfo));

        // Parse Server-Sent Events
        if (isServerSentEventsRequest(requestInfo)) {
            parameters.addAll(parseServerSentEvents(request, requestInfo));
        }

        // Parse gRPC/Protocol Buffers
        if (isGrpcRequest(requestInfo)) {
            parameters.addAll(parseGrpcParameters(request, requestInfo));
        }

        // Parse custom API formats (REST path parameters, API keys)
        parameters.addAll(parseRestApiParameters(requestInfo));

        // Parse authentication tokens and API keys
        parameters.addAll(parseAuthenticationParameters(requestInfo));

        // Parse custom headers with structured data
        parameters.addAll(parseStructuredHeaderParameters(requestInfo));

        // Parse URL-encoded nested parameters
        parameters.addAll(parseNestedUrlEncodedParameters(request, requestInfo));

        // Parse form data with file uploads
        parameters.addAll(parseFormDataWithFiles(request, requestInfo));

        return parameters;
    }
    
    /**
     * Check if request is JSON
     */
    private boolean isJsonRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && (
            contentType.contains("application/json") || 
            contentType.contains("text/json") ||
            contentType.contains("application/graphql")
        );
    }
    
    /**
     * Check if request is XML
     */
    private boolean isXmlRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && (
            contentType.contains("application/xml") ||
            contentType.contains("text/xml") ||
            contentType.contains("application/soap+xml")
        );
    }

    /**
     * Check if request is multipart
     */
    private boolean isMultipartRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && contentType.contains("multipart/");
    }
    
    /**
     * Get content type from headers
     */
    private String getContentType(IRequestInfo requestInfo) {
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:")) {
                return header.substring("content-type:".length()).trim().toLowerCase();
            }
        }
        return null;
    }
    
    /**
     * Parse JSON parameters from request - Enhanced to support nested JSON, arrays, and all value types
     */
    private List<HttpParameter> parseJsonParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> jsonParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length)).trim();

        if (body.isEmpty()) {
            return jsonParams;
        }

        System.out.println("Parsing JSON body: " + body.substring(0, Math.min(200, body.length())) + "...");

        try {
            // Enhanced JSON parsing with comprehensive type detection
            jsonParams.addAll(parseJsonRecursively(body, ""));

            // Also parse for GraphQL if detected
            if (isGraphQLRequest(body)) {
                jsonParams.addAll(parseGraphQLParameters(body));
            }

            System.out.println("Total JSON parameters found: " + jsonParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing JSON: " + e.getMessage());
            // Fallback to simple regex parsing
            jsonParams.addAll(parseJsonWithRegex(body));
        }

        return jsonParams;
    }

    /**
     * Recursively parse JSON to extract all parameters including nested objects and arrays
     */
    private List<HttpParameter> parseJsonRecursively(String jsonString, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        jsonString = jsonString.trim();
        if (jsonString.isEmpty()) {
            return params;
        }

        try {
            if (jsonString.startsWith("{") && jsonString.endsWith("}")) {
                // Parse JSON object
                params.addAll(parseJsonObject(jsonString, parentPath));
            } else if (jsonString.startsWith("[") && jsonString.endsWith("]")) {
                // Parse JSON array
                params.addAll(parseJsonArray(jsonString, parentPath));
            } else {
                // Single value - create parameter if we have a parent path
                if (!parentPath.isEmpty()) {
                    String value = cleanJsonValue(jsonString);
                    params.add(new HttpParameter(parentPath, value, HttpParameter.Type.JSON));
                }
            }
        } catch (Exception e) {
            System.out.println("Error in parseJsonRecursively: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse JSON object and extract key-value pairs
     */
    private List<HttpParameter> parseJsonObject(String jsonObject, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Remove outer braces
        String content = jsonObject.substring(1, jsonObject.length() - 1).trim();

        if (content.isEmpty()) {
            return params;
        }

        // Parse key-value pairs
        List<String> keyValuePairs = splitJsonKeyValuePairs(content);

        for (String pair : keyValuePairs) {
            try {
                int colonIndex = findJsonKeyValueSeparator(pair);
                if (colonIndex > 0) {
                    String key = pair.substring(0, colonIndex).trim();
                    String value = pair.substring(colonIndex + 1).trim();

                    // Clean the key (remove quotes)
                    key = cleanJsonKey(key);

                    // Build full path
                    String fullPath = parentPath.isEmpty() ? key : parentPath + "." + key;

                    // Add parameter value for injection
                    String cleanValue = cleanJsonValue(value);
                    params.add(new HttpParameter(fullPath, cleanValue, HttpParameter.Type.JSON));

                    // Add parameter name for injection
                    params.add(new HttpParameter(fullPath + "__NAME__", key, HttpParameter.Type.JSON));

                    // If value is an object or array, parse recursively
                    if ((value.startsWith("{") && value.endsWith("}")) ||
                        (value.startsWith("[") && value.endsWith("]"))) {
                        params.addAll(parseJsonRecursively(value, fullPath));
                    }
                }
            } catch (Exception e) {
                System.out.println("Error parsing key-value pair: " + pair + " - " + e.getMessage());
            }
        }

        return params;
    }

    /**
     * Parse JSON array and extract elements
     */
    private List<HttpParameter> parseJsonArray(String jsonArray, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Remove outer brackets
        String content = jsonArray.substring(1, jsonArray.length() - 1).trim();

        if (content.isEmpty()) {
            return params;
        }

        // Split array elements
        List<String> elements = splitJsonArrayElements(content);

        for (int i = 0; i < elements.size(); i++) {
            String element = elements.get(i).trim();
            String arrayPath = parentPath + "[" + i + "]";

            // Add this array element as a parameter
            String cleanValue = cleanJsonValue(element);
            params.add(new HttpParameter(arrayPath, cleanValue, HttpParameter.Type.JSON));

            // If element is an object or array, parse recursively
            if ((element.startsWith("{") && element.endsWith("}")) ||
                (element.startsWith("[") && element.endsWith("]"))) {
                params.addAll(parseJsonRecursively(element, arrayPath));
            }
        }

        return params;
    }

    /**
     * Helper method to split JSON key-value pairs while respecting nested structures
     */
    private List<String> splitJsonKeyValuePairs(String content) {
        List<String> pairs = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int braceDepth = 0;
        int bracketDepth = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);

            if (escaped) {
                escaped = false;
                current.append(c);
                continue;
            }

            if (c == '\\') {
                escaped = true;
                current.append(c);
                continue;
            }

            if (c == '"') {
                inString = !inString;
                current.append(c);
                continue;
            }

            if (!inString) {
                if (c == '{') braceDepth++;
                else if (c == '}') braceDepth--;
                else if (c == '[') bracketDepth++;
                else if (c == ']') bracketDepth--;
                else if (c == ',' && braceDepth == 0 && bracketDepth == 0) {
                    pairs.add(current.toString().trim());
                    current = new StringBuilder();
                    continue;
                }
            }

            current.append(c);
        }

        if (current.length() > 0) {
            pairs.add(current.toString().trim());
        }

        return pairs;
    }

    /**
     * Helper method to split JSON array elements while respecting nested structures
     */
    private List<String> splitJsonArrayElements(String content) {
        // Reuse the same logic as splitJsonKeyValuePairs since the parsing is similar
        return splitJsonKeyValuePairs(content);
    }

    /**
     * Find the colon separator in a JSON key-value pair
     */
    private int findJsonKeyValueSeparator(String pair) {
        boolean inString = false;
        boolean escaped = false;
        int braceDepth = 0;
        int bracketDepth = 0;

        for (int i = 0; i < pair.length(); i++) {
            char c = pair.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '{') braceDepth++;
                else if (c == '}') braceDepth--;
                else if (c == '[') bracketDepth++;
                else if (c == ']') bracketDepth--;
                else if (c == ':' && braceDepth == 0 && bracketDepth == 0) {
                    return i;
                }
            }
        }

        return -1;
    }

    /**
     * Clean JSON key by removing quotes
     */
    private String cleanJsonKey(String key) {
        key = key.trim();
        if (key.startsWith("\"") && key.endsWith("\"")) {
            return key.substring(1, key.length() - 1);
        }
        return key;
    }

    /**
     * Clean JSON value by removing quotes for strings, keeping original for others
     */
    private String cleanJsonValue(String value) {
        value = value.trim();

        // For string values, remove quotes but keep the content
        if (value.startsWith("\"") && value.endsWith("\"")) {
            return value.substring(1, value.length() - 1);
        }

        // For other values (numbers, booleans, null, objects, arrays), keep as-is
        return value;
    }

    /**
     * Fallback regex-based JSON parsing for when the recursive parser fails
     */
    private List<HttpParameter> parseJsonWithRegex(String body) {
        List<HttpParameter> jsonParams = new ArrayList<>();

        // Use the original regex pattern as fallback
        Matcher matcher = JSON_PARAM_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            String value = matcher.group(2).trim();

            // Remove quotes from string values
            if (value.startsWith("\"") && value.endsWith("\"")) {
                value = value.substring(1, value.length() - 1);
            }

            jsonParams.add(new HttpParameter(name, value, HttpParameter.Type.JSON));
        }

        return jsonParams;
    }

    /**
     * Check if the request body contains GraphQL
     */
    private boolean isGraphQLRequest(String body) {
        return body.contains("\"query\"") || body.contains("\"mutation\"") ||
               body.contains("\"subscription\"") || body.contains("\"variables\"") ||
               body.contains("query {") || body.contains("mutation {") ||
               body.contains("subscription {");
    }

    /**
     * Parse GraphQL parameters from request body
     */
    private List<HttpParameter> parseGraphQLParameters(String body) {
        List<HttpParameter> graphqlParams = new ArrayList<>();

        try {
            // Parse GraphQL query/mutation/subscription
            if (body.contains("\"query\"")) {
                String query = extractGraphQLField(body, "query");
                if (!query.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.query", query, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(query, "graphql.query"));
                }
            }

            if (body.contains("\"mutation\"")) {
                String mutation = extractGraphQLField(body, "mutation");
                if (!mutation.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.mutation", mutation, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(mutation, "graphql.mutation"));
                }
            }

            if (body.contains("\"subscription\"")) {
                String subscription = extractGraphQLField(body, "subscription");
                if (!subscription.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.subscription", subscription, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(subscription, "graphql.subscription"));
                }
            }

            // Parse GraphQL variables
            if (body.contains("\"variables\"")) {
                String variables = extractGraphQLField(body, "variables");
                if (!variables.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.variables", variables, HttpParameter.Type.GRAPHQL));
                    // Parse variables as JSON
                    graphqlParams.addAll(parseJsonRecursively(variables, "graphql.variables"));
                }
            }

            // Parse operation name
            if (body.contains("\"operationName\"")) {
                String operationName = extractGraphQLField(body, "operationName");
                if (!operationName.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.operationName", operationName, HttpParameter.Type.GRAPHQL));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL: " + e.getMessage());
        }

        return graphqlParams;
    }

    /**
     * Extract a specific field from GraphQL JSON body
     */
    private String extractGraphQLField(String body, String fieldName) {
        try {
            String pattern = "\"" + fieldName + "\"\\s*:\\s*";
            int startIndex = body.indexOf(pattern);
            if (startIndex == -1) return "";

            startIndex += pattern.length();

            // Find the value (could be string, object, or null)
            char firstChar = body.charAt(startIndex);
            if (firstChar == '"') {
                // String value
                int endIndex = findStringEnd(body, startIndex);
                return body.substring(startIndex + 1, endIndex);
            } else if (firstChar == '{') {
                // Object value
                int endIndex = findObjectEnd(body, startIndex);
                return body.substring(startIndex, endIndex + 1);
            } else if (firstChar == '[') {
                // Array value
                int endIndex = findArrayEnd(body, startIndex);
                return body.substring(startIndex, endIndex + 1);
            } else {
                // Primitive value (number, boolean, null)
                int endIndex = findPrimitiveEnd(body, startIndex);
                return body.substring(startIndex, endIndex);
            }
        } catch (Exception e) {
            System.out.println("Error extracting GraphQL field " + fieldName + ": " + e.getMessage());
            return "";
        }
    }

    /**
     * Parse GraphQL query string to extract field names and arguments
     */
    private List<HttpParameter> parseGraphQLQueryString(String query, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Extract field names
            Pattern fieldPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?:\\(|\\{|\\s)");
            Matcher fieldMatcher = fieldPattern.matcher(query);
            while (fieldMatcher.find()) {
                String fieldName = fieldMatcher.group(1);
                if (!isGraphQLKeyword(fieldName)) {
                    params.add(new HttpParameter(parentPath + ".field." + fieldName, fieldName, HttpParameter.Type.GRAPHQL));
                }
            }

            // Extract arguments
            Pattern argPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([^,\\)\\}]+)");
            Matcher argMatcher = argPattern.matcher(query);
            while (argMatcher.find()) {
                String argName = argMatcher.group(1);
                String argValue = argMatcher.group(2).trim();

                // Clean argument value
                if (argValue.startsWith("\"") && argValue.endsWith("\"")) {
                    argValue = argValue.substring(1, argValue.length() - 1);
                }

                params.add(new HttpParameter(parentPath + ".arg." + argName, argValue, HttpParameter.Type.GRAPHQL));
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL query string: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if a word is a GraphQL keyword
     */
    private boolean isGraphQLKeyword(String word) {
        String[] keywords = {"query", "mutation", "subscription", "fragment", "on", "type", "interface",
                           "union", "enum", "input", "extend", "scalar", "directive", "schema"};
        for (String keyword : keywords) {
            if (keyword.equals(word)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Find the end of a JSON string value
     */
    private int findStringEnd(String text, int startIndex) {
        boolean escaped = false;
        for (int i = startIndex + 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (escaped) {
                escaped = false;
                continue;
            }
            if (c == '\\') {
                escaped = true;
                continue;
            }
            if (c == '"') {
                return i;
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a JSON object
     */
    private int findObjectEnd(String text, int startIndex) {
        int braceCount = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '{') {
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        return i;
                    }
                }
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a JSON array
     */
    private int findArrayEnd(String text, int startIndex) {
        int bracketCount = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '[') {
                    bracketCount++;
                } else if (c == ']') {
                    bracketCount--;
                    if (bracketCount == 0) {
                        return i;
                    }
                }
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a primitive JSON value (number, boolean, null)
     */
    private int findPrimitiveEnd(String text, int startIndex) {
        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == ',' || c == '}' || c == ']' || Character.isWhitespace(c)) {
                return i;
            }
        }
        return text.length();
    }

    /**
     * Parse the entire request body as a single parameter for injection testing
     * This allows testing of any body content, including raw text, custom formats, etc.
     */
    private List<HttpParameter> parseBodyAsParameter(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> bodyParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        if (bodyOffset < request.length) {
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length)).trim();

            if (!body.isEmpty()) {
                // Only add __BODY__ parameter if there are no other body parameters
                // This prevents conflicts with standard parameter injection
                List<IParameter> bodyParameters = new ArrayList<>();
                for (IParameter param : requestInfo.getParameters()) {
                    if (param.getType() == IParameter.PARAM_BODY) {
                        bodyParameters.add(param);
                    }
                }

                // Only add __BODY__ if no standard body parameters exist and body is not form-encoded
                if (bodyParameters.isEmpty() && !body.contains("=") && !body.contains("&")) {
                    bodyParams.add(new HttpParameter("__BODY__", body, HttpParameter.Type.BODY));
                    System.out.println("Added full body parameter: " + body.substring(0, Math.min(100, body.length())) + "...");
                } else {
                    System.out.println("Skipping __BODY__ parameter - standard body parameters exist or body is form-encoded");
                }
            }
        }

        return bodyParams;
    }

    /**
     * Parse URL path segments for injection testing
     */
    private List<HttpParameter> parseUrlPathParameters(IRequestInfo requestInfo) {
        List<HttpParameter> pathParams = new ArrayList<>();

        try {
            // Get the URL from the request
            String url = requestInfo.getUrl().toString();
            System.out.println("Parsing URL path: " + url);

            // Parse the URL to extract path segments
            java.net.URL urlObj = new java.net.URL(url);
            String path = urlObj.getPath();

            if (path != null && !path.isEmpty() && !path.equals("/")) {
                // Split path into segments, removing empty segments
                String[] segments = path.split("/");

                for (int i = 0; i < segments.length; i++) {
                    String segment = segments[i];

                    // Skip empty segments
                    if (segment.isEmpty()) {
                        continue;
                    }

                    // Create parameter for this path segment
                    String paramName = "URL_PATH[" + i + "]";
                    pathParams.add(new HttpParameter(paramName, segment, HttpParameter.Type.URL_PATH));

                    // Also create a parameter for the full path replacement at this position
                    String fullPathName = "URL_PATH_FULL[" + i + "]";
                    pathParams.add(new HttpParameter(fullPathName, path, HttpParameter.Type.URL_PATH));

                    System.out.println("Added URL path parameter: " + paramName + " = " + segment);
                }

                // Add a parameter for the entire path
                pathParams.add(new HttpParameter("URL_PATH_COMPLETE", path, HttpParameter.Type.URL_PATH));
                System.out.println("Added complete URL path parameter: " + path);
            }

            System.out.println("Total URL path parameters found: " + pathParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing URL path: " + e.getMessage());
        }

        return pathParams;
    }

    /**
     * Parse XML parameters from request with enhanced support for all XML features
     */
    private List<HttpParameter> parseXmlParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> xmlParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        System.out.println("Parsing XML body: " + body.substring(0, Math.min(200, body.length())) + "...");

        try {
            // Parse XML elements with content
            xmlParams.addAll(parseXmlElements(body, ""));

            // Parse XML attributes
            xmlParams.addAll(parseXmlAttributes(body));

            // Parse CDATA sections
            xmlParams.addAll(parseXmlCDATA(body));

            // Parse XML text content
            xmlParams.addAll(parseXmlTextContent(body));

            System.out.println("Total XML parameters found: " + xmlParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing XML: " + e.getMessage());
            // Fallback to simple regex parsing
            xmlParams.addAll(parseXmlWithRegex(body));
        }

        return xmlParams;
    }

    /**
     * Parse XML elements recursively
     */
    private List<HttpParameter> parseXmlElements(String xml, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for XML elements: <tagname>content</tagname>
        Pattern elementPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)(?:\\s[^>]*)?>([^<]*)</\\1>");
        Matcher matcher = elementPattern.matcher(xml);

        while (matcher.find()) {
            String tagName = matcher.group(1);
            String content = matcher.group(2).trim();

            String fullPath = parentPath.isEmpty() ? tagName : parentPath + "." + tagName;

            // Add element value
            params.add(new HttpParameter(fullPath, content, HttpParameter.Type.XML));

            // Add element name for injection
            params.add(new HttpParameter(fullPath + "__NAME__", tagName, HttpParameter.Type.XML));

            // If content contains nested XML, parse recursively
            if (content.contains("<") && content.contains(">")) {
                params.addAll(parseXmlElements(content, fullPath));
            }
        }

        // Pattern for self-closing elements: <tagname attr="value"/>
        Pattern selfClosingPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)(?:\\s[^>]*)?/>");
        matcher = selfClosingPattern.matcher(xml);

        while (matcher.find()) {
            String tagName = matcher.group(1);
            String fullPath = parentPath.isEmpty() ? tagName : parentPath + "." + tagName;

            // Add self-closing element
            params.add(new HttpParameter(fullPath, "", HttpParameter.Type.XML));
            params.add(new HttpParameter(fullPath + "__NAME__", tagName, HttpParameter.Type.XML));
        }

        return params;
    }

    /**
     * Parse XML attributes
     */
    private List<HttpParameter> parseXmlAttributes(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for XML attributes: attribute="value" or attribute='value'
        Pattern attrPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)\\s+[^>]*?([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*?)\\s*=\\s*[\"']([^\"']*)[\"']");
        Matcher matcher = attrPattern.matcher(xml);

        while (matcher.find()) {
            String elementName = matcher.group(1);
            String attrName = matcher.group(2);
            String attrValue = matcher.group(3);

            // Add attribute value
            params.add(new HttpParameter(elementName + "@" + attrName, attrValue, HttpParameter.Type.XML));

            // Add attribute name for injection
            params.add(new HttpParameter(elementName + "@" + attrName + "__NAME__", attrName, HttpParameter.Type.XML));
        }

        return params;
    }

    /**
     * Parse XML CDATA sections
     */
    private List<HttpParameter> parseXmlCDATA(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for CDATA: <![CDATA[content]]>
        Pattern cdataPattern = Pattern.compile("<!\\[CDATA\\[([^\\]]*?)\\]\\]>");
        Matcher matcher = cdataPattern.matcher(xml);

        int cdataIndex = 0;
        while (matcher.find()) {
            String content = matcher.group(1);
            params.add(new HttpParameter("CDATA[" + cdataIndex + "]", content, HttpParameter.Type.XML));
            cdataIndex++;
        }

        return params;
    }

    /**
     * Parse XML text content between tags
     */
    private List<HttpParameter> parseXmlTextContent(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for text content between tags (excluding CDATA and comments)
        Pattern textPattern = Pattern.compile(">([^<]+)<");
        Matcher matcher = textPattern.matcher(xml);

        int textIndex = 0;
        while (matcher.find()) {
            String content = matcher.group(1).trim();
            if (!content.isEmpty() && !content.startsWith("![CDATA[") && !content.startsWith("!--")) {
                params.add(new HttpParameter("XML_TEXT[" + textIndex + "]", content, HttpParameter.Type.XML));
                textIndex++;
            }
        }

        return params;
    }

    /**
     * Fallback regex-based XML parsing
     */
    private List<HttpParameter> parseXmlWithRegex(String body) {
        List<HttpParameter> xmlParams = new ArrayList<>();

        // Use the original regex patterns as fallback
        Matcher matcher = XML_TAG_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            String value = matcher.group(2).trim();
            xmlParams.add(new HttpParameter(name, value, HttpParameter.Type.XML));
        }

        matcher = XML_SELF_CLOSING_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            xmlParams.add(new HttpParameter(name, "", HttpParameter.Type.XML));
        }

        return xmlParams;
    }

    /**
     * Parse multipart form data with enhanced support for all multipart features
     */
    private List<HttpParameter> parseMultipartParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> multipartParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Get Content-Type header to extract boundary
        String boundary = extractMultipartBoundary(requestInfo.getHeaders());
        if (boundary == null || boundary.isEmpty()) {
            System.out.println("No multipart boundary found");
            return multipartParams;
        }

        System.out.println("Parsing multipart data with boundary: " + boundary);

        try {
            // Split by boundary
            String[] parts = body.split("--" + Pattern.quote(boundary));

            for (int i = 1; i < parts.length - 1; i++) { // Skip first empty part and last closing part
                String part = parts[i].trim();
                if (!part.isEmpty()) {
                    multipartParams.addAll(parseMultipartPart(part, i));
                }
            }

            System.out.println("Total multipart parameters found: " + multipartParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing multipart data: " + e.getMessage());
        }

        return multipartParams;
    }

    /**
     * Extract boundary from Content-Type header
     */
    private String extractMultipartBoundary(List<String> headers) {
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:") &&
                header.toLowerCase().contains("multipart/")) {

                Pattern boundaryPattern = Pattern.compile("boundary=([^;\\s]+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = boundaryPattern.matcher(header);
                if (matcher.find()) {
                    String boundary = matcher.group(1);
                    // Remove quotes if present
                    if (boundary.startsWith("\"") && boundary.endsWith("\"")) {
                        boundary = boundary.substring(1, boundary.length() - 1);
                    }
                    return boundary;
                }
            }
        }
        return null;
    }

    /**
     * Parse individual multipart part
     */
    private List<HttpParameter> parseMultipartPart(String part, int partIndex) {
        List<HttpParameter> params = new ArrayList<>();

        // Split headers and body
        String[] sections = part.split("\r\n\r\n", 2);
        if (sections.length < 2) {
            sections = part.split("\n\n", 2);
        }

        if (sections.length >= 2) {
            String headers = sections[0];
            String content = sections[1];

            // Parse Content-Disposition header
            String name = extractMultipartName(headers);
            String filename = extractMultipartFilename(headers);
            String contentType = extractMultipartContentType(headers);

            // Add field name and value
            if (name != null && !name.isEmpty()) {
                params.add(new HttpParameter(name, content, HttpParameter.Type.MULTIPART));
                params.add(new HttpParameter(name + "__NAME__", name, HttpParameter.Type.MULTIPART));

                // Add filename if present (file upload)
                if (filename != null && !filename.isEmpty()) {
                    params.add(new HttpParameter(name + "__FILENAME__", filename, HttpParameter.Type.MULTIPART));

                    // Add content type if present
                    if (contentType != null && !contentType.isEmpty()) {
                        params.add(new HttpParameter(name + "__CONTENT_TYPE__", contentType, HttpParameter.Type.MULTIPART));
                    }

                    // For binary files, add metadata parameters
                    params.add(new HttpParameter(name + "__FILE_SIZE__", String.valueOf(content.length()), HttpParameter.Type.MULTIPART));
                    params.add(new HttpParameter(name + "__IS_FILE__", "true", HttpParameter.Type.MULTIPART));
                } else {
                    // Text field
                    params.add(new HttpParameter(name + "__IS_TEXT__", "true", HttpParameter.Type.MULTIPART));
                }
            } else {
                // Unnamed part
                params.add(new HttpParameter("MULTIPART_PART[" + partIndex + "]", content, HttpParameter.Type.MULTIPART));
            }

            // Parse all headers in the multipart part
            params.addAll(parseMultipartHeaders(headers, name != null ? name : "PART[" + partIndex + "]"));
        }

        return params;
    }

    /**
     * Extract name from Content-Disposition header
     */
    private String extractMultipartName(String headers) {
        Pattern namePattern = Pattern.compile("name\\s*=\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
        Matcher matcher = namePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        // Try without quotes
        namePattern = Pattern.compile("name\\s*=\\s*([^;\\s]+)", Pattern.CASE_INSENSITIVE);
        matcher = namePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * Extract filename from Content-Disposition header
     */
    private String extractMultipartFilename(String headers) {
        Pattern filenamePattern = Pattern.compile("filename\\s*=\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
        Matcher matcher = filenamePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        // Try without quotes
        filenamePattern = Pattern.compile("filename\\s*=\\s*([^;\\s]+)", Pattern.CASE_INSENSITIVE);
        matcher = filenamePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * Extract Content-Type from multipart headers
     */
    private String extractMultipartContentType(String headers) {
        Pattern contentTypePattern = Pattern.compile("Content-Type:\\s*([^\\r\\n]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = contentTypePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }

    /**
     * Parse headers within multipart part
     */
    private List<HttpParameter> parseMultipartHeaders(String headers, String partName) {
        List<HttpParameter> params = new ArrayList<>();

        String[] headerLines = headers.split("\r\n");
        if (headerLines.length == 1) {
            headerLines = headers.split("\n");
        }

        for (String headerLine : headerLines) {
            if (headerLine.contains(":")) {
                String[] headerParts = headerLine.split(":", 2);
                if (headerParts.length == 2) {
                    String headerName = headerParts[0].trim();
                    String headerValue = headerParts[1].trim();

                    params.add(new HttpParameter(partName + "__HEADER__" + headerName, headerValue, HttpParameter.Type.MULTIPART));
                }
            }
        }

        return params;
    }

    /**
     * Parse headers for potential injection points
     */
    private List<HttpParameter> parseHeaderParameters(IRequestInfo requestInfo) {
        List<HttpParameter> headerParams = new ArrayList<>();
        List<String> headers = requestInfo.getHeaders();

        // Skip first header (request line)
        for (int i = 1; i < headers.size(); i++) {
            String header = headers.get(i);
            int colonIndex = header.indexOf(':');

            if (colonIndex > 0) {
                String name = header.substring(0, colonIndex).trim();
                String value = header.substring(colonIndex + 1).trim();

                // Only add headers that might be vulnerable to injection
                if (isInterestingHeader(name)) {
                    // Add header value for injection
                    headerParams.add(new HttpParameter(name, value, HttpParameter.Type.HEADER));
                    // Add header name for injection
                    headerParams.add(new HttpParameter(name + "__NAME__", name, HttpParameter.Type.HEADER));
                    // Debug output to help troubleshoot header detection
                    System.out.println("Added header parameter: " + name + " = " + value);
                }
            }
        }

        System.out.println("Total header parameters found: " + headerParams.size());
        return headerParams;
    }
    
    /**
     * Check if a header is interesting for potential injections
     */
    private boolean isInterestingHeader(String headerName) {
        headerName = headerName.toLowerCase();

        // Headers commonly vulnerable to injection attacks
        String[] interestingHeaders = {
            // Standard headers
            "user-agent", "referer", "host", "origin", "accept", "accept-language",
            "accept-encoding", "accept-charset", "content-type", "content-length",

            // Authentication and authorization
            "authorization", "x-api-key", "x-auth-token", "x-access-token",
            "x-csrf-token", "x-xsrf-token", "x-requested-with",

            // Forwarding and proxy headers
            "x-forwarded-for", "x-forwarded-host", "x-forwarded-proto",
            "x-real-ip", "x-client-ip", "x-cluster-client-ip",

            // Custom application headers
            "x-custom-header", "x-application-id", "x-session-id", "x-user-id",
            "x-tenant-id", "x-correlation-id", "x-trace-id", "x-request-id",

            // Cache and CDN headers
            "cache-control", "pragma", "if-modified-since", "if-none-match",
            "x-cache", "x-cdn-provider",

            // Security headers (sometimes processed by backend)
            "x-frame-options", "x-content-type-options", "x-xss-protection",

            // Cookie (already included but keeping for clarity)
            "cookie"
        };

        for (String header : interestingHeaders) {
            if (headerName.equals(header)) {
                return true;
            }
        }

        // Also include any header starting with "x-" as these are often custom headers
        if (headerName.startsWith("x-")) {
            return true;
        }

        return false;
    }

    /**
     * Check if request is a WebSocket upgrade request
     */
    private boolean isWebSocketRequest(IRequestInfo requestInfo) {
        List<String> headers = requestInfo.getHeaders();
        boolean hasUpgrade = false;
        boolean hasConnection = false;
        boolean hasWebSocketKey = false;

        for (String header : headers) {
            String lowerHeader = header.toLowerCase();
            if (lowerHeader.startsWith("upgrade:") && lowerHeader.contains("websocket")) {
                hasUpgrade = true;
            } else if (lowerHeader.startsWith("connection:") && lowerHeader.contains("upgrade")) {
                hasConnection = true;
            } else if (lowerHeader.startsWith("sec-websocket-key:")) {
                hasWebSocketKey = true;
            }
        }

        return hasUpgrade && hasConnection && hasWebSocketKey;
    }

    /**
     * Parse WebSocket upgrade request parameters
     */
    private List<HttpParameter> parseWebSocketParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> wsParams = new ArrayList<>();

        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            String lowerHeader = header.toLowerCase();

            if (lowerHeader.startsWith("sec-websocket-")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();
                    wsParams.add(new HttpParameter("ws." + name, value, HttpParameter.Type.WEBSOCKET));
                    wsParams.add(new HttpParameter("ws." + name + "__NAME__", name, HttpParameter.Type.WEBSOCKET));
                }
            } else if (lowerHeader.startsWith("upgrade:") ||
                      (lowerHeader.startsWith("connection:") && lowerHeader.contains("upgrade"))) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();
                    wsParams.add(new HttpParameter("ws." + name, value, HttpParameter.Type.WEBSOCKET));
                }
            }
        }

        // Parse WebSocket subprotocols
        for (String header : headers) {
            if (header.toLowerCase().startsWith("sec-websocket-protocol:")) {
                String protocols = header.substring(header.indexOf(":") + 1).trim();
                String[] protocolList = protocols.split(",");
                for (int i = 0; i < protocolList.length; i++) {
                    String protocol = protocolList[i].trim();
                    wsParams.add(new HttpParameter("ws.protocol[" + i + "]", protocol, HttpParameter.Type.WEBSOCKET));
                }
            }
        }

        System.out.println("WebSocket parameters found: " + wsParams.size());
        return wsParams;
    }

    /**
     * Check if request contains Server-Sent Events
     */
    private boolean isServerSentEventsRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && contentType.contains("text/event-stream");
    }

    /**
     * Parse Server-Sent Events data
     */
    private List<HttpParameter> parseServerSentEvents(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> sseParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Parse SSE format: data: {...}\n\n
        String[] lines = body.split("\n");
        int eventIndex = 0;

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("data:")) {
                String data = line.substring(5).trim();

                // Try to parse as JSON
                if (data.startsWith("{") && data.endsWith("}")) {
                    try {
                        List<HttpParameter> jsonParams = parseJsonRecursively(data, "sse.event[" + eventIndex + "]");
                        sseParams.addAll(jsonParams);
                    } catch (Exception e) {
                        // Fallback to plain text
                        sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].data", data, HttpParameter.Type.SSE));
                    }
                } else {
                    sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].data", data, HttpParameter.Type.SSE));
                }
                eventIndex++;
            } else if (line.startsWith("event:")) {
                String eventType = line.substring(6).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].type", eventType, HttpParameter.Type.SSE));
            } else if (line.startsWith("id:")) {
                String id = line.substring(3).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].id", id, HttpParameter.Type.SSE));
            } else if (line.startsWith("retry:")) {
                String retry = line.substring(6).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].retry", retry, HttpParameter.Type.SSE));
            }
        }

        System.out.println("Server-Sent Events parameters found: " + sseParams.size());
        return sseParams;
    }

    /**
     * Parse JWT tokens from headers and body
     */
    private List<HttpParameter> parseJWTTokens(IRequestInfo requestInfo) {
        List<HttpParameter> jwtParams = new ArrayList<>();

        // Check Authorization header
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("authorization:")) {
                String authValue = header.substring(header.indexOf(":") + 1).trim();

                if (authValue.toLowerCase().startsWith("bearer ")) {
                    String token = authValue.substring(7).trim();
                    jwtParams.addAll(parseJWTToken(token, "auth.bearer"));
                } else if (authValue.toLowerCase().startsWith("jwt ")) {
                    String token = authValue.substring(4).trim();
                    jwtParams.addAll(parseJWTToken(token, "auth.jwt"));
                }
            }

            // Check custom JWT headers
            if (header.toLowerCase().contains("jwt") || header.toLowerCase().contains("token")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();

                    // Check if value looks like a JWT (three base64 parts separated by dots)
                    if (isJWTFormat(value)) {
                        jwtParams.addAll(parseJWTToken(value, "header." + name.toLowerCase()));
                    }
                }
            }
        }

        System.out.println("JWT parameters found: " + jwtParams.size());
        return jwtParams;
    }

    /**
     * Check if a string looks like a JWT token
     */
    private boolean isJWTFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        String[] parts = token.split("\\.");
        return parts.length == 3 &&
               parts[0].length() > 0 &&
               parts[1].length() > 0 &&
               parts[2].length() > 0;
    }

    /**
     * Parse individual JWT token
     */
    private List<HttpParameter> parseJWTToken(String token, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return params;
            }

            // Parse header (first part)
            try {
                String headerJson = decodeBase64Url(parts[0]);
                params.add(new HttpParameter(prefix + ".header", headerJson, HttpParameter.Type.JWT));
                params.addAll(parseJsonRecursively(headerJson, prefix + ".header"));
            } catch (Exception e) {
                params.add(new HttpParameter(prefix + ".header.raw", parts[0], HttpParameter.Type.JWT));
            }

            // Parse payload (second part)
            try {
                String payloadJson = decodeBase64Url(parts[1]);
                params.add(new HttpParameter(prefix + ".payload", payloadJson, HttpParameter.Type.JWT));
                params.addAll(parseJsonRecursively(payloadJson, prefix + ".payload"));
            } catch (Exception e) {
                params.add(new HttpParameter(prefix + ".payload.raw", parts[1], HttpParameter.Type.JWT));
            }

            // Add signature (third part) - don't decode as it's binary
            params.add(new HttpParameter(prefix + ".signature", parts[2], HttpParameter.Type.JWT));

            // Add full token for injection
            params.add(new HttpParameter(prefix + ".token", token, HttpParameter.Type.JWT));

        } catch (Exception e) {
            System.out.println("Error parsing JWT token: " + e.getMessage());
            // Add as raw token if parsing fails
            params.add(new HttpParameter(prefix + ".raw", token, HttpParameter.Type.JWT));
        }

        return params;
    }

    /**
     * Decode Base64 URL-safe string
     */
    private String decodeBase64Url(String base64Url) {
        // Add padding if needed
        String base64 = base64Url;
        while (base64.length() % 4 != 0) {
            base64 += "=";
        }

        // Replace URL-safe characters
        base64 = base64.replace('-', '+').replace('_', '/');

        // Decode
        try {
            byte[] decoded = java.util.Base64.getDecoder().decode(base64);
            return new String(decoded, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to decode Base64 URL: " + e.getMessage());
        }
    }

    /**
     * Parse Base64 encoded data from request body and parameters
     */
    private List<HttpParameter> parseBase64EncodedData(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> base64Params = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Look for Base64 patterns in body
        base64Params.addAll(findAndDecodeBase64(body, "body"));

        // Look for Base64 patterns in URL parameters
        for (IParameter param : requestInfo.getParameters()) {
            if (param.getType() == IParameter.PARAM_URL) {
                base64Params.addAll(findAndDecodeBase64(param.getValue(), "url." + param.getName()));
            }
        }

        // Look for Base64 patterns in headers
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.contains(":")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String headerName = parts[0].trim();
                    String headerValue = parts[1].trim();
                    base64Params.addAll(findAndDecodeBase64(headerValue, "header." + headerName.toLowerCase()));
                }
            }
        }

        System.out.println("Base64 encoded parameters found: " + base64Params.size());
        return base64Params;
    }

    /**
     * Find and decode Base64 strings in text
     */
    private List<HttpParameter> findAndDecodeBase64(String text, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for Base64 strings (at least 16 characters, valid Base64 characters)
        Pattern base64Pattern = Pattern.compile("([A-Za-z0-9+/]{16,}={0,2})");
        Matcher matcher = base64Pattern.matcher(text);

        int index = 0;
        while (matcher.find()) {
            String base64String = matcher.group(1);

            try {
                // Try to decode
                byte[] decoded = java.util.Base64.getDecoder().decode(base64String);
                String decodedString = new String(decoded, StandardCharsets.UTF_8);

                // Check if decoded string is meaningful (printable characters)
                if (isPrintableString(decodedString)) {
                    params.add(new HttpParameter(prefix + ".base64[" + index + "]", base64String, HttpParameter.Type.BASE64));
                    params.add(new HttpParameter(prefix + ".base64[" + index + "].decoded", decodedString, HttpParameter.Type.BASE64));

                    // If decoded string looks like JSON, parse it
                    if (decodedString.trim().startsWith("{") && decodedString.trim().endsWith("}")) {
                        try {
                            params.addAll(parseJsonRecursively(decodedString, prefix + ".base64[" + index + "].json"));
                        } catch (Exception e) {
                            // Not valid JSON, ignore
                        }
                    }

                    index++;
                }
            } catch (Exception e) {
                // Not valid Base64 or not UTF-8, ignore
            }
        }

        return params;
    }

    /**
     * Check if string contains mostly printable characters
     */
    private boolean isPrintableString(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        int printableCount = 0;
        for (char c : str.toCharArray()) {
            if (c >= 32 && c <= 126) { // Printable ASCII range
                printableCount++;
            }
        }

        // At least 80% printable characters
        return (double) printableCount / str.length() >= 0.8;
    }

    /**
     * Parse binary formats (MessagePack, CBOR, Avro, etc.)
     */
    private List<HttpParameter> parseBinaryFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> binaryParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        byte[] body = Arrays.copyOfRange(request, bodyOffset, request.length);

        if (body.length == 0) {
            return binaryParams;
        }

        // Check Content-Type for binary format hints
        String contentType = getContentType(requestInfo);

        if (contentType != null) {
            if (contentType.contains("application/msgpack") || contentType.contains("application/x-msgpack")) {
                binaryParams.addAll(parseMessagePack(body));
            } else if (contentType.contains("application/cbor")) {
                binaryParams.addAll(parseCBOR(body));
            } else if (contentType.contains("application/avro") || contentType.contains("avro/binary")) {
                binaryParams.addAll(parseAvro(body));
            } else if (contentType.contains("application/protobuf") || contentType.contains("application/x-protobuf")) {
                binaryParams.addAll(parseProtobuf(body));
            }
        }

        // Try to detect binary formats by magic bytes
        if (binaryParams.isEmpty()) {
            binaryParams.addAll(detectBinaryFormat(body));
        }

        System.out.println("Binary format parameters found: " + binaryParams.size());
        return binaryParams;
    }

    /**
     * Parse MessagePack format (simplified detection)
     */
    private List<HttpParameter> parseMessagePack(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // MessagePack format detection (simplified)
            // Look for common MessagePack type markers
            if (data.length > 0) {
                byte firstByte = data[0];

                // Fixed map (0x80-0x8f), fixed array (0x90-0x9f), fixed string (0xa0-0xbf)
                if ((firstByte & 0x80) == 0x80 || (firstByte & 0x90) == 0x90 || (firstByte & 0xa0) == 0xa0) {
                    params.add(new HttpParameter("msgpack.detected", "true", HttpParameter.Type.MESSAGEPACK));
                    params.add(new HttpParameter("msgpack.size", String.valueOf(data.length), HttpParameter.Type.MESSAGEPACK));
                    params.add(new HttpParameter("msgpack.first_byte", String.format("0x%02x", firstByte), HttpParameter.Type.MESSAGEPACK));

                    // Try to extract string-like data
                    params.addAll(extractBinaryStrings(data, "msgpack"));
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing MessagePack: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse CBOR format (simplified detection)
     */
    private List<HttpParameter> parseCBOR(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 0) {
                byte firstByte = data[0];

                // CBOR major types: 0-7 (first 3 bits)
                int majorType = (firstByte >> 5) & 0x07;

                params.add(new HttpParameter("cbor.detected", "true", HttpParameter.Type.CBOR));
                params.add(new HttpParameter("cbor.size", String.valueOf(data.length), HttpParameter.Type.CBOR));
                params.add(new HttpParameter("cbor.major_type", String.valueOf(majorType), HttpParameter.Type.CBOR));

                // Try to extract string-like data
                params.addAll(extractBinaryStrings(data, "cbor"));
            }
        } catch (Exception e) {
            System.out.println("Error parsing CBOR: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Avro format (simplified detection)
     */
    private List<HttpParameter> parseAvro(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 4) {
                // Avro files often start with magic bytes or have specific patterns
                params.add(new HttpParameter("avro.detected", "true", HttpParameter.Type.AVRO));
                params.add(new HttpParameter("avro.size", String.valueOf(data.length), HttpParameter.Type.AVRO));

                // Try to extract string-like data
                params.addAll(extractBinaryStrings(data, "avro"));
            }
        } catch (Exception e) {
            System.out.println("Error parsing Avro: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Protocol Buffers format (simplified detection)
     */
    private List<HttpParameter> parseProtobuf(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 0) {
                params.add(new HttpParameter("protobuf.detected", "true", HttpParameter.Type.PROTOBUF));
                params.add(new HttpParameter("protobuf.size", String.valueOf(data.length), HttpParameter.Type.PROTOBUF));

                // Try to extract string-like data and field numbers
                params.addAll(extractProtobufFields(data));
            }
        } catch (Exception e) {
            System.out.println("Error parsing Protobuf: " + e.getMessage());
        }

        return params;
    }

    /**
     * Detect binary format by magic bytes and patterns
     */
    private List<HttpParameter> detectBinaryFormat(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        if (data.length == 0) {
            return params;
        }

        // Check for common binary format signatures
        if (data.length >= 4) {
            // Check for various magic bytes
            String hex = bytesToHex(data, 0, Math.min(4, data.length));
            params.add(new HttpParameter("binary.magic_bytes", hex, HttpParameter.Type.BINARY));
        }

        // Try to extract any readable strings from binary data
        params.addAll(extractBinaryStrings(data, "binary"));

        return params;
    }

    /**
     * Extract readable strings from binary data
     */
    private List<HttpParameter> extractBinaryStrings(byte[] data, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        StringBuilder currentString = new StringBuilder();
        int stringIndex = 0;

        for (int i = 0; i < data.length; i++) {
            byte b = data[i];

            // Check if byte is printable ASCII
            if (b >= 32 && b <= 126) {
                currentString.append((char) b);
            } else {
                // End of string, save if long enough
                if (currentString.length() >= 4) {
                    params.add(new HttpParameter(prefix + ".string[" + stringIndex + "]",
                                               currentString.toString(), HttpParameter.Type.BINARY));
                    stringIndex++;
                }
                currentString = new StringBuilder();
            }
        }

        // Save last string if exists
        if (currentString.length() >= 4) {
            params.add(new HttpParameter(prefix + ".string[" + stringIndex + "]",
                                       currentString.toString(), HttpParameter.Type.BINARY));
        }

        return params;
    }

    /**
     * Extract Protocol Buffer fields (simplified)
     */
    private List<HttpParameter> extractProtobufFields(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            int i = 0;
            int fieldIndex = 0;

            while (i < data.length && fieldIndex < 20) { // Limit to prevent infinite loops
                if (i + 1 >= data.length) break;

                // Read varint tag
                int tag = data[i] & 0xFF;
                int fieldNumber = tag >> 3;
                int wireType = tag & 0x07;

                params.add(new HttpParameter("protobuf.field[" + fieldIndex + "].number",
                                           String.valueOf(fieldNumber), HttpParameter.Type.PROTOBUF));
                params.add(new HttpParameter("protobuf.field[" + fieldIndex + "].wire_type",
                                           String.valueOf(wireType), HttpParameter.Type.PROTOBUF));

                i++;
                fieldIndex++;

                // Skip field data based on wire type (simplified)
                switch (wireType) {
                    case 0: // Varint
                        while (i < data.length && (data[i] & 0x80) != 0) i++;
                        if (i < data.length) i++;
                        break;
                    case 1: // 64-bit
                        i += 8;
                        break;
                    case 2: // Length-delimited
                        if (i < data.length) {
                            int length = data[i] & 0xFF;
                            i += 1 + length;
                        }
                        break;
                    case 5: // 32-bit
                        i += 4;
                        break;
                    default:
                        i++; // Unknown wire type, skip one byte
                }
            }
        } catch (Exception e) {
            System.out.println("Error extracting Protobuf fields: " + e.getMessage());
        }

        return params;
    }

    /**
     * Convert bytes to hex string
     */
    private String bytesToHex(byte[] bytes, int offset, int length) {
        StringBuilder hex = new StringBuilder();
        for (int i = offset; i < offset + length && i < bytes.length; i++) {
            hex.append(String.format("%02x", bytes[i] & 0xFF));
        }
        return hex.toString();
    }

    /**
     * Parse configuration formats (YAML, TOML, INI)
     */
    private List<HttpParameter> parseConfigurationFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> configParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        if (body.trim().isEmpty()) {
            return configParams;
        }

        // Check Content-Type for format hints
        String contentType = getContentType(requestInfo);

        if (contentType != null) {
            if (contentType.contains("application/yaml") || contentType.contains("text/yaml") ||
                contentType.contains("application/x-yaml")) {
                configParams.addAll(parseYAML(body));
            } else if (contentType.contains("application/toml") || contentType.contains("text/toml")) {
                configParams.addAll(parseTOML(body));
            } else if (contentType.contains("text/plain") && (body.contains("[") || body.contains("="))) {
                // Could be INI format
                configParams.addAll(parseINI(body));
            }
        } else {
            // Try to detect format by content
            if (body.contains("---") || body.contains(":") && body.contains("\n")) {
                configParams.addAll(parseYAML(body));
            } else if (body.contains("[") && body.contains("]") && body.contains("=")) {
                if (body.contains("\"\"\"") || body.contains("'''")) {
                    configParams.addAll(parseTOML(body));
                } else {
                    configParams.addAll(parseINI(body));
                }
            }
        }

        System.out.println("Configuration format parameters found: " + configParams.size());
        return configParams;
    }

    /**
     * Parse YAML format (simplified)
     */
    private List<HttpParameter> parseYAML(String yaml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = yaml.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // Handle YAML key-value pairs
                if (line.contains(":")) {
                    String[] parts = line.split(":", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        } else if (value.startsWith("'") && value.endsWith("'")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("yaml." + fullKey, value, HttpParameter.Type.YAML));
                        params.add(new HttpParameter("yaml." + fullKey + "__NAME__", key, HttpParameter.Type.YAML));

                        // If value is empty, this might be a section header
                        if (value.isEmpty()) {
                            currentSection = key;
                        }
                    }
                }

                // Handle YAML arrays
                if (line.startsWith("-")) {
                    String value = line.substring(1).trim();
                    params.add(new HttpParameter("yaml.array_item", value, HttpParameter.Type.YAML));
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing YAML: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse TOML format (simplified)
     */
    private List<HttpParameter> parseTOML(String toml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = toml.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // Handle TOML sections [section]
                if (line.startsWith("[") && line.endsWith("]")) {
                    currentSection = line.substring(1, line.length() - 1);
                    params.add(new HttpParameter("toml.section", currentSection, HttpParameter.Type.TOML));
                    continue;
                }

                // Handle TOML key-value pairs
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        } else if (value.startsWith("'") && value.endsWith("'")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("toml." + fullKey, value, HttpParameter.Type.TOML));
                        params.add(new HttpParameter("toml." + fullKey + "__NAME__", key, HttpParameter.Type.TOML));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing TOML: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse INI format (simplified)
     */
    private List<HttpParameter> parseINI(String ini) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = ini.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith(";") || line.startsWith("#")) {
                    continue;
                }

                // Handle INI sections [section]
                if (line.startsWith("[") && line.endsWith("]")) {
                    currentSection = line.substring(1, line.length() - 1);
                    params.add(new HttpParameter("ini.section", currentSection, HttpParameter.Type.INI));
                    continue;
                }

                // Handle INI key-value pairs
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("ini." + fullKey, value, HttpParameter.Type.INI));
                        params.add(new HttpParameter("ini." + fullKey + "__NAME__", key, HttpParameter.Type.INI));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing INI: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse template formats (Handlebars, Jinja2, etc.)
     */
    private List<HttpParameter> parseTemplateFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> templateParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        if (body.trim().isEmpty()) {
            return templateParams;
        }

        // Detect template format by patterns
        if (body.contains("{{") && body.contains("}}")) {
            templateParams.addAll(parseHandlebars(body));
        }

        if (body.contains("{%") && body.contains("%}")) {
            templateParams.addAll(parseJinja2(body));
        }

        if (body.contains("<%") && body.contains("%>")) {
            templateParams.addAll(parseERB(body));
        }

        System.out.println("Template format parameters found: " + templateParams.size());
        return templateParams;
    }

    /**
     * Parse Handlebars/Mustache templates
     */
    private List<HttpParameter> parseHandlebars(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for Handlebars expressions: {{variable}}, {{#each items}}, {{helper arg1 arg2}}
            Pattern handlebarsPattern = Pattern.compile("\\{\\{([^}]+)\\}\\}");
            Matcher matcher = handlebarsPattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();

                // Add the full expression
                params.add(new HttpParameter("handlebars.expression[" + index + "]", expression, HttpParameter.Type.HANDLEBARS));

                // Parse different types of expressions
                if (expression.startsWith("#")) {
                    // Block helper: {{#each items}}
                    String[] parts = expression.substring(1).split("\\s+");
                    if (parts.length > 0) {
                        params.add(new HttpParameter("handlebars.block_helper[" + index + "]", parts[0], HttpParameter.Type.HANDLEBARS));
                        if (parts.length > 1) {
                            params.add(new HttpParameter("handlebars.block_context[" + index + "]", parts[1], HttpParameter.Type.HANDLEBARS));
                        }
                    }
                } else if (expression.startsWith("/")) {
                    // Closing block: {{/each}}
                    params.add(new HttpParameter("handlebars.block_end[" + index + "]", expression.substring(1), HttpParameter.Type.HANDLEBARS));
                } else if (expression.contains(" ")) {
                    // Helper with arguments: {{helper arg1 arg2}}
                    String[] parts = expression.split("\\s+");
                    params.add(new HttpParameter("handlebars.helper[" + index + "]", parts[0], HttpParameter.Type.HANDLEBARS));
                    for (int i = 1; i < parts.length; i++) {
                        params.add(new HttpParameter("handlebars.arg[" + index + "][" + (i-1) + "]", parts[i], HttpParameter.Type.HANDLEBARS));
                    }
                } else {
                    // Simple variable: {{variable}}
                    params.add(new HttpParameter("handlebars.variable[" + index + "]", expression, HttpParameter.Type.HANDLEBARS));

                    // Handle dot notation: {{user.name}}
                    if (expression.contains(".")) {
                        String[] parts = expression.split("\\.");
                        for (int i = 0; i < parts.length; i++) {
                            params.add(new HttpParameter("handlebars.path[" + index + "][" + i + "]", parts[i], HttpParameter.Type.HANDLEBARS));
                        }
                    }
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing Handlebars: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Jinja2 templates
     */
    private List<HttpParameter> parseJinja2(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for Jinja2 expressions: {% for %}, {{ variable }}, {# comment #}
            Pattern jinja2Pattern = Pattern.compile("\\{[%{#]([^%}#]+)[%}#]\\}");
            Matcher matcher = jinja2Pattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();
                String fullMatch = matcher.group(0);

                // Add the full expression
                params.add(new HttpParameter("jinja2.expression[" + index + "]", expression, HttpParameter.Type.JINJA2));

                // Determine expression type
                if (fullMatch.startsWith("{%")) {
                    // Control structure: {% for item in items %}
                    params.add(new HttpParameter("jinja2.control[" + index + "]", expression, HttpParameter.Type.JINJA2));

                    if (expression.startsWith("for ")) {
                        String[] parts = expression.split("\\s+");
                        if (parts.length >= 4 && "in".equals(parts[2])) {
                            params.add(new HttpParameter("jinja2.for_var[" + index + "]", parts[1], HttpParameter.Type.JINJA2));
                            params.add(new HttpParameter("jinja2.for_iterable[" + index + "]", parts[3], HttpParameter.Type.JINJA2));
                        }
                    } else if (expression.startsWith("if ")) {
                        String condition = expression.substring(3).trim();
                        params.add(new HttpParameter("jinja2.if_condition[" + index + "]", condition, HttpParameter.Type.JINJA2));
                    }
                } else if (fullMatch.startsWith("{{")) {
                    // Variable: {{ variable }}
                    params.add(new HttpParameter("jinja2.variable[" + index + "]", expression, HttpParameter.Type.JINJA2));

                    // Handle filters: {{ variable|filter }}
                    if (expression.contains("|")) {
                        String[] parts = expression.split("\\|");
                        params.add(new HttpParameter("jinja2.var_name[" + index + "]", parts[0].trim(), HttpParameter.Type.JINJA2));
                        for (int i = 1; i < parts.length; i++) {
                            params.add(new HttpParameter("jinja2.filter[" + index + "][" + (i-1) + "]", parts[i].trim(), HttpParameter.Type.JINJA2));
                        }
                    }

                    // Handle dot notation: {{ user.profile.name }}
                    if (expression.contains(".")) {
                        String[] parts = expression.split("\\.");
                        for (int i = 0; i < parts.length; i++) {
                            params.add(new HttpParameter("jinja2.path[" + index + "][" + i + "]", parts[i], HttpParameter.Type.JINJA2));
                        }
                    }
                } else if (fullMatch.startsWith("{#")) {
                    // Comment: {# comment #}
                    params.add(new HttpParameter("jinja2.comment[" + index + "]", expression, HttpParameter.Type.JINJA2));
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing Jinja2: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse ERB (Embedded Ruby) templates
     */
    private List<HttpParameter> parseERB(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for ERB expressions: <% code %>, <%= expression %>
            Pattern erbPattern = Pattern.compile("<%=?([^%]+)%>");
            Matcher matcher = erbPattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();
                String fullMatch = matcher.group(0);

                // Add the full expression
                params.add(new HttpParameter("erb.expression[" + index + "]", expression, HttpParameter.Type.TEMPLATE));

                // Determine if it's output or code
                if (fullMatch.startsWith("<%=")) {
                    params.add(new HttpParameter("erb.output[" + index + "]", expression, HttpParameter.Type.TEMPLATE));
                } else {
                    params.add(new HttpParameter("erb.code[" + index + "]", expression, HttpParameter.Type.TEMPLATE));
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing ERB: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if request is gRPC/Protocol Buffers
     */
    private boolean isGrpcRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        List<String> headers = requestInfo.getHeaders();

        // Check content type
        if (contentType != null && (
            contentType.contains("application/grpc") ||
            contentType.contains("application/x-protobuf") ||
            contentType.contains("application/protobuf")
        )) {
            return true;
        }

        // Check for gRPC headers
        for (String header : headers) {
            String lowerHeader = header.toLowerCase();
            if (lowerHeader.startsWith("grpc-") ||
                lowerHeader.contains("te: trailers") ||
                lowerHeader.contains("content-type: application/grpc")) {
                return true;
            }
        }

        return false;
    }

    /**
     * Parse gRPC/Protocol Buffers parameters
     */
    private List<HttpParameter> parseGrpcParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> grpcParams = new ArrayList<>();

        try {
            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            byte[] body = Arrays.copyOfRange(request, bodyOffset, request.length);

            if (body.length == 0) {
                return grpcParams;
            }

            // Add the entire gRPC body as a parameter for injection testing
            String bodyString = new String(body, StandardCharsets.UTF_8);
            grpcParams.add(new HttpParameter("grpc.body", bodyString, HttpParameter.Type.GRPC));

            // Try to parse as text-based protobuf (JSON format)
            if (bodyString.trim().startsWith("{")) {
                grpcParams.addAll(parseJsonRecursively(bodyString, "grpc"));
            }

            // Add gRPC headers as parameters
            List<String> headers = requestInfo.getHeaders();
            for (String header : headers) {
                if (header.toLowerCase().startsWith("grpc-")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        grpcParams.add(new HttpParameter("grpc.header." + name, value, HttpParameter.Type.GRPC));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing gRPC parameters: " + e.getMessage());
        }

        return grpcParams;
    }

    /**
     * Parse REST API path parameters and resource identifiers
     */
    private List<HttpParameter> parseRestApiParameters(IRequestInfo requestInfo) {
        List<HttpParameter> restParams = new ArrayList<>();

        try {
            String url = requestInfo.getUrl().getPath();
            String[] pathSegments = url.split("/");

            for (int i = 0; i < pathSegments.length; i++) {
                String segment = pathSegments[i];
                if (!segment.isEmpty()) {
                    // Check if segment looks like an ID or parameter
                    if (isLikelyApiParameter(segment)) {
                        restParams.add(new HttpParameter("rest.path[" + i + "]", segment, HttpParameter.Type.REST_PATH));

                        // Also add as potential ID parameter
                        if (isLikelyId(segment)) {
                            restParams.add(new HttpParameter("rest.id[" + i + "]", segment, HttpParameter.Type.REST_PATH));
                        }
                    }
                }
            }

            // Parse API versioning
            if (url.contains("/v") || url.contains("/api/")) {
                Pattern versionPattern = Pattern.compile("/v(\\d+(?:\\.\\d+)*)/");
                Matcher matcher = versionPattern.matcher(url);
                if (matcher.find()) {
                    restParams.add(new HttpParameter("rest.api_version", matcher.group(1), HttpParameter.Type.REST_PATH));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing REST API parameters: " + e.getMessage());
        }

        return restParams;
    }

    /**
     * Check if a path segment is likely an API parameter
     */
    private boolean isLikelyApiParameter(String segment) {
        // Check for numeric IDs
        if (segment.matches("\\d+")) return true;

        // Check for UUIDs
        if (segment.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")) return true;

        // Check for base64-like strings
        if (segment.length() > 10 && segment.matches("[A-Za-z0-9+/=]+")) return true;

        // Check for hex strings
        if (segment.length() > 8 && segment.matches("[0-9a-fA-F]+")) return true;

        // Check for encoded parameters
        if (segment.contains("%") || segment.contains("+")) return true;

        return false;
    }

    /**
     * Check if a segment looks like an ID
     */
    private boolean isLikelyId(String segment) {
        return segment.matches("\\d+") ||
               segment.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}") ||
               (segment.length() > 10 && segment.matches("[A-Za-z0-9+/=]+"));
    }

    /**
     * Parse authentication parameters (tokens, API keys, etc.)
     */
    private List<HttpParameter> parseAuthenticationParameters(IRequestInfo requestInfo) {
        List<HttpParameter> authParams = new ArrayList<>();

        try {
            List<String> headers = requestInfo.getHeaders();

            for (String header : headers) {
                String lowerHeader = header.toLowerCase();

                // Authorization header
                if (lowerHeader.startsWith("authorization:")) {
                    String authValue = header.substring("authorization:".length()).trim();
                    authParams.add(new HttpParameter("auth.authorization", authValue, HttpParameter.Type.AUTH));

                    // Parse different auth types
                    if (authValue.startsWith("Bearer ")) {
                        String token = authValue.substring("Bearer ".length());
                        authParams.add(new HttpParameter("auth.bearer_token", token, HttpParameter.Type.AUTH));

                        // Check if it's a JWT
                        if (token.contains(".")) {
                            authParams.addAll(parseJWTToken(token, "auth.jwt"));
                        }
                    } else if (authValue.startsWith("Basic ")) {
                        String credentials = authValue.substring("Basic ".length());
                        authParams.add(new HttpParameter("auth.basic_credentials", credentials, HttpParameter.Type.AUTH));
                    } else if (authValue.startsWith("Digest ")) {
                        String digest = authValue.substring("Digest ".length());
                        authParams.add(new HttpParameter("auth.digest", digest, HttpParameter.Type.AUTH));
                    }
                }

                // API Key headers
                if (lowerHeader.startsWith("x-api-key:") ||
                    lowerHeader.startsWith("api-key:") ||
                    lowerHeader.startsWith("apikey:")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        authParams.add(new HttpParameter("auth.api_key", parts[1].trim(), HttpParameter.Type.AUTH));
                    }
                }

                // Custom auth headers
                if (lowerHeader.startsWith("x-auth-") ||
                    lowerHeader.startsWith("x-token-") ||
                    lowerHeader.startsWith("x-session-")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        authParams.add(new HttpParameter("auth.custom." + name, value, HttpParameter.Type.AUTH));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing authentication parameters: " + e.getMessage());
        }

        return authParams;
    }

    /**
     * Parse structured header parameters (like Accept-Language, Cache-Control)
     */
    private List<HttpParameter> parseStructuredHeaderParameters(IRequestInfo requestInfo) {
        List<HttpParameter> structuredParams = new ArrayList<>();

        try {
            List<String> headers = requestInfo.getHeaders();

            for (String header : headers) {
                String lowerHeader = header.toLowerCase();

                // Parse Accept-Language header
                if (lowerHeader.startsWith("accept-language:")) {
                    String value = header.substring("accept-language:".length()).trim();
                    structuredParams.addAll(parseAcceptLanguageHeader(value));
                }

                // Parse Cache-Control header
                if (lowerHeader.startsWith("cache-control:")) {
                    String value = header.substring("cache-control:".length()).trim();
                    structuredParams.addAll(parseCacheControlHeader(value));
                }

                // Parse Accept header
                if (lowerHeader.startsWith("accept:")) {
                    String value = header.substring("accept:".length()).trim();
                    structuredParams.addAll(parseAcceptHeader(value));
                }

                // Parse custom structured headers (comma-separated values)
                if (lowerHeader.startsWith("x-") && header.contains(",")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        structuredParams.addAll(parseCommaSeparatedHeader(name, value));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing structured headers: " + e.getMessage());
        }

        return structuredParams;
    }

    /**
     * Parse Accept-Language header values
     */
    private List<HttpParameter> parseAcceptLanguageHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] languages = value.split(",");
        for (int i = 0; i < languages.length; i++) {
            String lang = languages[i].trim();
            params.add(new HttpParameter("accept_language[" + i + "]", lang, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse quality values
            if (lang.contains(";q=")) {
                String[] parts = lang.split(";q=");
                if (parts.length == 2) {
                    params.add(new HttpParameter("accept_language.lang[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                    params.add(new HttpParameter("accept_language.quality[" + i + "]", parts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                }
            }
        }

        return params;
    }

    /**
     * Parse Cache-Control header values
     */
    private List<HttpParameter> parseCacheControlHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] directives = value.split(",");
        for (int i = 0; i < directives.length; i++) {
            String directive = directives[i].trim();
            params.add(new HttpParameter("cache_control[" + i + "]", directive, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse directive values
            if (directive.contains("=")) {
                String[] parts = directive.split("=", 2);
                if (parts.length == 2) {
                    params.add(new HttpParameter("cache_control.directive[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                    params.add(new HttpParameter("cache_control.value[" + i + "]", parts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                }
            }
        }

        return params;
    }

    /**
     * Parse Accept header values
     */
    private List<HttpParameter> parseAcceptHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] mediaTypes = value.split(",");
        for (int i = 0; i < mediaTypes.length; i++) {
            String mediaType = mediaTypes[i].trim();
            params.add(new HttpParameter("accept[" + i + "]", mediaType, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse media type and parameters
            if (mediaType.contains(";")) {
                String[] parts = mediaType.split(";");
                params.add(new HttpParameter("accept.media_type[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));

                for (int j = 1; j < parts.length; j++) {
                    String param = parts[j].trim();
                    if (param.contains("=")) {
                        String[] paramParts = param.split("=", 2);
                        if (paramParts.length == 2) {
                            params.add(new HttpParameter("accept.param[" + i + "][" + j + "]", paramParts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                        }
                    }
                }
            }
        }

        return params;
    }

    /**
     * Parse comma-separated header values
     */
    private List<HttpParameter> parseCommaSeparatedHeader(String headerName, String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] values = value.split(",");
        for (int i = 0; i < values.length; i++) {
            String val = values[i].trim();
            params.add(new HttpParameter(headerName + "[" + i + "]", val, HttpParameter.Type.STRUCTURED_HEADER));
        }

        return params;
    }

    /**
     * Parse nested URL-encoded parameters (like param[key]=value)
     */
    private List<HttpParameter> parseNestedUrlEncodedParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> nestedParams = new ArrayList<>();

        try {
            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

            if (body.isEmpty()) {
                return nestedParams;
            }

            // Look for nested parameter patterns
            Pattern nestedPattern = Pattern.compile("([^&=]+)\\[([^\\]]+)\\]=([^&]*)");
            Matcher matcher = nestedPattern.matcher(body);

            while (matcher.find()) {
                String paramName = matcher.group(1);
                String key = matcher.group(2);
                String value = matcher.group(3);

                // Decode URL-encoded values
                try {
                    paramName = java.net.URLDecoder.decode(paramName, "UTF-8");
                    key = java.net.URLDecoder.decode(key, "UTF-8");
                    value = java.net.URLDecoder.decode(value, "UTF-8");
                } catch (Exception e) {
                    // Keep original values if decoding fails
                }

                nestedParams.add(new HttpParameter(paramName + "." + key, value, HttpParameter.Type.NESTED_FORM));
                nestedParams.add(new HttpParameter(paramName + "[" + key + "]", value, HttpParameter.Type.NESTED_FORM));
            }

        } catch (Exception e) {
            System.out.println("Error parsing nested URL-encoded parameters: " + e.getMessage());
        }

        return nestedParams;
    }

    /**
     * Parse form data with file uploads
     */
    private List<HttpParameter> parseFormDataWithFiles(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> fileParams = new ArrayList<>();

        try {
            if (!isMultipartRequest(requestInfo)) {
                return fileParams;
            }

            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

            // Find boundary
            String contentType = getContentType(requestInfo);
            if (contentType == null || !contentType.contains("boundary=")) {
                return fileParams;
            }

            String boundary = contentType.substring(contentType.indexOf("boundary=") + 9);
            if (boundary.contains(";")) {
                boundary = boundary.substring(0, boundary.indexOf(";"));
            }

            // Split by boundary
            String[] parts = body.split("--" + boundary);

            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                if (part.isEmpty() || part.equals("--")) continue;

                // Parse multipart section
                if (part.contains("Content-Disposition:")) {
                    fileParams.addAll(parseMultipartSection(part, i));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing form data with files: " + e.getMessage());
        }

        return fileParams;
    }

    /**
     * Parse individual multipart section
     */
    private List<HttpParameter> parseMultipartSection(String section, int index) {
        List<HttpParameter> sectionParams = new ArrayList<>();

        try {
            String[] lines = section.split("\r?\n");
            String name = null;
            String filename = null;
            String contentType = null;
            StringBuilder content = new StringBuilder();
            boolean inContent = false;

            for (String line : lines) {
                if (line.startsWith("Content-Disposition:")) {
                    // Parse name and filename
                    if (line.contains("name=\"")) {
                        int start = line.indexOf("name=\"") + 6;
                        int end = line.indexOf("\"", start);
                        if (end > start) {
                            name = line.substring(start, end);
                        }
                    }

                    if (line.contains("filename=\"")) {
                        int start = line.indexOf("filename=\"") + 10;
                        int end = line.indexOf("\"", start);
                        if (end > start) {
                            filename = line.substring(start, end);
                        }
                    }
                } else if (line.startsWith("Content-Type:")) {
                    contentType = line.substring("Content-Type:".length()).trim();
                } else if (line.isEmpty() && !inContent) {
                    inContent = true;
                } else if (inContent) {
                    if (content.length() > 0) content.append("\n");
                    content.append(line);
                }
            }

            if (name != null) {
                String value = content.toString();

                if (filename != null) {
                    // File upload
                    sectionParams.add(new HttpParameter("file." + name, value, HttpParameter.Type.FILE_UPLOAD));
                    sectionParams.add(new HttpParameter("file." + name + ".filename", filename, HttpParameter.Type.FILE_UPLOAD));
                    if (contentType != null) {
                        sectionParams.add(new HttpParameter("file." + name + ".content_type", contentType, HttpParameter.Type.FILE_UPLOAD));
                    }
                } else {
                    // Regular form field
                    sectionParams.add(new HttpParameter("form." + name, value, HttpParameter.Type.MULTIPART));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing multipart section: " + e.getMessage());
        }

        return sectionParams;
    }
}