package com.timebasedscan.utils;

import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IParameter;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for parsing HTTP parameters from requests
 */
public class HttpParameterParser {

    private IExtensionHelpers helpers;
    
    // Regex patterns for JSON parameters
    private static final Pattern JSON_PARAM_PATTERN = Pattern.compile("\"([^\"]+)\"\\s*:\\s*([^,}\\]]+|\"[^\"]*\"|\\{[^}]*\\}|\\[[^\\]]*\\])");
    
    // Regex patterns for XML parameters
    private static final Pattern XML_TAG_PATTERN = Pattern.compile("<([^\\s>/]+)[^>]*>(.*?)</\\1>", Pattern.DOTALL);
    private static final Pattern XML_SELF_CLOSING_PATTERN = Pattern.compile("<([^\\s>/]+)[^>]*/>");
    
    public HttpParameterParser(IExtensionHelpers helpers) {
        this.helpers = helpers;
    }
    
    /**
     * Parse parameters from a request
     * @param requestResponse The request to parse
     * @return List of parameters
     */
    public List<HttpParameter> parseParameters(IHttpRequestResponse requestResponse) {
        List<HttpParameter> parameters = new ArrayList<>();

        byte[] request = requestResponse.getRequest();
        IRequestInfo requestInfo = helpers.analyzeRequest(requestResponse);
        
        // Get standard parameters (URL, body, cookie)
        List<IParameter> burpParams = requestInfo.getParameters();
        System.out.println("=== HttpParameterParser.parseParameters() START ===");
        System.out.println("Burp found " + burpParams.size() + " standard parameters");
        for (IParameter param : burpParams) {
            System.out.println("  Burp param: " + param.getName() + " = " + param.getValue() + " (type=" + param.getType() + ")");
            HttpParameter.Type type;

            switch (param.getType()) {
                case IParameter.PARAM_URL:
                    type = HttpParameter.Type.URL;
                    break;
                case IParameter.PARAM_BODY:
                    type = HttpParameter.Type.BODY;
                    break;
                case IParameter.PARAM_COOKIE:
                    type = HttpParameter.Type.COOKIE;
                    break;
                case IParameter.PARAM_XML:
                    type = HttpParameter.Type.XML;
                    break;
                case IParameter.PARAM_XML_ATTR:
                    type = HttpParameter.Type.XML;
                    break;
                case IParameter.PARAM_MULTIPART_ATTR:
                    type = HttpParameter.Type.MULTIPART;
                    break;
                case IParameter.PARAM_JSON:
                    type = HttpParameter.Type.JSON;
                    break;
                default:
                    type = HttpParameter.Type.BODY;
            }

            // Add parameter value for injection
            parameters.add(new HttpParameter(param.getName(), param.getValue(), type));

            // Add parameter name for injection (if not empty)
            if (param.getName() != null && !param.getName().trim().isEmpty()) {
                parameters.add(new HttpParameter(param.getName() + "__NAME__", param.getName(), type));
            }
        }
        
        // Add any JSON parameters that Burp might have missed
        if (isJsonRequest(requestInfo)) {
            parameters.addAll(parseJsonParameters(request, requestInfo));
        }

        // Add any XML parameters that Burp might have missed
        if (isXmlRequest(requestInfo)) {
            parameters.addAll(parseXmlParameters(request, requestInfo));
        }

        // Add any multipart parameters that Burp might have missed
        if (isMultipartRequest(requestInfo)) {
            parameters.addAll(parseMultipartParameters(request, requestInfo));
        }

        // Parse any body content as a single parameter for injection testing
        parameters.addAll(parseBodyAsParameter(request, requestInfo));

        // Parse URL path segments for injection testing
        parameters.addAll(parseUrlPathParameters(requestInfo));

        // Parse headers for potential injection points
        parameters.addAll(parseHeaderParameters(requestInfo));

        // Parse WebSocket upgrade requests
        if (isWebSocketRequest(requestInfo)) {
            parameters.addAll(parseWebSocketParameters(request, requestInfo));
        }

        // Parse JWT tokens from headers
        parameters.addAll(parseJWTTokens(requestInfo));

        // Parse Base64 encoded data
        parameters.addAll(parseBase64EncodedData(request, requestInfo));

        // Parse binary formats (MessagePack, CBOR, etc.)
        parameters.addAll(parseBinaryFormats(request, requestInfo));

        // Parse configuration formats (YAML, TOML, INI)
        parameters.addAll(parseConfigurationFormats(request, requestInfo));

        // Parse template formats (Handlebars, Jinja2)
        parameters.addAll(parseTemplateFormats(request, requestInfo));

        // Parse Server-Sent Events
        if (isServerSentEventsRequest(requestInfo)) {
            parameters.addAll(parseServerSentEvents(request, requestInfo));
        }

        // Parse gRPC/Protocol Buffers
        if (isGrpcRequest(requestInfo)) {
            parameters.addAll(parseGrpcParameters(request, requestInfo));
        }

        // Parse custom API formats (REST path parameters, API keys)
        parameters.addAll(parseRestApiParameters(requestInfo));

        // Parse authentication tokens and API keys
        parameters.addAll(parseAuthenticationParameters(requestInfo));

        // Parse custom headers with structured data
        parameters.addAll(parseStructuredHeaderParameters(requestInfo));

        // Parse URL-encoded nested parameters
        parameters.addAll(parseNestedUrlEncodedParameters(request, requestInfo));

        // Parse form data with file uploads
        parameters.addAll(parseFormDataWithFiles(request, requestInfo));

        // Apply advanced parameter filtering and processing
        parameters = applyAdvancedParameterFiltering(parameters);

        // Add parameter categorization
        parameters = categorizeParameters(parameters);

        // Remove duplicates and system parameters
        parameters = filterSystemAndDuplicateParameters(parameters);

        // Validate and sanitize parameters for injection testing
        parameters = validateAndSanitizeParameters(parameters);

        // TEMPORARY DEBUG: Add test parameters if none found
        if (parameters.isEmpty()) {
            System.out.println("WARNING: No parameters found, adding test parameters for debugging");
            parameters.add(new HttpParameter("TEST_PARAM_1", "test_value_1", HttpParameter.Type.BODY));
            parameters.add(new HttpParameter("TEST_PARAM_2", "test_value_2", HttpParameter.Type.URL));
        }

        System.out.println("=== HttpParameterParser.parseParameters() END ===");
        System.out.println("Total parameters found: " + parameters.size());
        for (int i = 0; i < Math.min(parameters.size(), 10); i++) {
            HttpParameter param = parameters.get(i);
            System.out.println("  [" + i + "] " + param.getName() + " = " + param.getValue() + " (" + param.getType() + ")");
        }
        if (parameters.size() > 10) {
            System.out.println("  ... and " + (parameters.size() - 10) + " more parameters");
        }

        return parameters;
    }
    
    /**
     * Check if request is JSON
     */
    private boolean isJsonRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && (
            contentType.contains("application/json") || 
            contentType.contains("text/json") ||
            contentType.contains("application/graphql")
        );
    }
    
    /**
     * Check if request is XML
     */
    private boolean isXmlRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && (
            contentType.contains("application/xml") ||
            contentType.contains("text/xml") ||
            contentType.contains("application/soap+xml")
        );
    }

    /**
     * Check if request is multipart
     */
    private boolean isMultipartRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && contentType.contains("multipart/");
    }
    
    /**
     * Get content type from headers
     */
    private String getContentType(IRequestInfo requestInfo) {
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:")) {
                return header.substring("content-type:".length()).trim().toLowerCase();
            }
        }
        return null;
    }
    
    /**
     * Parse JSON parameters from request - Enhanced to support nested JSON, arrays, and all value types
     */
    private List<HttpParameter> parseJsonParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> jsonParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length)).trim();

        if (body.isEmpty()) {
            return jsonParams;
        }

        System.out.println("Parsing JSON body: " + body.substring(0, Math.min(200, body.length())) + "...");

        try {
            // Enhanced JSON parsing with comprehensive type detection
            jsonParams.addAll(parseJsonRecursively(body, ""));

            // Parse JSON arrays at root level
            if (body.startsWith("[") && body.endsWith("]")) {
                jsonParams.addAll(parseJsonRootArray(body));
            }

            // Parse for potential JSON-in-JSON (escaped JSON strings)
            jsonParams.addAll(parseEscapedJsonStrings(body));

            // Enhanced GraphQL parsing with comprehensive detection
            if (isGraphQLRequest(body)) {
                jsonParams.addAll(parseGraphQLParametersEnhanced(body));
            }

            // Parse for JSON-RPC format
            if (isJsonRpcRequest(body)) {
                jsonParams.addAll(parseJsonRpcParameters(body));
            }

            // Parse for JSON Patch format
            if (isJsonPatchRequest(body)) {
                jsonParams.addAll(parseJsonPatchParameters(body));
            }

            System.out.println("Total JSON parameters found: " + jsonParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing JSON: " + e.getMessage());
            // Enhanced fallback parsing with multiple strategies
            jsonParams.addAll(parseJsonWithRegex(body));
            jsonParams.addAll(parseJsonWithFallbackStrategies(body));
        }

        return jsonParams;
    }

    /**
     * Recursively parse JSON to extract all parameters including nested objects and arrays
     */
    private List<HttpParameter> parseJsonRecursively(String jsonString, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        jsonString = jsonString.trim();
        if (jsonString.isEmpty()) {
            return params;
        }

        try {
            if (jsonString.startsWith("{") && jsonString.endsWith("}")) {
                // Parse JSON object
                params.addAll(parseJsonObject(jsonString, parentPath));
            } else if (jsonString.startsWith("[") && jsonString.endsWith("]")) {
                // Parse JSON array
                params.addAll(parseJsonArray(jsonString, parentPath));
            } else {
                // Single value - create parameter if we have a parent path
                if (!parentPath.isEmpty()) {
                    String value = cleanJsonValue(jsonString);
                    params.add(new HttpParameter(parentPath, value, HttpParameter.Type.JSON));
                }
            }
        } catch (Exception e) {
            System.out.println("Error in parseJsonRecursively: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse JSON object and extract key-value pairs
     */
    private List<HttpParameter> parseJsonObject(String jsonObject, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Remove outer braces
        String content = jsonObject.substring(1, jsonObject.length() - 1).trim();

        if (content.isEmpty()) {
            return params;
        }

        // Parse key-value pairs
        List<String> keyValuePairs = splitJsonKeyValuePairs(content);

        for (String pair : keyValuePairs) {
            try {
                int colonIndex = findJsonKeyValueSeparator(pair);
                if (colonIndex > 0) {
                    String key = pair.substring(0, colonIndex).trim();
                    String value = pair.substring(colonIndex + 1).trim();

                    // Clean the key (remove quotes)
                    key = cleanJsonKey(key);

                    // Build full path
                    String fullPath = parentPath.isEmpty() ? key : parentPath + "." + key;

                    // Add parameter value for injection
                    String cleanValue = cleanJsonValue(value);
                    params.add(new HttpParameter(fullPath, cleanValue, HttpParameter.Type.JSON));

                    // Add parameter name for injection
                    params.add(new HttpParameter(fullPath + "__NAME__", key, HttpParameter.Type.JSON));

                    // If value is an object or array, parse recursively
                    if ((value.startsWith("{") && value.endsWith("}")) ||
                        (value.startsWith("[") && value.endsWith("]"))) {
                        params.addAll(parseJsonRecursively(value, fullPath));
                    }
                }
            } catch (Exception e) {
                System.out.println("Error parsing key-value pair: " + pair + " - " + e.getMessage());
            }
        }

        return params;
    }

    /**
     * Parse JSON array and extract elements
     */
    private List<HttpParameter> parseJsonArray(String jsonArray, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Remove outer brackets
        String content = jsonArray.substring(1, jsonArray.length() - 1).trim();

        if (content.isEmpty()) {
            return params;
        }

        // Split array elements
        List<String> elements = splitJsonArrayElements(content);

        for (int i = 0; i < elements.size(); i++) {
            String element = elements.get(i).trim();
            String arrayPath = parentPath + "[" + i + "]";

            // Add this array element as a parameter
            String cleanValue = cleanJsonValue(element);
            params.add(new HttpParameter(arrayPath, cleanValue, HttpParameter.Type.JSON));

            // If element is an object or array, parse recursively
            if ((element.startsWith("{") && element.endsWith("}")) ||
                (element.startsWith("[") && element.endsWith("]"))) {
                params.addAll(parseJsonRecursively(element, arrayPath));
            }
        }

        return params;
    }

    /**
     * Helper method to split JSON key-value pairs while respecting nested structures
     */
    private List<String> splitJsonKeyValuePairs(String content) {
        List<String> pairs = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int braceDepth = 0;
        int bracketDepth = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);

            if (escaped) {
                escaped = false;
                current.append(c);
                continue;
            }

            if (c == '\\') {
                escaped = true;
                current.append(c);
                continue;
            }

            if (c == '"') {
                inString = !inString;
                current.append(c);
                continue;
            }

            if (!inString) {
                if (c == '{') braceDepth++;
                else if (c == '}') braceDepth--;
                else if (c == '[') bracketDepth++;
                else if (c == ']') bracketDepth--;
                else if (c == ',' && braceDepth == 0 && bracketDepth == 0) {
                    pairs.add(current.toString().trim());
                    current = new StringBuilder();
                    continue;
                }
            }

            current.append(c);
        }

        if (current.length() > 0) {
            pairs.add(current.toString().trim());
        }

        return pairs;
    }

    /**
     * Helper method to split JSON array elements while respecting nested structures
     */
    private List<String> splitJsonArrayElements(String content) {
        List<String> elements = new ArrayList<>();
        StringBuilder currentElement = new StringBuilder();
        int depth = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);

            if (escaped) {
                currentElement.append(c);
                escaped = false;
                continue;
            }

            if (c == '\\' && inString) {
                escaped = true;
                currentElement.append(c);
                continue;
            }

            if (c == '"') {
                inString = !inString;
                currentElement.append(c);
                continue;
            }

            if (!inString) {
                if (c == '{' || c == '[') {
                    depth++;
                } else if (c == '}' || c == ']') {
                    depth--;
                } else if (c == ',' && depth == 0) {
                    // Found element separator at root level
                    elements.add(currentElement.toString().trim());
                    currentElement = new StringBuilder();
                    continue;
                }
            }

            currentElement.append(c);
        }

        // Add the last element
        if (currentElement.length() > 0) {
            elements.add(currentElement.toString().trim());
        }

        return elements;
    }

    /**
     * Find the colon separator in a JSON key-value pair
     */
    private int findJsonKeyValueSeparator(String pair) {
        boolean inString = false;
        boolean escaped = false;
        int braceDepth = 0;
        int bracketDepth = 0;

        for (int i = 0; i < pair.length(); i++) {
            char c = pair.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '{') braceDepth++;
                else if (c == '}') braceDepth--;
                else if (c == '[') bracketDepth++;
                else if (c == ']') bracketDepth--;
                else if (c == ':' && braceDepth == 0 && bracketDepth == 0) {
                    return i;
                }
            }
        }

        return -1;
    }

    /**
     * Clean JSON key by removing quotes
     */
    private String cleanJsonKey(String key) {
        key = key.trim();
        if (key.startsWith("\"") && key.endsWith("\"")) {
            return key.substring(1, key.length() - 1);
        }
        return key;
    }

    /**
     * Clean JSON value by removing quotes for strings, keeping original for others
     */
    private String cleanJsonValue(String value) {
        value = value.trim();

        // For string values, remove quotes but keep the content
        if (value.startsWith("\"") && value.endsWith("\"")) {
            return value.substring(1, value.length() - 1);
        }

        // For other values (numbers, booleans, null, objects, arrays), keep as-is
        return value;
    }

    /**
     * Fallback regex-based JSON parsing for when the recursive parser fails
     */
    private List<HttpParameter> parseJsonWithRegex(String body) {
        List<HttpParameter> jsonParams = new ArrayList<>();

        // Use the original regex pattern as fallback
        Matcher matcher = JSON_PARAM_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            String value = matcher.group(2).trim();

            // Remove quotes from string values
            if (value.startsWith("\"") && value.endsWith("\"")) {
                value = value.substring(1, value.length() - 1);
            }

            jsonParams.add(new HttpParameter(name, value, HttpParameter.Type.JSON));
        }

        return jsonParams;
    }

    /**
     * Parse JSON root array for parameters
     */
    private List<HttpParameter> parseJsonRootArray(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Remove outer brackets
            String content = body.substring(1, body.length() - 1).trim();

            if (content.isEmpty()) {
                return params;
            }

            // Split array elements more carefully
            List<String> elements = splitJsonArrayElements(content);

            for (int i = 0; i < elements.size(); i++) {
                String element = elements.get(i).trim();
                String arrayPath = "root_array[" + i + "]";

                // Parse each array element recursively
                if ((element.startsWith("{") && element.endsWith("}")) ||
                    (element.startsWith("[") && element.endsWith("]"))) {
                    params.addAll(parseJsonRecursively(element, arrayPath));
                } else {
                    // Simple value
                    String cleanValue = cleanJsonValue(element);
                    params.add(new HttpParameter(arrayPath, cleanValue, HttpParameter.Type.JSON));
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing JSON root array: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse escaped JSON strings within JSON
     */
    private List<HttpParameter> parseEscapedJsonStrings(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for escaped JSON patterns like "data": "{\"key\":\"value\"}"
            Pattern escapedJsonPattern = Pattern.compile("\"([^\"]+)\"\\s*:\\s*\"(\\{[^\"]*\\}|\\[[^\"]*\\])\"");
            Matcher matcher = escapedJsonPattern.matcher(body);

            while (matcher.find()) {
                String key = matcher.group(1);
                String escapedJson = matcher.group(2);

                // Unescape the JSON string
                String unescapedJson = escapedJson.replace("\\\"", "\"")
                                                 .replace("\\\\", "\\")
                                                 .replace("\\/", "/");

                // Parse the unescaped JSON
                params.addAll(parseJsonRecursively(unescapedJson, "escaped." + key));
            }
        } catch (Exception e) {
            System.out.println("Error parsing escaped JSON strings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if request is JSON-RPC format
     */
    private boolean isJsonRpcRequest(String body) {
        return body.contains("\"jsonrpc\"") &&
               (body.contains("\"method\"") || body.contains("\"result\"") || body.contains("\"error\""));
    }

    /**
     * Parse JSON-RPC parameters
     */
    private List<HttpParameter> parseJsonRpcParameters(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse standard JSON-RPC fields
            params.addAll(parseJsonRecursively(body, "jsonrpc"));

            // Extract specific JSON-RPC components
            String method = extractJsonRpcField(body, "method");
            if (!method.isEmpty()) {
                params.add(new HttpParameter("jsonrpc.method", method, HttpParameter.Type.JSON));
            }

            String id = extractJsonRpcField(body, "id");
            if (!id.isEmpty()) {
                params.add(new HttpParameter("jsonrpc.id", id, HttpParameter.Type.JSON));
            }

            // Parse params array/object if present
            String paramsField = extractJsonRpcField(body, "params");
            if (!paramsField.isEmpty()) {
                params.addAll(parseJsonRecursively(paramsField, "jsonrpc.params"));
            }

        } catch (Exception e) {
            System.out.println("Error parsing JSON-RPC: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if request is JSON Patch format
     */
    private boolean isJsonPatchRequest(String body) {
        return body.contains("\"op\"") &&
               (body.contains("\"add\"") || body.contains("\"remove\"") ||
                body.contains("\"replace\"") || body.contains("\"move\"") ||
                body.contains("\"copy\"") || body.contains("\"test\""));
    }

    /**
     * Parse JSON Patch parameters
     */
    private List<HttpParameter> parseJsonPatchParameters(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse as regular JSON first
            params.addAll(parseJsonRecursively(body, "jsonpatch"));

            // Extract JSON Patch specific operations
            if (body.startsWith("[")) {
                // Array of operations
                List<String> operations = splitJsonArrayElements(body.substring(1, body.length() - 1));

                for (int i = 0; i < operations.size(); i++) {
                    String op = operations.get(i).trim();
                    String opPath = "jsonpatch.op[" + i + "]";

                    // Extract operation details
                    String operation = extractJsonRpcField(op, "op");
                    String path = extractJsonRpcField(op, "path");
                    String value = extractJsonRpcField(op, "value");

                    if (!operation.isEmpty()) {
                        params.add(new HttpParameter(opPath + ".operation", operation, HttpParameter.Type.JSON));
                    }
                    if (!path.isEmpty()) {
                        params.add(new HttpParameter(opPath + ".path", path, HttpParameter.Type.JSON));
                    }
                    if (!value.isEmpty()) {
                        params.add(new HttpParameter(opPath + ".value", value, HttpParameter.Type.JSON));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing JSON Patch: " + e.getMessage());
        }

        return params;
    }

    /**
     * Enhanced fallback JSON parsing with multiple strategies
     */
    private List<HttpParameter> parseJsonWithFallbackStrategies(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Strategy 1: Extract all quoted strings as potential values
            Pattern quotedStringPattern = Pattern.compile("\"([^\"\\\\]*(\\\\.[^\"\\\\]*)*)\"");
            Matcher matcher = quotedStringPattern.matcher(body);
            int stringIndex = 0;

            while (matcher.find()) {
                String value = matcher.group(1);
                if (value.length() > 2) { // Skip very short strings
                    params.add(new HttpParameter("fallback.string[" + stringIndex + "]", value, HttpParameter.Type.JSON));
                    stringIndex++;
                }
            }

            // Strategy 2: Extract numeric values
            Pattern numberPattern = Pattern.compile(":\\s*(-?\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)");
            matcher = numberPattern.matcher(body);
            int numberIndex = 0;

            while (matcher.find()) {
                String value = matcher.group(1);
                params.add(new HttpParameter("fallback.number[" + numberIndex + "]", value, HttpParameter.Type.JSON));
                numberIndex++;
            }

            // Strategy 3: Extract boolean values
            Pattern booleanPattern = Pattern.compile(":\\s*(true|false)");
            matcher = booleanPattern.matcher(body);
            int boolIndex = 0;

            while (matcher.find()) {
                String value = matcher.group(1);
                params.add(new HttpParameter("fallback.boolean[" + boolIndex + "]", value, HttpParameter.Type.JSON));
                boolIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error in fallback JSON parsing: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if the request body contains GraphQL
     */
    private boolean isGraphQLRequest(String body) {
        return body.contains("\"query\"") || body.contains("\"mutation\"") ||
               body.contains("\"subscription\"") || body.contains("\"variables\"") ||
               body.contains("query {") || body.contains("mutation {") ||
               body.contains("subscription {");
    }

    /**
     * Parse GraphQL parameters from request body
     */
    private List<HttpParameter> parseGraphQLParameters(String body) {
        List<HttpParameter> graphqlParams = new ArrayList<>();

        try {
            // Parse GraphQL query/mutation/subscription
            if (body.contains("\"query\"")) {
                String query = extractGraphQLField(body, "query");
                if (!query.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.query", query, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(query, "graphql.query"));
                }
            }

            if (body.contains("\"mutation\"")) {
                String mutation = extractGraphQLField(body, "mutation");
                if (!mutation.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.mutation", mutation, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(mutation, "graphql.mutation"));
                }
            }

            if (body.contains("\"subscription\"")) {
                String subscription = extractGraphQLField(body, "subscription");
                if (!subscription.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.subscription", subscription, HttpParameter.Type.GRAPHQL));
                    graphqlParams.addAll(parseGraphQLQueryString(subscription, "graphql.subscription"));
                }
            }

            // Parse GraphQL variables
            if (body.contains("\"variables\"")) {
                String variables = extractGraphQLField(body, "variables");
                if (!variables.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.variables", variables, HttpParameter.Type.GRAPHQL));
                    // Parse variables as JSON
                    graphqlParams.addAll(parseJsonRecursively(variables, "graphql.variables"));
                }
            }

            // Parse operation name
            if (body.contains("\"operationName\"")) {
                String operationName = extractGraphQLField(body, "operationName");
                if (!operationName.isEmpty()) {
                    graphqlParams.add(new HttpParameter("graphql.operationName", operationName, HttpParameter.Type.GRAPHQL));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL: " + e.getMessage());
        }

        return graphqlParams;
    }

    /**
     * Enhanced GraphQL parameter parsing with comprehensive detection
     */
    private List<HttpParameter> parseGraphQLParametersEnhanced(String body) {
        List<HttpParameter> graphqlParams = new ArrayList<>();

        try {
            // First, try the original parsing method
            graphqlParams.addAll(parseGraphQLParameters(body));

            // Enhanced GraphQL introspection query detection
            if (isIntrospectionQuery(body)) {
                graphqlParams.add(new HttpParameter("graphql.introspection", "true", HttpParameter.Type.GRAPHQL));
                graphqlParams.addAll(parseIntrospectionQuery(body));
            }

            // Parse GraphQL fragments
            graphqlParams.addAll(parseGraphQLFragments(body));

            // Parse GraphQL directives
            graphqlParams.addAll(parseGraphQLDirectives(body));

            // Parse GraphQL aliases
            graphqlParams.addAll(parseGraphQLAliases(body));

            // Parse nested GraphQL selections
            graphqlParams.addAll(parseNestedGraphQLSelections(body));

            // Parse GraphQL variable definitions
            graphqlParams.addAll(parseGraphQLVariableDefinitions(body));

            // Parse GraphQL input objects
            graphqlParams.addAll(parseGraphQLInputObjects(body));

            // Parse GraphQL enums and scalars
            graphqlParams.addAll(parseGraphQLEnumsAndScalars(body));

            // Enhanced argument parsing with complex types
            graphqlParams.addAll(parseComplexGraphQLArguments(body));

        } catch (Exception e) {
            System.out.println("Error in enhanced GraphQL parsing: " + e.getMessage());
        }

        return graphqlParams;
    }

    /**
     * Check if query is an introspection query
     */
    private boolean isIntrospectionQuery(String body) {
        return body.contains("__schema") || body.contains("__type") ||
               body.contains("__typename") || body.contains("__Field") ||
               body.contains("__Directive") || body.contains("__EnumValue");
    }

    /**
     * Parse GraphQL introspection queries
     */
    private List<HttpParameter> parseIntrospectionQuery(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] introspectionFields = {"__schema", "__type", "__typename", "__Field", "__Directive", "__EnumValue"};

            for (String field : introspectionFields) {
                if (body.contains(field)) {
                    params.add(new HttpParameter("graphql.introspection." + field, "true", HttpParameter.Type.GRAPHQL));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing introspection query: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL fragments
     */
    private List<HttpParameter> parseGraphQLFragments(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for fragment definitions: fragment FragmentName on TypeName
            Pattern fragmentPattern = Pattern.compile("fragment\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s+on\\s+([a-zA-Z_][a-zA-Z0-9_]*)");
            Matcher matcher = fragmentPattern.matcher(body);

            int fragmentIndex = 0;
            while (matcher.find() && fragmentIndex < 10) {
                String fragmentName = matcher.group(1);
                String typeName = matcher.group(2);

                params.add(new HttpParameter("graphql.fragment[" + fragmentIndex + "].name", fragmentName, HttpParameter.Type.GRAPHQL));
                params.add(new HttpParameter("graphql.fragment[" + fragmentIndex + "].type", typeName, HttpParameter.Type.GRAPHQL));
                fragmentIndex++;
            }

            // Look for fragment spreads: ...FragmentName
            Pattern spreadPattern = Pattern.compile("\\.\\.\\.([a-zA-Z_][a-zA-Z0-9_]*)");
            Matcher spreadMatcher = spreadPattern.matcher(body);

            int spreadIndex = 0;
            while (spreadMatcher.find() && spreadIndex < 10) {
                String fragmentName = spreadMatcher.group(1);
                params.add(new HttpParameter("graphql.fragment_spread[" + spreadIndex + "]", fragmentName, HttpParameter.Type.GRAPHQL));
                spreadIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL fragments: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL directives
     */
    private List<HttpParameter> parseGraphQLDirectives(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for directives: @directiveName or @directiveName(arg: value)
            Pattern directivePattern = Pattern.compile("@([a-zA-Z_][a-zA-Z0-9_]*)(?:\\(([^)]+)\\))?");
            Matcher matcher = directivePattern.matcher(body);

            int directiveIndex = 0;
            while (matcher.find() && directiveIndex < 10) {
                String directiveName = matcher.group(1);
                String directiveArgs = matcher.group(2);

                params.add(new HttpParameter("graphql.directive[" + directiveIndex + "].name", directiveName, HttpParameter.Type.GRAPHQL));

                if (directiveArgs != null && !directiveArgs.trim().isEmpty()) {
                    params.add(new HttpParameter("graphql.directive[" + directiveIndex + "].args", directiveArgs, HttpParameter.Type.GRAPHQL));

                    // Parse directive arguments
                    Pattern argPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([^,\\)]+)");
                    Matcher argMatcher = argPattern.matcher(directiveArgs);

                    int argIndex = 0;
                    while (argMatcher.find() && argIndex < 5) {
                        String argName = argMatcher.group(1);
                        String argValue = argMatcher.group(2).trim();

                        params.add(new HttpParameter("graphql.directive[" + directiveIndex + "].arg." + argName, argValue, HttpParameter.Type.GRAPHQL));
                        argIndex++;
                    }
                }

                directiveIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL directives: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL aliases
     */
    private List<HttpParameter> parseGraphQLAliases(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for aliases: aliasName: fieldName
            Pattern aliasPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([a-zA-Z_][a-zA-Z0-9_]*)(?:\\s*\\(|\\s*\\{|\\s|$)");
            Matcher matcher = aliasPattern.matcher(body);

            int aliasIndex = 0;
            while (matcher.find() && aliasIndex < 10) {
                String aliasName = matcher.group(1);
                String fieldName = matcher.group(2);

                // Skip if it looks like an argument (contains quotes or numbers)
                if (!isGraphQLKeyword(aliasName) && !isGraphQLKeyword(fieldName)) {
                    params.add(new HttpParameter("graphql.alias[" + aliasIndex + "].name", aliasName, HttpParameter.Type.GRAPHQL));
                    params.add(new HttpParameter("graphql.alias[" + aliasIndex + "].field", fieldName, HttpParameter.Type.GRAPHQL));
                    aliasIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL aliases: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse nested GraphQL selections
     */
    private List<HttpParameter> parseNestedGraphQLSelections(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for nested field selections with braces
            Pattern nestedPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\{([^{}]+)\\}");
            Matcher matcher = nestedPattern.matcher(body);

            int nestedIndex = 0;
            while (matcher.find() && nestedIndex < 10) {
                String parentField = matcher.group(1);
                String nestedFields = matcher.group(2);

                if (!isGraphQLKeyword(parentField)) {
                    params.add(new HttpParameter("graphql.nested[" + nestedIndex + "].parent", parentField, HttpParameter.Type.GRAPHQL));
                    params.add(new HttpParameter("graphql.nested[" + nestedIndex + "].fields", nestedFields.trim(), HttpParameter.Type.GRAPHQL));

                    // Parse individual nested fields
                    String[] fields = nestedFields.split("\\s+");
                    for (int i = 0; i < fields.length && i < 5; i++) {
                        String field = fields[i].trim();
                        if (!field.isEmpty() && !isGraphQLKeyword(field)) {
                            params.add(new HttpParameter("graphql.nested[" + nestedIndex + "].field[" + i + "]", field, HttpParameter.Type.GRAPHQL));
                        }
                    }

                    nestedIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing nested GraphQL selections: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL variable definitions
     */
    private List<HttpParameter> parseGraphQLVariableDefinitions(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for variable definitions: $variableName: Type or $variableName: Type!
            Pattern varDefPattern = Pattern.compile("\\$([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([a-zA-Z_][a-zA-Z0-9_\\[\\]!]+)");
            Matcher matcher = varDefPattern.matcher(body);

            int varIndex = 0;
            while (matcher.find() && varIndex < 10) {
                String varName = matcher.group(1);
                String varType = matcher.group(2);

                params.add(new HttpParameter("graphql.variable_def[" + varIndex + "].name", varName, HttpParameter.Type.GRAPHQL));
                params.add(new HttpParameter("graphql.variable_def[" + varIndex + "].type", varType, HttpParameter.Type.GRAPHQL));

                // Check if it's required (ends with !)
                if (varType.endsWith("!")) {
                    params.add(new HttpParameter("graphql.variable_def[" + varIndex + "].required", "true", HttpParameter.Type.GRAPHQL));
                }

                // Check if it's a list (contains [])
                if (varType.contains("[") && varType.contains("]")) {
                    params.add(new HttpParameter("graphql.variable_def[" + varIndex + "].is_list", "true", HttpParameter.Type.GRAPHQL));
                }

                varIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL variable definitions: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL input objects
     */
    private List<HttpParameter> parseGraphQLInputObjects(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for input object patterns: {field: value, field2: value2}
            Pattern inputPattern = Pattern.compile("\\{([^{}]+)\\}");
            Matcher matcher = inputPattern.matcher(body);

            int inputIndex = 0;
            while (matcher.find() && inputIndex < 5) {
                String inputContent = matcher.group(1);

                // Parse individual fields in the input object
                Pattern fieldPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([^,}]+)");
                Matcher fieldMatcher = fieldPattern.matcher(inputContent);

                int fieldIndex = 0;
                while (fieldMatcher.find() && fieldIndex < 10) {
                    String fieldName = fieldMatcher.group(1);
                    String fieldValue = fieldMatcher.group(2).trim();

                    // Clean up the value
                    if (fieldValue.startsWith("\"") && fieldValue.endsWith("\"")) {
                        fieldValue = fieldValue.substring(1, fieldValue.length() - 1);
                    }

                    params.add(new HttpParameter("graphql.input[" + inputIndex + "]." + fieldName, fieldValue, HttpParameter.Type.GRAPHQL));
                    fieldIndex++;
                }

                inputIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL input objects: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse GraphQL enums and scalars
     */
    private List<HttpParameter> parseGraphQLEnumsAndScalars(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for enum values (uppercase constants)
            Pattern enumPattern = Pattern.compile("\\b([A-Z][A-Z0-9_]*)\\b");
            Matcher matcher = enumPattern.matcher(body);

            int enumIndex = 0;
            while (matcher.find() && enumIndex < 10) {
                String enumValue = matcher.group(1);

                // Skip common GraphQL keywords
                if (!enumValue.equals("ID") && !enumValue.equals("STRING") &&
                    !enumValue.equals("INT") && !enumValue.equals("FLOAT") &&
                    !enumValue.equals("BOOLEAN")) {
                    params.add(new HttpParameter("graphql.enum[" + enumIndex + "]", enumValue, HttpParameter.Type.GRAPHQL));
                    enumIndex++;
                }
            }

            // Look for scalar type usage
            String[] scalarTypes = {"ID", "String", "Int", "Float", "Boolean", "DateTime", "Date", "Time", "JSON"};
            for (String scalar : scalarTypes) {
                if (body.contains(scalar)) {
                    params.add(new HttpParameter("graphql.scalar." + scalar.toLowerCase(), "used", HttpParameter.Type.GRAPHQL));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL enums and scalars: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse complex GraphQL arguments
     */
    private List<HttpParameter> parseComplexGraphQLArguments(String body) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for complex arguments with nested structures
            Pattern complexArgPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*(\\{[^{}]*\\}|\\[[^\\[\\]]*\\])");
            Matcher matcher = complexArgPattern.matcher(body);

            int argIndex = 0;
            while (matcher.find() && argIndex < 10) {
                String argName = matcher.group(1);
                String argValue = matcher.group(2);

                params.add(new HttpParameter("graphql.complex_arg[" + argIndex + "].name", argName, HttpParameter.Type.GRAPHQL));
                params.add(new HttpParameter("graphql.complex_arg[" + argIndex + "].value", argValue, HttpParameter.Type.GRAPHQL));

                // Determine if it's an object or array
                if (argValue.startsWith("{")) {
                    params.add(new HttpParameter("graphql.complex_arg[" + argIndex + "].type", "object", HttpParameter.Type.GRAPHQL));
                } else if (argValue.startsWith("[")) {
                    params.add(new HttpParameter("graphql.complex_arg[" + argIndex + "].type", "array", HttpParameter.Type.GRAPHQL));
                }

                argIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing complex GraphQL arguments: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract a specific field from GraphQL JSON body
     */
    private String extractGraphQLField(String body, String fieldName) {
        try {
            String pattern = "\"" + fieldName + "\"\\s*:\\s*";
            int startIndex = body.indexOf(pattern);
            if (startIndex == -1) return "";

            startIndex += pattern.length();

            // Find the value (could be string, object, or null)
            char firstChar = body.charAt(startIndex);
            if (firstChar == '"') {
                // String value
                int endIndex = findStringEnd(body, startIndex);
                return body.substring(startIndex + 1, endIndex);
            } else if (firstChar == '{') {
                // Object value
                int endIndex = findObjectEnd(body, startIndex);
                return body.substring(startIndex, endIndex + 1);
            } else if (firstChar == '[') {
                // Array value
                int endIndex = findArrayEnd(body, startIndex);
                return body.substring(startIndex, endIndex + 1);
            } else {
                // Primitive value (number, boolean, null)
                int endIndex = findPrimitiveEnd(body, startIndex);
                return body.substring(startIndex, endIndex);
            }
        } catch (Exception e) {
            System.out.println("Error extracting GraphQL field " + fieldName + ": " + e.getMessage());
            return "";
        }
    }

    /**
     * Parse GraphQL query string to extract field names and arguments
     */
    private List<HttpParameter> parseGraphQLQueryString(String query, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Extract field names
            Pattern fieldPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?:\\(|\\{|\\s)");
            Matcher fieldMatcher = fieldPattern.matcher(query);
            while (fieldMatcher.find()) {
                String fieldName = fieldMatcher.group(1);
                if (!isGraphQLKeyword(fieldName)) {
                    params.add(new HttpParameter(parentPath + ".field." + fieldName, fieldName, HttpParameter.Type.GRAPHQL));
                }
            }

            // Extract arguments
            Pattern argPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*:\\s*([^,\\)\\}]+)");
            Matcher argMatcher = argPattern.matcher(query);
            while (argMatcher.find()) {
                String argName = argMatcher.group(1);
                String argValue = argMatcher.group(2).trim();

                // Clean argument value
                if (argValue.startsWith("\"") && argValue.endsWith("\"")) {
                    argValue = argValue.substring(1, argValue.length() - 1);
                }

                params.add(new HttpParameter(parentPath + ".arg." + argName, argValue, HttpParameter.Type.GRAPHQL));
            }

        } catch (Exception e) {
            System.out.println("Error parsing GraphQL query string: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if a word is a GraphQL keyword
     */
    private boolean isGraphQLKeyword(String word) {
        String[] keywords = {"query", "mutation", "subscription", "fragment", "on", "type", "interface",
                           "union", "enum", "input", "extend", "scalar", "directive", "schema"};
        for (String keyword : keywords) {
            if (keyword.equals(word)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Find the end of a JSON string value
     */
    private int findStringEnd(String text, int startIndex) {
        boolean escaped = false;
        for (int i = startIndex + 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (escaped) {
                escaped = false;
                continue;
            }
            if (c == '\\') {
                escaped = true;
                continue;
            }
            if (c == '"') {
                return i;
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a JSON object
     */
    private int findObjectEnd(String text, int startIndex) {
        int braceCount = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '{') {
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        return i;
                    }
                }
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a JSON array
     */
    private int findArrayEnd(String text, int startIndex) {
        int bracketCount = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (c == '[') {
                    bracketCount++;
                } else if (c == ']') {
                    bracketCount--;
                    if (bracketCount == 0) {
                        return i;
                    }
                }
            }
        }
        return text.length() - 1;
    }

    /**
     * Find the end of a primitive JSON value (number, boolean, null)
     */
    private int findPrimitiveEnd(String text, int startIndex) {
        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == ',' || c == '}' || c == ']' || Character.isWhitespace(c)) {
                return i;
            }
        }
        return text.length();
    }

    /**
     * Parse the entire request body as a single parameter for injection testing
     * This allows testing of any body content, including raw text, custom formats, etc.
     */
    private List<HttpParameter> parseBodyAsParameter(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> bodyParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        if (bodyOffset < request.length) {
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length)).trim();

            if (!body.isEmpty()) {
                // Only add __BODY__ parameter if there are no other body parameters
                // This prevents conflicts with standard parameter injection
                List<IParameter> bodyParameters = new ArrayList<>();
                for (IParameter param : requestInfo.getParameters()) {
                    if (param.getType() == IParameter.PARAM_BODY) {
                        bodyParameters.add(param);
                    }
                }

                // Only add __BODY__ if no standard body parameters exist and body is not form-encoded
                if (bodyParameters.isEmpty() && !body.contains("=") && !body.contains("&")) {
                    bodyParams.add(new HttpParameter("__BODY__", body, HttpParameter.Type.BODY));
                    System.out.println("Added full body parameter: " + body.substring(0, Math.min(100, body.length())) + "...");
                } else {
                    System.out.println("Skipping __BODY__ parameter - standard body parameters exist or body is form-encoded");
                }
            }
        }

        return bodyParams;
    }

    /**
     * Parse URL path segments for injection testing
     */
    private List<HttpParameter> parseUrlPathParameters(IRequestInfo requestInfo) {
        List<HttpParameter> pathParams = new ArrayList<>();

        try {
            // Get the URL from the request
            String url = requestInfo.getUrl().toString();
            System.out.println("Parsing URL path: " + url);

            // Parse the URL to extract path segments
            java.net.URL urlObj = new java.net.URL(url);
            String path = urlObj.getPath();

            if (path != null && !path.isEmpty() && !path.equals("/")) {
                // Enhanced path parsing with multiple strategies
                pathParams.addAll(parseUrlPathSegments(path));
                pathParams.addAll(parseUrlPathPatterns(path));
                pathParams.addAll(parseUrlEncodedPathSegments(path));
                pathParams.addAll(parseUrlPathExtensions(path));
            }

            // Parse query string parameters with enhanced detection
            String query = urlObj.getQuery();
            if (query != null && !query.isEmpty()) {
                pathParams.addAll(parseEnhancedQueryParameters(query));
            }

            // Parse URL fragment if present
            String fragment = urlObj.getRef();
            if (fragment != null && !fragment.isEmpty()) {
                pathParams.addAll(parseUrlFragment(fragment));
            }

            // Parse potential REST API patterns
            pathParams.addAll(parseRestApiPatterns(url));

            // Parse URL with different encoding schemes
            pathParams.addAll(parseUrlWithDifferentEncodings(url));

            System.out.println("Total URL path parameters found: " + pathParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing URL path: " + e.getMessage());
        }

        return pathParams;
    }

    /**
     * Parse URL path segments with enhanced detection
     */
    private List<HttpParameter> parseUrlPathSegments(String path) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Split path into segments, removing empty segments
            String[] segments = path.split("/");

            for (int i = 0; i < segments.length; i++) {
                String segment = segments[i];

                // Skip empty segments
                if (segment.isEmpty()) {
                    continue;
                }

                // Create parameter for this path segment
                String paramName = "URL_PATH[" + i + "]";
                params.add(new HttpParameter(paramName, segment, HttpParameter.Type.URL_PATH));

                // Also create a parameter for the full path replacement at this position
                String fullPathName = "URL_PATH_FULL[" + i + "]";
                params.add(new HttpParameter(fullPathName, path, HttpParameter.Type.URL_PATH));

                // Check if segment looks like an ID (numeric)
                if (segment.matches("\\d+")) {
                    params.add(new HttpParameter("URL_PATH_ID[" + i + "]", segment, HttpParameter.Type.URL_PATH));
                }

                // Check if segment looks like a UUID
                if (segment.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")) {
                    params.add(new HttpParameter("URL_PATH_UUID[" + i + "]", segment, HttpParameter.Type.URL_PATH));
                }

                System.out.println("Added URL path parameter: " + paramName + " = " + segment);
            }

            // Add a parameter for the entire path
            params.add(new HttpParameter("URL_PATH_COMPLETE", path, HttpParameter.Type.URL_PATH));
            System.out.println("Added complete URL path parameter: " + path);

        } catch (Exception e) {
            System.out.println("Error parsing URL path segments: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse URL path patterns (REST API patterns)
     */
    private List<HttpParameter> parseUrlPathPatterns(String path) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Common REST patterns
            String[] patterns = {
                "/api/v(\\d+)/",           // API version
                "/users/(\\d+)/",          // User ID
                "/posts/(\\d+)/",          // Post ID
                "/items/([^/]+)/",         // Item identifier
                "/(\\w+)/(\\d+)/edit",     // Edit pattern
                "/(\\w+)/(\\d+)/delete",   // Delete pattern
                "/([^/]+)\\.([^/]+)$"      // File with extension
            };

            for (int i = 0; i < patterns.length; i++) {
                Pattern pattern = Pattern.compile(patterns[i]);
                Matcher matcher = pattern.matcher(path);

                if (matcher.find()) {
                    for (int j = 1; j <= matcher.groupCount(); j++) {
                        String value = matcher.group(j);
                        params.add(new HttpParameter("URL_PATTERN[" + i + "][" + j + "]", value, HttpParameter.Type.URL_PATH));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing URL path patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse URL encoded path segments
     */
    private List<HttpParameter> parseUrlEncodedPathSegments(String path) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for URL encoded characters in path
            if (path.contains("%")) {
                String decodedPath = java.net.URLDecoder.decode(path, "UTF-8");

                if (!decodedPath.equals(path)) {
                    params.add(new HttpParameter("URL_PATH_DECODED", decodedPath, HttpParameter.Type.URL_PATH));

                    // Parse the decoded path segments
                    String[] segments = decodedPath.split("/");
                    for (int i = 0; i < segments.length; i++) {
                        String segment = segments[i];
                        if (!segment.isEmpty()) {
                            params.add(new HttpParameter("URL_PATH_DECODED[" + i + "]", segment, HttpParameter.Type.URL_PATH));
                        }
                    }
                }
            }

            // Double URL decode (sometimes data is double encoded)
            if (path.contains("%25")) {
                String doubleDecoded = java.net.URLDecoder.decode(
                    java.net.URLDecoder.decode(path, "UTF-8"), "UTF-8");

                if (!doubleDecoded.equals(path)) {
                    params.add(new HttpParameter("URL_PATH_DOUBLE_DECODED", doubleDecoded, HttpParameter.Type.URL_PATH));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing URL encoded path segments: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse URL path extensions
     */
    private List<HttpParameter> parseUrlPathExtensions(String path) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Extract file extensions from path
            Pattern extensionPattern = Pattern.compile("\\.([a-zA-Z0-9]+)(?:/|$)");
            Matcher matcher = extensionPattern.matcher(path);
            int extIndex = 0;

            while (matcher.find()) {
                String extension = matcher.group(1);
                params.add(new HttpParameter("URL_EXTENSION[" + extIndex + "]", extension, HttpParameter.Type.URL_PATH));
                extIndex++;
            }

            // Extract filename if present
            Pattern filenamePattern = Pattern.compile("/([^/]+\\.[a-zA-Z0-9]+)(?:/|$)");
            matcher = filenamePattern.matcher(path);
            int fileIndex = 0;

            while (matcher.find()) {
                String filename = matcher.group(1);
                params.add(new HttpParameter("URL_FILENAME[" + fileIndex + "]", filename, HttpParameter.Type.URL_PATH));
                fileIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing URL path extensions: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse enhanced query parameters
     */
    private List<HttpParameter> parseEnhancedQueryParameters(String query) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Standard query parameter parsing
            String[] pairs = query.split("&");

            for (String pair : pairs) {
                if (pair.contains("=")) {
                    String[] kv = pair.split("=", 2);
                    String key = java.net.URLDecoder.decode(kv[0], "UTF-8");
                    String value = kv.length > 1 ? java.net.URLDecoder.decode(kv[1], "UTF-8") : "";

                    params.add(new HttpParameter(key, value, HttpParameter.Type.URL));

                    // Check for nested parameters (param[key]=value)
                    if (key.contains("[") && key.contains("]")) {
                        Pattern nestedPattern = Pattern.compile("([^\\[]+)\\[([^\\]]+)\\]");
                        Matcher matcher = nestedPattern.matcher(key);
                        if (matcher.find()) {
                            String baseKey = matcher.group(1);
                            String nestedKey = matcher.group(2);
                            params.add(new HttpParameter(baseKey + "__NESTED__" + nestedKey, value, HttpParameter.Type.URL));
                        }
                    }

                    // Check for JSON-like values
                    if (value.startsWith("{") && value.endsWith("}")) {
                        params.add(new HttpParameter(key + "__JSON__", value, HttpParameter.Type.URL));
                    }

                    // Check for array-like values
                    if (value.startsWith("[") && value.endsWith("]")) {
                        params.add(new HttpParameter(key + "__ARRAY__", value, HttpParameter.Type.URL));
                    }

                } else {
                    // Parameter without value
                    String key = java.net.URLDecoder.decode(pair, "UTF-8");
                    params.add(new HttpParameter(key, "", HttpParameter.Type.URL));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing enhanced query parameters: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse URL fragment
     */
    private List<HttpParameter> parseUrlFragment(String fragment) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            params.add(new HttpParameter("URL_FRAGMENT", fragment, HttpParameter.Type.URL));

            // Check if fragment contains parameters (like #param=value&param2=value2)
            if (fragment.contains("=")) {
                String[] pairs = fragment.split("&");
                for (String pair : pairs) {
                    if (pair.contains("=")) {
                        String[] kv = pair.split("=", 2);
                        String key = java.net.URLDecoder.decode(kv[0], "UTF-8");
                        String value = kv.length > 1 ? java.net.URLDecoder.decode(kv[1], "UTF-8") : "";
                        params.add(new HttpParameter("FRAGMENT_" + key, value, HttpParameter.Type.URL));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing URL fragment: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse REST API patterns
     */
    private List<HttpParameter> parseRestApiPatterns(String url) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Common REST API patterns
            Map<String, String> restPatterns = new HashMap<>();
            restPatterns.put("API_VERSION", "/api/v(\\d+)");
            restPatterns.put("RESOURCE_ID", "/(\\w+)/(\\d+)(?:/|$)");
            restPatterns.put("NESTED_RESOURCE", "/(\\w+)/(\\d+)/(\\w+)/(\\d+)");
            restPatterns.put("ACTION", "/(\\w+)/(\\d+)/(edit|delete|update|create)");
            restPatterns.put("PAGINATION", "[?&](page|offset|limit)=(\\d+)");
            restPatterns.put("SORTING", "[?&](sort|order)=([^&]+)");
            restPatterns.put("FILTERING", "[?&](filter|where)=([^&]+)");

            for (Map.Entry<String, String> entry : restPatterns.entrySet()) {
                String patternName = entry.getKey();
                String patternRegex = entry.getValue();

                Pattern pattern = Pattern.compile(patternRegex);
                Matcher matcher = pattern.matcher(url);

                int matchIndex = 0;
                while (matcher.find()) {
                    for (int i = 1; i <= matcher.groupCount(); i++) {
                        String value = matcher.group(i);
                        params.add(new HttpParameter("REST_" + patternName + "[" + matchIndex + "][" + i + "]",
                                                   value, HttpParameter.Type.URL));
                    }
                    matchIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing REST API patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse URL with different encoding schemes
     */
    private List<HttpParameter> parseUrlWithDifferentEncodings(String url) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Try different encoding schemes
            String[] encodings = {"UTF-8", "ISO-8859-1", "UTF-16", "ASCII"};

            for (String encoding : encodings) {
                try {
                    String decoded = java.net.URLDecoder.decode(url, encoding);
                    if (!decoded.equals(url)) {
                        params.add(new HttpParameter("URL_DECODED_" + encoding.replace("-", "_"),
                                                   decoded, HttpParameter.Type.URL));
                    }
                } catch (Exception e) {
                    // Encoding not supported or failed, continue
                }
            }

            // Try HTML entity decoding
            String htmlDecoded = url.replace("&amp;", "&")
                                   .replace("&lt;", "<")
                                   .replace("&gt;", ">")
                                   .replace("&quot;", "\"")
                                   .replace("&#39;", "'");

            if (!htmlDecoded.equals(url)) {
                params.add(new HttpParameter("URL_HTML_DECODED", htmlDecoded, HttpParameter.Type.URL));
            }

            // Try Base64 decoding on URL parts
            String[] urlParts = url.split("[/?&=]");
            for (int i = 0; i < urlParts.length; i++) {
                String part = urlParts[i];
                if (part.length() > 4 && part.matches("[A-Za-z0-9+/=]+")) {
                    try {
                        byte[] decoded = java.util.Base64.getDecoder().decode(part);
                        String decodedStr = new String(decoded, "UTF-8");
                        if (decodedStr.length() > 0 && !decodedStr.equals(part)) {
                            params.add(new HttpParameter("URL_BASE64_DECODED[" + i + "]",
                                                       decodedStr, HttpParameter.Type.URL));
                        }
                    } catch (Exception e) {
                        // Not valid Base64, continue
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing URL with different encodings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse XML parameters from request with enhanced support for all XML features
     */
    private List<HttpParameter> parseXmlParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> xmlParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        System.out.println("Parsing XML body: " + body.substring(0, Math.min(200, body.length())) + "...");

        try {
            // Parse XML elements with content
            xmlParams.addAll(parseXmlElements(body, ""));

            // Parse XML attributes
            xmlParams.addAll(parseXmlAttributes(body));

            // Parse CDATA sections
            xmlParams.addAll(parseXmlCDATA(body));

            // Parse XML text content
            xmlParams.addAll(parseXmlTextContent(body));

            // Parse XML namespaces
            xmlParams.addAll(parseXmlNamespaces(body));

            // Parse XML processing instructions
            xmlParams.addAll(parseXmlProcessingInstructions(body));

            // Parse XML comments (for potential data hiding)
            xmlParams.addAll(parseXmlComments(body));

            // Parse mixed content elements
            xmlParams.addAll(parseXmlMixedContent(body));

            // Parse XML entities
            xmlParams.addAll(parseXmlEntities(body));

            System.out.println("Total XML parameters found: " + xmlParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing XML: " + e.getMessage());
            // Enhanced fallback parsing with multiple strategies
            xmlParams.addAll(parseXmlWithRegex(body));
            xmlParams.addAll(parseXmlWithFallbackStrategies(body));
        }

        return xmlParams;
    }

    /**
     * Parse XML elements recursively
     */
    private List<HttpParameter> parseXmlElements(String xml, String parentPath) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for XML elements: <tagname>content</tagname>
        Pattern elementPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)(?:\\s[^>]*)?>([^<]*)</\\1>");
        Matcher matcher = elementPattern.matcher(xml);

        while (matcher.find()) {
            String tagName = matcher.group(1);
            String content = matcher.group(2).trim();

            String fullPath = parentPath.isEmpty() ? tagName : parentPath + "." + tagName;

            // Add element value
            params.add(new HttpParameter(fullPath, content, HttpParameter.Type.XML));

            // Add element name for injection
            params.add(new HttpParameter(fullPath + "__NAME__", tagName, HttpParameter.Type.XML));

            // If content contains nested XML, parse recursively
            if (content.contains("<") && content.contains(">")) {
                params.addAll(parseXmlElements(content, fullPath));
            }
        }

        // Pattern for self-closing elements: <tagname attr="value"/>
        Pattern selfClosingPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)(?:\\s[^>]*)?/>");
        matcher = selfClosingPattern.matcher(xml);

        while (matcher.find()) {
            String tagName = matcher.group(1);
            String fullPath = parentPath.isEmpty() ? tagName : parentPath + "." + tagName;

            // Add self-closing element
            params.add(new HttpParameter(fullPath, "", HttpParameter.Type.XML));
            params.add(new HttpParameter(fullPath + "__NAME__", tagName, HttpParameter.Type.XML));
        }

        return params;
    }

    /**
     * Parse XML attributes
     */
    private List<HttpParameter> parseXmlAttributes(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for XML attributes: attribute="value" or attribute='value'
        Pattern attrPattern = Pattern.compile("<([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*)\\s+[^>]*?([a-zA-Z_][a-zA-Z0-9_\\-\\.:]*?)\\s*=\\s*[\"']([^\"']*)[\"']");
        Matcher matcher = attrPattern.matcher(xml);

        while (matcher.find()) {
            String elementName = matcher.group(1);
            String attrName = matcher.group(2);
            String attrValue = matcher.group(3);

            // Add attribute value
            params.add(new HttpParameter(elementName + "@" + attrName, attrValue, HttpParameter.Type.XML));

            // Add attribute name for injection
            params.add(new HttpParameter(elementName + "@" + attrName + "__NAME__", attrName, HttpParameter.Type.XML));
        }

        return params;
    }

    /**
     * Parse XML CDATA sections
     */
    private List<HttpParameter> parseXmlCDATA(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for CDATA: <![CDATA[content]]>
        Pattern cdataPattern = Pattern.compile("<!\\[CDATA\\[([^\\]]*?)\\]\\]>");
        Matcher matcher = cdataPattern.matcher(xml);

        int cdataIndex = 0;
        while (matcher.find()) {
            String content = matcher.group(1);
            params.add(new HttpParameter("CDATA[" + cdataIndex + "]", content, HttpParameter.Type.XML));
            cdataIndex++;
        }

        return params;
    }

    /**
     * Parse XML text content between tags
     */
    private List<HttpParameter> parseXmlTextContent(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for text content between tags (excluding CDATA and comments)
        Pattern textPattern = Pattern.compile(">([^<]+)<");
        Matcher matcher = textPattern.matcher(xml);

        int textIndex = 0;
        while (matcher.find()) {
            String content = matcher.group(1).trim();
            if (!content.isEmpty() && !content.startsWith("![CDATA[") && !content.startsWith("!--")) {
                params.add(new HttpParameter("XML_TEXT[" + textIndex + "]", content, HttpParameter.Type.XML));
                textIndex++;
            }
        }

        return params;
    }

    /**
     * Fallback regex-based XML parsing
     */
    private List<HttpParameter> parseXmlWithRegex(String body) {
        List<HttpParameter> xmlParams = new ArrayList<>();

        // Use the original regex patterns as fallback
        Matcher matcher = XML_TAG_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            String value = matcher.group(2).trim();
            xmlParams.add(new HttpParameter(name, value, HttpParameter.Type.XML));
        }

        matcher = XML_SELF_CLOSING_PATTERN.matcher(body);
        while (matcher.find()) {
            String name = matcher.group(1);
            xmlParams.add(new HttpParameter(name, "", HttpParameter.Type.XML));
        }

        return xmlParams;
    }

    /**
     * Parse XML namespaces
     */
    private List<HttpParameter> parseXmlNamespaces(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse namespace declarations (xmlns:prefix="uri" or xmlns="uri")
            Pattern namespacePattern = Pattern.compile("xmlns(?::(\\w+))?\\s*=\\s*[\"']([^\"']+)[\"']");
            Matcher matcher = namespacePattern.matcher(xml);

            while (matcher.find()) {
                String prefix = matcher.group(1);
                String uri = matcher.group(2);

                if (prefix != null) {
                    params.add(new HttpParameter("xmlns." + prefix, uri, HttpParameter.Type.XML));
                    params.add(new HttpParameter("xmlns." + prefix + "__NAME__", prefix, HttpParameter.Type.XML));
                } else {
                    params.add(new HttpParameter("xmlns.default", uri, HttpParameter.Type.XML));
                }
            }

            // Parse elements with namespace prefixes
            Pattern namespacedElementPattern = Pattern.compile("<(\\w+):(\\w+)[^>]*>([^<]*)</\\1:\\2>");
            matcher = namespacedElementPattern.matcher(xml);

            while (matcher.find()) {
                String prefix = matcher.group(1);
                String localName = matcher.group(2);
                String content = matcher.group(3);

                params.add(new HttpParameter(prefix + ":" + localName, content, HttpParameter.Type.XML));
                params.add(new HttpParameter(prefix + ":" + localName + "__NAME__", localName, HttpParameter.Type.XML));
            }

        } catch (Exception e) {
            System.out.println("Error parsing XML namespaces: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse XML processing instructions
     */
    private List<HttpParameter> parseXmlProcessingInstructions(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse processing instructions like <?xml version="1.0"?> or <?php echo $data; ?>
            Pattern piPattern = Pattern.compile("<\\?(\\w+)([^?]*)\\?>");
            Matcher matcher = piPattern.matcher(xml);
            int piIndex = 0;

            while (matcher.find()) {
                String target = matcher.group(1);
                String data = matcher.group(2).trim();

                params.add(new HttpParameter("pi[" + piIndex + "].target", target, HttpParameter.Type.XML));

                if (!data.isEmpty()) {
                    params.add(new HttpParameter("pi[" + piIndex + "].data", data, HttpParameter.Type.XML));

                    // Parse attributes within processing instruction data
                    Pattern attrPattern = Pattern.compile("(\\w+)\\s*=\\s*[\"']([^\"']*)[\"']");
                    Matcher attrMatcher = attrPattern.matcher(data);

                    while (attrMatcher.find()) {
                        String attrName = attrMatcher.group(1);
                        String attrValue = attrMatcher.group(2);
                        params.add(new HttpParameter("pi[" + piIndex + "]." + attrName, attrValue, HttpParameter.Type.XML));
                    }
                }

                piIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing XML processing instructions: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse XML comments
     */
    private List<HttpParameter> parseXmlComments(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse XML comments <!-- comment -->
            Pattern commentPattern = Pattern.compile("<!--([^-]*)-->");
            Matcher matcher = commentPattern.matcher(xml);
            int commentIndex = 0;

            while (matcher.find()) {
                String comment = matcher.group(1).trim();

                if (!comment.isEmpty()) {
                    params.add(new HttpParameter("comment[" + commentIndex + "]", comment, HttpParameter.Type.XML));

                    // Check if comment contains structured data (JSON, key=value pairs, etc.)
                    if (comment.contains("=") || comment.contains(":")) {
                        // Try to parse as key-value pairs
                        String[] pairs = comment.split("[,;\\n]");
                        for (String pair : pairs) {
                            if (pair.contains("=")) {
                                String[] kv = pair.split("=", 2);
                                if (kv.length == 2) {
                                    params.add(new HttpParameter("comment[" + commentIndex + "]." + kv[0].trim(),
                                                               kv[1].trim(), HttpParameter.Type.XML));
                                }
                            }
                        }
                    }
                }

                commentIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing XML comments: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse XML mixed content (elements with both text and child elements)
     */
    private List<HttpParameter> parseXmlMixedContent(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Find elements that contain both text and child elements
            Pattern mixedContentPattern = Pattern.compile("<(\\w+)[^>]*>([^<]*)<[^>]+>[^<]*</\\1>");
            Matcher matcher = mixedContentPattern.matcher(xml);
            int mixedIndex = 0;

            while (matcher.find()) {
                String elementName = matcher.group(1);
                String textContent = matcher.group(2).trim();

                if (!textContent.isEmpty()) {
                    params.add(new HttpParameter("mixed[" + mixedIndex + "]." + elementName + ".text",
                                               textContent, HttpParameter.Type.XML));
                }

                mixedIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing XML mixed content: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse XML entities
     */
    private List<HttpParameter> parseXmlEntities(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Parse character entities like &#123; or &#x1A;
            Pattern charEntityPattern = Pattern.compile("&#(x?)([0-9a-fA-F]+);");
            Matcher matcher = charEntityPattern.matcher(xml);
            int entityIndex = 0;

            while (matcher.find()) {
                String hex = matcher.group(1);
                String value = matcher.group(2);

                params.add(new HttpParameter("entity[" + entityIndex + "].raw",
                                           "&#" + hex + value + ";", HttpParameter.Type.XML));

                try {
                    int charCode = hex.isEmpty() ? Integer.parseInt(value) : Integer.parseInt(value, 16);
                    char decodedChar = (char) charCode;
                    params.add(new HttpParameter("entity[" + entityIndex + "].decoded",
                                               String.valueOf(decodedChar), HttpParameter.Type.XML));
                } catch (NumberFormatException e) {
                    // Invalid entity, keep raw value
                }

                entityIndex++;
            }

            // Parse named entities like &amp; &lt; &gt; &quot; &apos;
            Pattern namedEntityPattern = Pattern.compile("&(\\w+);");
            matcher = namedEntityPattern.matcher(xml);

            while (matcher.find()) {
                String entityName = matcher.group(1);
                params.add(new HttpParameter("named_entity[" + entityIndex + "]", entityName, HttpParameter.Type.XML));
                entityIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error parsing XML entities: " + e.getMessage());
        }

        return params;
    }

    /**
     * Enhanced fallback XML parsing with multiple strategies
     */
    private List<HttpParameter> parseXmlWithFallbackStrategies(String xml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Strategy 1: Extract all text content between tags
            Pattern textContentPattern = Pattern.compile(">([^<]+)<");
            Matcher matcher = textContentPattern.matcher(xml);
            int textIndex = 0;

            while (matcher.find()) {
                String text = matcher.group(1).trim();
                if (text.length() > 2) { // Skip very short text
                    params.add(new HttpParameter("fallback.text[" + textIndex + "]", text, HttpParameter.Type.XML));
                    textIndex++;
                }
            }

            // Strategy 2: Extract all attribute values
            Pattern attrValuePattern = Pattern.compile("\\w+\\s*=\\s*[\"']([^\"']+)[\"']");
            matcher = attrValuePattern.matcher(xml);
            int attrIndex = 0;

            while (matcher.find()) {
                String value = matcher.group(1);
                if (value.length() > 1) {
                    params.add(new HttpParameter("fallback.attr[" + attrIndex + "]", value, HttpParameter.Type.XML));
                    attrIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error in fallback XML parsing: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse multipart form data with enhanced support for all multipart features
     */
    private List<HttpParameter> parseMultipartParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> multipartParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Get Content-Type header to extract boundary
        String boundary = extractMultipartBoundary(requestInfo.getHeaders());
        if (boundary == null || boundary.isEmpty()) {
            System.out.println("No multipart boundary found");
            return multipartParams;
        }

        System.out.println("Parsing multipart data with boundary: " + boundary);

        try {
            // Enhanced multipart parsing with better boundary handling
            multipartParams.addAll(parseMultipartWithEnhancedBoundaryDetection(body, boundary));

            // Parse nested multipart (multipart within multipart)
            multipartParams.addAll(parseNestedMultipart(body, boundary));

            // Parse multipart with mixed encodings
            multipartParams.addAll(parseMultipartMixedEncodings(body, boundary));

            // Parse multipart file metadata
            multipartParams.addAll(parseMultipartFileMetadata(body, boundary));

            System.out.println("Total multipart parameters found: " + multipartParams.size());
        } catch (Exception e) {
            System.out.println("Error parsing multipart data: " + e.getMessage());
            // Fallback to basic parsing
            multipartParams.addAll(parseMultipartFallback(body, boundary));
        }

        return multipartParams;
    }

    /**
     * Extract boundary from Content-Type header
     */
    private String extractMultipartBoundary(List<String> headers) {
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:") &&
                header.toLowerCase().contains("multipart/")) {

                Pattern boundaryPattern = Pattern.compile("boundary=([^;\\s]+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = boundaryPattern.matcher(header);
                if (matcher.find()) {
                    String boundary = matcher.group(1);
                    // Remove quotes if present
                    if (boundary.startsWith("\"") && boundary.endsWith("\"")) {
                        boundary = boundary.substring(1, boundary.length() - 1);
                    }
                    return boundary;
                }
            }
        }
        return null;
    }

    /**
     * Parse individual multipart part
     */
    private List<HttpParameter> parseMultipartPart(String part, int partIndex) {
        List<HttpParameter> params = new ArrayList<>();

        // Split headers and body
        String[] sections = part.split("\r\n\r\n", 2);
        if (sections.length < 2) {
            sections = part.split("\n\n", 2);
        }

        if (sections.length >= 2) {
            String headers = sections[0];
            String content = sections[1];

            // Parse Content-Disposition header
            String name = extractMultipartName(headers);
            String filename = extractMultipartFilename(headers);
            String contentType = extractMultipartContentType(headers);

            // Add field name and value
            if (name != null && !name.isEmpty()) {
                params.add(new HttpParameter(name, content, HttpParameter.Type.MULTIPART));
                params.add(new HttpParameter(name + "__NAME__", name, HttpParameter.Type.MULTIPART));

                // Add filename if present (file upload)
                if (filename != null && !filename.isEmpty()) {
                    params.add(new HttpParameter(name + "__FILENAME__", filename, HttpParameter.Type.MULTIPART));

                    // Add content type if present
                    if (contentType != null && !contentType.isEmpty()) {
                        params.add(new HttpParameter(name + "__CONTENT_TYPE__", contentType, HttpParameter.Type.MULTIPART));
                    }

                    // For binary files, add metadata parameters
                    params.add(new HttpParameter(name + "__FILE_SIZE__", String.valueOf(content.length()), HttpParameter.Type.MULTIPART));
                    params.add(new HttpParameter(name + "__IS_FILE__", "true", HttpParameter.Type.MULTIPART));
                } else {
                    // Text field
                    params.add(new HttpParameter(name + "__IS_TEXT__", "true", HttpParameter.Type.MULTIPART));
                }
            } else {
                // Unnamed part
                params.add(new HttpParameter("MULTIPART_PART[" + partIndex + "]", content, HttpParameter.Type.MULTIPART));
            }

            // Parse all headers in the multipart part
            params.addAll(parseMultipartHeaders(headers, name != null ? name : "PART[" + partIndex + "]"));
        }

        return params;
    }

    /**
     * Extract name from Content-Disposition header
     */
    private String extractMultipartName(String headers) {
        Pattern namePattern = Pattern.compile("name\\s*=\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
        Matcher matcher = namePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        // Try without quotes
        namePattern = Pattern.compile("name\\s*=\\s*([^;\\s]+)", Pattern.CASE_INSENSITIVE);
        matcher = namePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * Extract filename from Content-Disposition header
     */
    private String extractMultipartFilename(String headers) {
        Pattern filenamePattern = Pattern.compile("filename\\s*=\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
        Matcher matcher = filenamePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        // Try without quotes
        filenamePattern = Pattern.compile("filename\\s*=\\s*([^;\\s]+)", Pattern.CASE_INSENSITIVE);
        matcher = filenamePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * Extract Content-Type from multipart headers
     */
    private String extractMultipartContentType(String headers) {
        Pattern contentTypePattern = Pattern.compile("Content-Type:\\s*([^\\r\\n]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = contentTypePattern.matcher(headers);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }

    /**
     * Parse headers within multipart part
     */
    private List<HttpParameter> parseMultipartHeaders(String headers, String partName) {
        List<HttpParameter> params = new ArrayList<>();

        String[] headerLines = headers.split("\r\n");
        if (headerLines.length == 1) {
            headerLines = headers.split("\n");
        }

        for (String headerLine : headerLines) {
            if (headerLine.contains(":")) {
                String[] headerParts = headerLine.split(":", 2);
                if (headerParts.length == 2) {
                    String headerName = headerParts[0].trim();
                    String headerValue = headerParts[1].trim();

                    params.add(new HttpParameter(partName + "__HEADER__" + headerName, headerValue, HttpParameter.Type.MULTIPART));
                }
            }
        }

        return params;
    }

    /**
     * Enhanced multipart parsing with better boundary detection
     */
    private List<HttpParameter> parseMultipartWithEnhancedBoundaryDetection(String body, String boundary) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Handle different boundary formats and edge cases
            String[] possibleBoundaries = {
                "--" + boundary,
                "--" + boundary.replace("\"", ""),
                "--" + boundary.replace("'", ""),
                boundary
            };

            for (String testBoundary : possibleBoundaries) {
                if (body.contains(testBoundary)) {
                    String[] parts = body.split(Pattern.quote(testBoundary));

                    for (int i = 1; i < parts.length; i++) {
                        String part = parts[i].trim();
                        if (!part.isEmpty() && !part.equals("--")) {
                            params.addAll(parseMultipartPart(part, i));
                        }
                    }
                    break; // Use the first working boundary
                }
            }

        } catch (Exception e) {
            System.out.println("Error in enhanced boundary detection: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse nested multipart data
     */
    private List<HttpParameter> parseNestedMultipart(String body, String outerBoundary) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for nested Content-Type: multipart headers within parts
            Pattern nestedMultipartPattern = Pattern.compile(
                "Content-Type:\\s*multipart/[^\\r\\n]*boundary=([^\\r\\n;]+)",
                Pattern.CASE_INSENSITIVE
            );
            Matcher matcher = nestedMultipartPattern.matcher(body);

            while (matcher.find()) {
                String nestedBoundary = matcher.group(1).trim().replace("\"", "");

                // Find the content after this nested multipart declaration
                int startPos = matcher.end();
                String remainingContent = body.substring(startPos);

                // Parse the nested multipart
                if (remainingContent.contains("--" + nestedBoundary)) {
                    String[] nestedParts = remainingContent.split("--" + Pattern.quote(nestedBoundary));

                    for (int i = 1; i < nestedParts.length; i++) {
                        String nestedPart = nestedParts[i].trim();
                        if (!nestedPart.isEmpty() && !nestedPart.equals("--")) {
                            params.addAll(parseMultipartPart(nestedPart, i, "nested"));
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing nested multipart: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse multipart with mixed encodings
     */
    private List<HttpParameter> parseMultipartMixedEncodings(String body, String boundary) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] parts = body.split("--" + Pattern.quote(boundary));

            for (int i = 1; i < parts.length; i++) {
                String part = parts[i].trim();
                if (part.isEmpty() || part.equals("--")) continue;

                // Check for different encodings in Content-Transfer-Encoding header
                if (part.toLowerCase().contains("content-transfer-encoding:")) {
                    Pattern encodingPattern = Pattern.compile(
                        "Content-Transfer-Encoding:\\s*([^\\r\\n]+)",
                        Pattern.CASE_INSENSITIVE
                    );
                    Matcher matcher = encodingPattern.matcher(part);

                    if (matcher.find()) {
                        String encoding = matcher.group(1).trim();

                        // Extract the encoded content
                        String[] partLines = part.split("\\r?\\n");
                        StringBuilder content = new StringBuilder();
                        boolean inContent = false;

                        for (String line : partLines) {
                            if (inContent) {
                                content.append(line).append("\n");
                            } else if (line.trim().isEmpty()) {
                                inContent = true;
                            }
                        }

                        String encodedContent = content.toString().trim();
                        if (!encodedContent.isEmpty()) {
                            params.add(new HttpParameter("encoded[" + i + "].encoding", encoding, HttpParameter.Type.MULTIPART));
                            params.add(new HttpParameter("encoded[" + i + "].content", encodedContent, HttpParameter.Type.MULTIPART));

                            // Try to decode based on encoding type
                            String decodedContent = decodeMultipartContent(encodedContent, encoding);
                            if (!decodedContent.equals(encodedContent)) {
                                params.add(new HttpParameter("encoded[" + i + "].decoded", decodedContent, HttpParameter.Type.MULTIPART));
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing multipart mixed encodings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse multipart file metadata
     */
    private List<HttpParameter> parseMultipartFileMetadata(String body, String boundary) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] parts = body.split("--" + Pattern.quote(boundary));

            for (int i = 1; i < parts.length; i++) {
                String part = parts[i].trim();
                if (part.isEmpty() || part.equals("--")) continue;

                // Look for file uploads (parts with filename)
                if (part.toLowerCase().contains("filename=")) {
                    Pattern filenamePattern = Pattern.compile("filename=\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = filenamePattern.matcher(part);

                    if (matcher.find()) {
                        String filename = matcher.group(1);
                        params.add(new HttpParameter("file[" + i + "].filename", filename, HttpParameter.Type.FILE_UPLOAD));

                        // Extract file extension
                        if (filename.contains(".")) {
                            String extension = filename.substring(filename.lastIndexOf(".") + 1);
                            params.add(new HttpParameter("file[" + i + "].extension", extension, HttpParameter.Type.FILE_UPLOAD));
                        }

                        // Extract Content-Type if present
                        Pattern contentTypePattern = Pattern.compile("Content-Type:\\s*([^\\r\\n]+)", Pattern.CASE_INSENSITIVE);
                        matcher = contentTypePattern.matcher(part);
                        if (matcher.find()) {
                            String contentType = matcher.group(1).trim();
                            params.add(new HttpParameter("file[" + i + "].content_type", contentType, HttpParameter.Type.FILE_UPLOAD));
                        }

                        // Extract file size (approximate)
                        String[] partLines = part.split("\\r?\\n");
                        StringBuilder fileContent = new StringBuilder();
                        boolean inContent = false;

                        for (String line : partLines) {
                            if (inContent) {
                                fileContent.append(line).append("\n");
                            } else if (line.trim().isEmpty()) {
                                inContent = true;
                            }
                        }

                        if (fileContent.length() > 0) {
                            params.add(new HttpParameter("file[" + i + "].size",
                                                       String.valueOf(fileContent.length()), HttpParameter.Type.FILE_UPLOAD));
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing multipart file metadata: " + e.getMessage());
        }

        return params;
    }

    /**
     * Fallback multipart parsing
     */
    private List<HttpParameter> parseMultipartFallback(String body, String boundary) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Simple split and parse
            String[] parts = body.split("--" + Pattern.quote(boundary));

            for (int i = 1; i < parts.length - 1; i++) {
                String part = parts[i].trim();
                if (!part.isEmpty()) {
                    params.addAll(parseMultipartPart(part, i));
                }
            }

        } catch (Exception e) {
            System.out.println("Error in multipart fallback parsing: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse individual multipart part with optional prefix
     */
    private List<HttpParameter> parseMultipartPart(String part, int partIndex, String prefix) {
        List<HttpParameter> params = parseMultipartPart(part, partIndex);

        // Add prefix to all parameter names
        if (prefix != null && !prefix.isEmpty()) {
            List<HttpParameter> prefixedParams = new ArrayList<>();
            for (HttpParameter param : params) {
                prefixedParams.add(new HttpParameter(prefix + "." + param.getName(),
                                                   param.getValue(), param.getType()));
            }
            return prefixedParams;
        }

        return params;
    }

    /**
     * Decode multipart content based on encoding type
     */
    private String decodeMultipartContent(String content, String encoding) {
        try {
            switch (encoding.toLowerCase()) {
                case "base64":
                    return new String(java.util.Base64.getDecoder().decode(content));
                case "quoted-printable":
                    return decodeQuotedPrintable(content);
                case "7bit":
                case "8bit":
                case "binary":
                default:
                    return content; // No decoding needed
            }
        } catch (Exception e) {
            System.out.println("Error decoding multipart content: " + e.getMessage());
            return content;
        }
    }

    /**
     * Decode quoted-printable encoding
     */
    private String decodeQuotedPrintable(String input) {
        try {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.length(); i++) {
                char c = input.charAt(i);
                if (c == '=' && i + 2 < input.length()) {
                    String hex = input.substring(i + 1, i + 3);
                    try {
                        int value = Integer.parseInt(hex, 16);
                        result.append((char) value);
                        i += 2;
                    } catch (NumberFormatException e) {
                        result.append(c);
                    }
                } else {
                    result.append(c);
                }
            }
            return result.toString();
        } catch (Exception e) {
            return input;
        }
    }

    /**
     * Parse headers for potential injection points
     */
    private List<HttpParameter> parseHeaderParameters(IRequestInfo requestInfo) {
        List<HttpParameter> headerParams = new ArrayList<>();
        List<String> headers = requestInfo.getHeaders();

        // Skip first header (request line)
        for (int i = 1; i < headers.size(); i++) {
            String header = headers.get(i);
            int colonIndex = header.indexOf(':');

            if (colonIndex > 0) {
                String name = header.substring(0, colonIndex).trim();
                String value = header.substring(colonIndex + 1).trim();

                // Only add headers that might be vulnerable to injection
                if (isInterestingHeader(name)) {
                    // Add header value for injection
                    headerParams.add(new HttpParameter(name, value, HttpParameter.Type.HEADER));
                    // Add header name for injection
                    headerParams.add(new HttpParameter(name + "__NAME__", name, HttpParameter.Type.HEADER));
                    // Debug output to help troubleshoot header detection
                    System.out.println("Added header parameter: " + name + " = " + value);
                }
            }
        }

        System.out.println("Total header parameters found: " + headerParams.size());
        return headerParams;
    }
    
    /**
     * Check if a header is interesting for potential injections
     */
    private boolean isInterestingHeader(String headerName) {
        headerName = headerName.toLowerCase();

        // Headers commonly vulnerable to injection attacks
        String[] interestingHeaders = {
            // Standard headers
            "user-agent", "referer", "host", "origin", "accept", "accept-language",
            "accept-encoding", "accept-charset", "content-type", "content-length",

            // Authentication and authorization
            "authorization", "x-api-key", "x-auth-token", "x-access-token",
            "x-csrf-token", "x-xsrf-token", "x-requested-with",

            // Forwarding and proxy headers
            "x-forwarded-for", "x-forwarded-host", "x-forwarded-proto",
            "x-real-ip", "x-client-ip", "x-cluster-client-ip",

            // Custom application headers
            "x-custom-header", "x-application-id", "x-session-id", "x-user-id",
            "x-tenant-id", "x-correlation-id", "x-trace-id", "x-request-id",

            // Cache and CDN headers
            "cache-control", "pragma", "if-modified-since", "if-none-match",
            "x-cache", "x-cdn-provider",

            // Security headers (sometimes processed by backend)
            "x-frame-options", "x-content-type-options", "x-xss-protection",

            // Cookie (already included but keeping for clarity)
            "cookie"
        };

        for (String header : interestingHeaders) {
            if (headerName.equals(header)) {
                return true;
            }
        }

        // Also include any header starting with "x-" as these are often custom headers
        if (headerName.startsWith("x-")) {
            return true;
        }

        return false;
    }

    /**
     * Check if request is a WebSocket upgrade request
     */
    private boolean isWebSocketRequest(IRequestInfo requestInfo) {
        List<String> headers = requestInfo.getHeaders();
        boolean hasUpgrade = false;
        boolean hasConnection = false;
        boolean hasWebSocketKey = false;

        for (String header : headers) {
            String lowerHeader = header.toLowerCase();
            if (lowerHeader.startsWith("upgrade:") && lowerHeader.contains("websocket")) {
                hasUpgrade = true;
            } else if (lowerHeader.startsWith("connection:") && lowerHeader.contains("upgrade")) {
                hasConnection = true;
            } else if (lowerHeader.startsWith("sec-websocket-key:")) {
                hasWebSocketKey = true;
            }
        }

        return hasUpgrade && hasConnection && hasWebSocketKey;
    }

    /**
     * Parse WebSocket upgrade request parameters
     */
    private List<HttpParameter> parseWebSocketParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> wsParams = new ArrayList<>();

        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            String lowerHeader = header.toLowerCase();

            if (lowerHeader.startsWith("sec-websocket-")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();
                    wsParams.add(new HttpParameter("ws." + name, value, HttpParameter.Type.WEBSOCKET));
                    wsParams.add(new HttpParameter("ws." + name + "__NAME__", name, HttpParameter.Type.WEBSOCKET));
                }
            } else if (lowerHeader.startsWith("upgrade:") ||
                      (lowerHeader.startsWith("connection:") && lowerHeader.contains("upgrade"))) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();
                    wsParams.add(new HttpParameter("ws." + name, value, HttpParameter.Type.WEBSOCKET));
                }
            }
        }

        // Parse WebSocket subprotocols
        for (String header : headers) {
            if (header.toLowerCase().startsWith("sec-websocket-protocol:")) {
                String protocols = header.substring(header.indexOf(":") + 1).trim();
                String[] protocolList = protocols.split(",");
                for (int i = 0; i < protocolList.length; i++) {
                    String protocol = protocolList[i].trim();
                    wsParams.add(new HttpParameter("ws.protocol[" + i + "]", protocol, HttpParameter.Type.WEBSOCKET));
                }
            }
        }

        System.out.println("WebSocket parameters found: " + wsParams.size());
        return wsParams;
    }

    /**
     * Check if request contains Server-Sent Events
     */
    private boolean isServerSentEventsRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        return contentType != null && contentType.contains("text/event-stream");
    }

    /**
     * Parse Server-Sent Events data
     */
    private List<HttpParameter> parseServerSentEvents(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> sseParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Parse SSE format: data: {...}\n\n
        String[] lines = body.split("\n");
        int eventIndex = 0;

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("data:")) {
                String data = line.substring(5).trim();

                // Try to parse as JSON
                if (data.startsWith("{") && data.endsWith("}")) {
                    try {
                        List<HttpParameter> jsonParams = parseJsonRecursively(data, "sse.event[" + eventIndex + "]");
                        sseParams.addAll(jsonParams);
                    } catch (Exception e) {
                        // Fallback to plain text
                        sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].data", data, HttpParameter.Type.SSE));
                    }
                } else {
                    sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].data", data, HttpParameter.Type.SSE));
                }
                eventIndex++;
            } else if (line.startsWith("event:")) {
                String eventType = line.substring(6).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].type", eventType, HttpParameter.Type.SSE));
            } else if (line.startsWith("id:")) {
                String id = line.substring(3).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].id", id, HttpParameter.Type.SSE));
            } else if (line.startsWith("retry:")) {
                String retry = line.substring(6).trim();
                sseParams.add(new HttpParameter("sse.event[" + eventIndex + "].retry", retry, HttpParameter.Type.SSE));
            }
        }

        System.out.println("Server-Sent Events parameters found: " + sseParams.size());
        return sseParams;
    }

    /**
     * Parse JWT tokens from headers and body
     */
    private List<HttpParameter> parseJWTTokens(IRequestInfo requestInfo) {
        List<HttpParameter> jwtParams = new ArrayList<>();

        // Check Authorization header
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("authorization:")) {
                String authValue = header.substring(header.indexOf(":") + 1).trim();

                if (authValue.toLowerCase().startsWith("bearer ")) {
                    String token = authValue.substring(7).trim();
                    jwtParams.addAll(parseJWTToken(token, "auth.bearer"));
                } else if (authValue.toLowerCase().startsWith("jwt ")) {
                    String token = authValue.substring(4).trim();
                    jwtParams.addAll(parseJWTToken(token, "auth.jwt"));
                }
            }

            // Check custom JWT headers
            if (header.toLowerCase().contains("jwt") || header.toLowerCase().contains("token")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();

                    // Check if value looks like a JWT (three base64 parts separated by dots)
                    if (isJWTFormat(value)) {
                        jwtParams.addAll(parseJWTToken(value, "header." + name.toLowerCase()));
                    }
                }
            }
        }

        System.out.println("JWT parameters found: " + jwtParams.size());
        return jwtParams;
    }

    /**
     * Check if a string looks like a JWT token
     */
    private boolean isJWTFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        String[] parts = token.split("\\.");
        return parts.length == 3 &&
               parts[0].length() > 0 &&
               parts[1].length() > 0 &&
               parts[2].length() > 0;
    }

    /**
     * Parse individual JWT token
     */
    private List<HttpParameter> parseJWTToken(String token, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return params;
            }

            // Parse header (first part)
            try {
                String headerJson = decodeBase64Url(parts[0]);
                params.add(new HttpParameter(prefix + ".header", headerJson, HttpParameter.Type.JWT));
                params.addAll(parseJsonRecursively(headerJson, prefix + ".header"));
            } catch (Exception e) {
                params.add(new HttpParameter(prefix + ".header.raw", parts[0], HttpParameter.Type.JWT));
            }

            // Parse payload (second part)
            try {
                String payloadJson = decodeBase64Url(parts[1]);
                params.add(new HttpParameter(prefix + ".payload", payloadJson, HttpParameter.Type.JWT));
                params.addAll(parseJsonRecursively(payloadJson, prefix + ".payload"));
            } catch (Exception e) {
                params.add(new HttpParameter(prefix + ".payload.raw", parts[1], HttpParameter.Type.JWT));
            }

            // Add signature (third part) - don't decode as it's binary
            params.add(new HttpParameter(prefix + ".signature", parts[2], HttpParameter.Type.JWT));

            // Add full token for injection
            params.add(new HttpParameter(prefix + ".token", token, HttpParameter.Type.JWT));

        } catch (Exception e) {
            System.out.println("Error parsing JWT token: " + e.getMessage());
            // Add as raw token if parsing fails
            params.add(new HttpParameter(prefix + ".raw", token, HttpParameter.Type.JWT));
        }

        return params;
    }

    /**
     * Decode Base64 URL-safe string
     */
    private String decodeBase64Url(String base64Url) {
        // Add padding if needed
        String base64 = base64Url;
        while (base64.length() % 4 != 0) {
            base64 += "=";
        }

        // Replace URL-safe characters
        base64 = base64.replace('-', '+').replace('_', '/');

        // Decode
        try {
            byte[] decoded = java.util.Base64.getDecoder().decode(base64);
            return new String(decoded, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to decode Base64 URL: " + e.getMessage());
        }
    }

    /**
     * Parse Base64 encoded data from request body and parameters
     */
    private List<HttpParameter> parseBase64EncodedData(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> base64Params = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Look for Base64 patterns in body
        base64Params.addAll(findAndDecodeBase64(body, "body"));

        // Look for Base64 patterns in URL parameters
        for (IParameter param : requestInfo.getParameters()) {
            if (param.getType() == IParameter.PARAM_URL) {
                base64Params.addAll(findAndDecodeBase64(param.getValue(), "url." + param.getName()));
            }
        }

        // Look for Base64 patterns in headers
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.contains(":")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String headerName = parts[0].trim();
                    String headerValue = parts[1].trim();
                    base64Params.addAll(findAndDecodeBase64(headerValue, "header." + headerName.toLowerCase()));
                }
            }
        }

        System.out.println("Base64 encoded parameters found: " + base64Params.size());
        return base64Params;
    }

    /**
     * Find and decode Base64 strings in text
     */
    private List<HttpParameter> findAndDecodeBase64(String text, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        // Pattern for Base64 strings (at least 16 characters, valid Base64 characters)
        Pattern base64Pattern = Pattern.compile("([A-Za-z0-9+/]{16,}={0,2})");
        Matcher matcher = base64Pattern.matcher(text);

        int index = 0;
        while (matcher.find()) {
            String base64String = matcher.group(1);

            try {
                // Try to decode
                byte[] decoded = java.util.Base64.getDecoder().decode(base64String);
                String decodedString = new String(decoded, StandardCharsets.UTF_8);

                // Check if decoded string is meaningful (printable characters)
                if (isPrintableString(decodedString)) {
                    params.add(new HttpParameter(prefix + ".base64[" + index + "]", base64String, HttpParameter.Type.BASE64));
                    params.add(new HttpParameter(prefix + ".base64[" + index + "].decoded", decodedString, HttpParameter.Type.BASE64));

                    // If decoded string looks like JSON, parse it
                    if (decodedString.trim().startsWith("{") && decodedString.trim().endsWith("}")) {
                        try {
                            params.addAll(parseJsonRecursively(decodedString, prefix + ".base64[" + index + "].json"));
                        } catch (Exception e) {
                            // Not valid JSON, ignore
                        }
                    }

                    index++;
                }
            } catch (Exception e) {
                // Not valid Base64 or not UTF-8, ignore
            }
        }

        return params;
    }

    /**
     * Check if string contains mostly printable characters
     */
    private boolean isPrintableString(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        int printableCount = 0;
        for (char c : str.toCharArray()) {
            if (c >= 32 && c <= 126) { // Printable ASCII range
                printableCount++;
            }
        }

        // At least 80% printable characters
        return (double) printableCount / str.length() >= 0.8;
    }

    /**
     * Parse binary formats (MessagePack, CBOR, Avro, etc.)
     */
    private List<HttpParameter> parseBinaryFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> binaryParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        byte[] body = Arrays.copyOfRange(request, bodyOffset, request.length);

        if (body.length == 0) {
            return binaryParams;
        }

        // Check Content-Type for binary format hints
        String contentType = getContentType(requestInfo);

        if (contentType != null) {
            if (contentType.contains("application/msgpack") || contentType.contains("application/x-msgpack")) {
                binaryParams.addAll(parseMessagePack(body));
            } else if (contentType.contains("application/cbor")) {
                binaryParams.addAll(parseCBOR(body));
            } else if (contentType.contains("application/avro") || contentType.contains("avro/binary")) {
                binaryParams.addAll(parseAvro(body));
            } else if (contentType.contains("application/protobuf") || contentType.contains("application/x-protobuf")) {
                binaryParams.addAll(parseProtobuf(body));
            }
        }

        // Enhanced binary format detection with comprehensive magic byte recognition
        if (binaryParams.isEmpty()) {
            binaryParams.addAll(detectBinaryFormatEnhanced(body));
        }

        // Parse binary data with multiple extraction strategies
        binaryParams.addAll(parseBinaryWithMultipleStrategies(body));

        // Extract structured data from binary formats
        binaryParams.addAll(extractStructuredBinaryData(body));

        // Parse binary metadata and headers
        binaryParams.addAll(parseBinaryMetadata(body));

        System.out.println("Binary format parameters found: " + binaryParams.size());
        return binaryParams;
    }

    /**
     * Parse MessagePack format (simplified detection)
     */
    private List<HttpParameter> parseMessagePack(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // MessagePack format detection (simplified)
            // Look for common MessagePack type markers
            if (data.length > 0) {
                byte firstByte = data[0];

                // Fixed map (0x80-0x8f), fixed array (0x90-0x9f), fixed string (0xa0-0xbf)
                if ((firstByte & 0x80) == 0x80 || (firstByte & 0x90) == 0x90 || (firstByte & 0xa0) == 0xa0) {
                    params.add(new HttpParameter("msgpack.detected", "true", HttpParameter.Type.MESSAGEPACK));
                    params.add(new HttpParameter("msgpack.size", String.valueOf(data.length), HttpParameter.Type.MESSAGEPACK));
                    params.add(new HttpParameter("msgpack.first_byte", String.format("0x%02x", firstByte), HttpParameter.Type.MESSAGEPACK));

                    // Try to extract string-like data
                    params.addAll(extractBinaryStrings(data, "msgpack"));
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing MessagePack: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse CBOR format (simplified detection)
     */
    private List<HttpParameter> parseCBOR(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 0) {
                byte firstByte = data[0];

                // CBOR major types: 0-7 (first 3 bits)
                int majorType = (firstByte >> 5) & 0x07;

                params.add(new HttpParameter("cbor.detected", "true", HttpParameter.Type.CBOR));
                params.add(new HttpParameter("cbor.size", String.valueOf(data.length), HttpParameter.Type.CBOR));
                params.add(new HttpParameter("cbor.major_type", String.valueOf(majorType), HttpParameter.Type.CBOR));

                // Try to extract string-like data
                params.addAll(extractBinaryStrings(data, "cbor"));
            }
        } catch (Exception e) {
            System.out.println("Error parsing CBOR: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Avro format (simplified detection)
     */
    private List<HttpParameter> parseAvro(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 4) {
                // Avro files often start with magic bytes or have specific patterns
                params.add(new HttpParameter("avro.detected", "true", HttpParameter.Type.AVRO));
                params.add(new HttpParameter("avro.size", String.valueOf(data.length), HttpParameter.Type.AVRO));

                // Try to extract string-like data
                params.addAll(extractBinaryStrings(data, "avro"));
            }
        } catch (Exception e) {
            System.out.println("Error parsing Avro: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Protocol Buffers format (simplified detection)
     */
    private List<HttpParameter> parseProtobuf(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            if (data.length > 0) {
                params.add(new HttpParameter("protobuf.detected", "true", HttpParameter.Type.PROTOBUF));
                params.add(new HttpParameter("protobuf.size", String.valueOf(data.length), HttpParameter.Type.PROTOBUF));

                // Try to extract string-like data and field numbers
                params.addAll(extractProtobufFields(data));
            }
        } catch (Exception e) {
            System.out.println("Error parsing Protobuf: " + e.getMessage());
        }

        return params;
    }

    /**
     * Detect binary format by magic bytes and patterns
     */
    private List<HttpParameter> detectBinaryFormat(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        if (data.length == 0) {
            return params;
        }

        // Check for common binary format signatures
        if (data.length >= 4) {
            // Check for various magic bytes
            String hex = bytesToHex(data, 0, Math.min(4, data.length));
            params.add(new HttpParameter("binary.magic_bytes", hex, HttpParameter.Type.BINARY));
        }

        // Try to extract any readable strings from binary data
        params.addAll(extractBinaryStrings(data, "binary"));

        return params;
    }

    /**
     * Extract readable strings from binary data
     */
    private List<HttpParameter> extractBinaryStrings(byte[] data, String prefix) {
        List<HttpParameter> params = new ArrayList<>();

        StringBuilder currentString = new StringBuilder();
        int stringIndex = 0;

        for (int i = 0; i < data.length; i++) {
            byte b = data[i];

            // Check if byte is printable ASCII
            if (b >= 32 && b <= 126) {
                currentString.append((char) b);
            } else {
                // End of string, save if long enough
                if (currentString.length() >= 4) {
                    params.add(new HttpParameter(prefix + ".string[" + stringIndex + "]",
                                               currentString.toString(), HttpParameter.Type.BINARY));
                    stringIndex++;
                }
                currentString = new StringBuilder();
            }
        }

        // Save last string if exists
        if (currentString.length() >= 4) {
            params.add(new HttpParameter(prefix + ".string[" + stringIndex + "]",
                                       currentString.toString(), HttpParameter.Type.BINARY));
        }

        return params;
    }

    /**
     * Enhanced binary format detection with comprehensive magic byte recognition
     */
    private List<HttpParameter> detectBinaryFormatEnhanced(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        if (data.length == 0) {
            return params;
        }

        // Comprehensive magic byte detection
        Map<String, String> magicBytes = new HashMap<>();
        magicBytes.put("89504E47", "PNG");
        magicBytes.put("FFD8FF", "JPEG");
        magicBytes.put("47494638", "GIF");
        magicBytes.put("504B0304", "ZIP/JAR");
        magicBytes.put("504B0506", "ZIP_EMPTY");
        magicBytes.put("504B0708", "ZIP_SPANNED");
        magicBytes.put("52617221", "RAR");
        magicBytes.put("377ABCAF", "7Z");
        magicBytes.put("1F8B08", "GZIP");
        magicBytes.put("425A68", "BZIP2");
        magicBytes.put("FD377A58", "XZ");
        magicBytes.put("4D5A", "PE/EXE");
        magicBytes.put("7F454C46", "ELF");
        magicBytes.put("CAFEBABE", "JAVA_CLASS");
        magicBytes.put("FEEDFACE", "MACH_O_32");
        magicBytes.put("FEEDFACF", "MACH_O_64");
        magicBytes.put("D0CF11E0", "MS_OFFICE");
        magicBytes.put("25504446", "PDF");
        magicBytes.put("38425053", "PSD");
        magicBytes.put("52494646", "RIFF/WAV/AVI");
        magicBytes.put("664C6143", "FLAC");
        magicBytes.put("4F676753", "OGG");
        magicBytes.put("49443303", "MP3_ID3v2");
        magicBytes.put("FFFB", "MP3");
        magicBytes.put("00000018", "MP4");
        magicBytes.put("00000020", "MP4_ALT");
        magicBytes.put("6674797069736F6D", "MP4_ISOM");

        // Check magic bytes
        if (data.length >= 4) {
            String hex4 = bytesToHex(data, 0, 4);
            String hex3 = bytesToHex(data, 0, 3);
            String hex2 = bytesToHex(data, 0, 2);
            String hex8 = data.length >= 8 ? bytesToHex(data, 0, 8) : "";

            for (Map.Entry<String, String> entry : magicBytes.entrySet()) {
                String magic = entry.getKey();
                String format = entry.getValue();

                if ((magic.length() == 8 && hex4.startsWith(magic)) ||
                    (magic.length() == 6 && hex3.startsWith(magic)) ||
                    (magic.length() == 4 && hex2.startsWith(magic)) ||
                    (magic.length() == 16 && hex8.startsWith(magic))) {

                    params.add(new HttpParameter("binary.format", format, HttpParameter.Type.BINARY));
                    params.add(new HttpParameter("binary.magic_bytes", magic, HttpParameter.Type.BINARY));
                    params.add(new HttpParameter("binary.detected_hex", hex4, HttpParameter.Type.BINARY));
                    break;
                }
            }
        }

        // Additional format-specific detection
        params.addAll(detectSpecialBinaryFormats(data));

        return params;
    }

    /**
     * Parse binary data with multiple extraction strategies
     */
    private List<HttpParameter> parseBinaryWithMultipleStrategies(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Strategy 1: Extract all printable ASCII strings
            params.addAll(extractBinaryStrings(data, "ascii"));

            // Strategy 2: Extract UTF-8 strings
            params.addAll(extractUTF8Strings(data));

            // Strategy 3: Extract numeric patterns
            params.addAll(extractNumericPatterns(data));

            // Strategy 4: Extract URL-like patterns
            params.addAll(extractUrlPatterns(data));

            // Strategy 5: Extract JSON-like patterns
            params.addAll(extractJsonPatterns(data));

            // Strategy 6: Extract XML-like patterns
            params.addAll(extractXmlPatterns(data));

        } catch (Exception e) {
            System.out.println("Error in binary parsing strategies: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract structured data from binary formats
     */
    private List<HttpParameter> extractStructuredBinaryData(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for length-prefixed strings (common in binary protocols)
            params.addAll(extractLengthPrefixedStrings(data));

            // Look for null-terminated strings
            params.addAll(extractNullTerminatedStrings(data));

            // Look for repeated patterns (arrays/lists)
            params.addAll(extractRepeatedPatterns(data));

            // Look for key-value patterns
            params.addAll(extractBinaryKeyValuePairs(data));

        } catch (Exception e) {
            System.out.println("Error extracting structured binary data: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse binary metadata and headers
     */
    private List<HttpParameter> parseBinaryMetadata(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            params.add(new HttpParameter("binary.size", String.valueOf(data.length), HttpParameter.Type.BINARY));

            // Calculate entropy (measure of randomness)
            double entropy = calculateEntropy(data);
            params.add(new HttpParameter("binary.entropy", String.format("%.2f", entropy), HttpParameter.Type.BINARY));

            // Count printable characters
            int printableCount = 0;
            for (byte b : data) {
                if (b >= 32 && b <= 126) {
                    printableCount++;
                }
            }
            double printableRatio = (double) printableCount / data.length;
            params.add(new HttpParameter("binary.printable_ratio", String.format("%.2f", printableRatio), HttpParameter.Type.BINARY));

            // Check for common patterns
            if (hasRepeatingBytes(data)) {
                params.add(new HttpParameter("binary.has_repeating_bytes", "true", HttpParameter.Type.BINARY));
            }

            if (hasNullBytes(data)) {
                params.add(new HttpParameter("binary.has_null_bytes", "true", HttpParameter.Type.BINARY));
            }

        } catch (Exception e) {
            System.out.println("Error parsing binary metadata: " + e.getMessage());
        }

        return params;
    }

    /**
     * Detect special binary formats
     */
    private List<HttpParameter> detectSpecialBinaryFormats(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Check for MessagePack patterns
            if (data.length > 0) {
                byte firstByte = data[0];
                if ((firstByte & 0x80) == 0x80 || (firstByte & 0x90) == 0x90 || (firstByte & 0xa0) == 0xa0) {
                    params.add(new HttpParameter("binary.likely_msgpack", "true", HttpParameter.Type.BINARY));
                }
            }

            // Check for CBOR patterns
            if (data.length > 0) {
                byte firstByte = data[0];
                int majorType = (firstByte >> 5) & 0x07;
                if (majorType >= 0 && majorType <= 7) {
                    params.add(new HttpParameter("binary.likely_cbor", "true", HttpParameter.Type.BINARY));
                    params.add(new HttpParameter("binary.cbor_major_type", String.valueOf(majorType), HttpParameter.Type.BINARY));
                }
            }

            // Check for Protocol Buffer patterns (varint encoding)
            if (hasVarintPatterns(data)) {
                params.add(new HttpParameter("binary.likely_protobuf", "true", HttpParameter.Type.BINARY));
            }

        } catch (Exception e) {
            System.out.println("Error detecting special binary formats: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract UTF-8 strings from binary data
     */
    private List<HttpParameter> extractUTF8Strings(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String utf8String = new String(data, "UTF-8");

            // Look for valid UTF-8 sequences
            Pattern utf8Pattern = Pattern.compile("[\\p{L}\\p{N}\\p{P}\\p{S}\\s]{4,}");
            Matcher matcher = utf8Pattern.matcher(utf8String);

            int stringIndex = 0;
            while (matcher.find() && stringIndex < 10) {
                String match = matcher.group().trim();
                if (match.length() >= 4) {
                    params.add(new HttpParameter("utf8.string[" + stringIndex + "]", match, HttpParameter.Type.BINARY));
                    stringIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error extracting UTF-8 strings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract numeric patterns from binary data
     */
    private List<HttpParameter> extractNumericPatterns(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Extract 32-bit integers (little and big endian)
            for (int i = 0; i <= data.length - 4; i += 4) {
                int littleEndian = (data[i] & 0xFF) |
                                 ((data[i+1] & 0xFF) << 8) |
                                 ((data[i+2] & 0xFF) << 16) |
                                 ((data[i+3] & 0xFF) << 24);

                int bigEndian = ((data[i] & 0xFF) << 24) |
                               ((data[i+1] & 0xFF) << 16) |
                               ((data[i+2] & 0xFF) << 8) |
                               (data[i+3] & 0xFF);

                // Only add if the numbers seem reasonable (not too large)
                if (Math.abs(littleEndian) < 1000000) {
                    params.add(new HttpParameter("int32_le[" + (i/4) + "]", String.valueOf(littleEndian), HttpParameter.Type.BINARY));
                }
                if (Math.abs(bigEndian) < 1000000) {
                    params.add(new HttpParameter("int32_be[" + (i/4) + "]", String.valueOf(bigEndian), HttpParameter.Type.BINARY));
                }

                if (params.size() > 20) break; // Limit output
            }

        } catch (Exception e) {
            System.out.println("Error extracting numeric patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract URL patterns from binary data
     */
    private List<HttpParameter> extractUrlPatterns(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String dataString = new String(data, "UTF-8");

            // Look for URL patterns
            Pattern urlPattern = Pattern.compile("https?://[\\w\\.-]+(?:/[\\w\\.-]*)*(?:\\?[\\w&=%-]*)?");
            Matcher matcher = urlPattern.matcher(dataString);

            int urlIndex = 0;
            while (matcher.find() && urlIndex < 5) {
                String url = matcher.group();
                params.add(new HttpParameter("url[" + urlIndex + "]", url, HttpParameter.Type.BINARY));
                urlIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error extracting URL patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract JSON patterns from binary data
     */
    private List<HttpParameter> extractJsonPatterns(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String dataString = new String(data, "UTF-8");

            // Look for JSON-like patterns
            Pattern jsonPattern = Pattern.compile("\\{[^{}]*\"[^\"]+\"[^{}]*:[^{}]*[^{}]*\\}");
            Matcher matcher = jsonPattern.matcher(dataString);

            int jsonIndex = 0;
            while (matcher.find() && jsonIndex < 5) {
                String json = matcher.group();
                params.add(new HttpParameter("json[" + jsonIndex + "]", json, HttpParameter.Type.BINARY));
                jsonIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error extracting JSON patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract XML patterns from binary data
     */
    private List<HttpParameter> extractXmlPatterns(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String dataString = new String(data, "UTF-8");

            // Look for XML-like patterns
            Pattern xmlPattern = Pattern.compile("<[^>]+>[^<]*</[^>]+>");
            Matcher matcher = xmlPattern.matcher(dataString);

            int xmlIndex = 0;
            while (matcher.find() && xmlIndex < 5) {
                String xml = matcher.group();
                params.add(new HttpParameter("xml[" + xmlIndex + "]", xml, HttpParameter.Type.BINARY));
                xmlIndex++;
            }

        } catch (Exception e) {
            System.out.println("Error extracting XML patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check for varint patterns (Protocol Buffer encoding)
     */
    private boolean hasVarintPatterns(byte[] data) {
        try {
            int varintCount = 0;
            for (int i = 0; i < data.length; i++) {
                byte b = data[i];
                // Varint continuation bit is the MSB
                if ((b & 0x80) != 0) {
                    varintCount++;
                    // Skip the rest of this varint
                    while (i < data.length && (data[i] & 0x80) != 0) {
                        i++;
                    }
                }
            }
            // If more than 10% of bytes look like varint, it might be protobuf
            return varintCount > data.length * 0.1;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Extract length-prefixed strings
     */
    private List<HttpParameter> extractLengthPrefixedStrings(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            for (int i = 0; i < data.length - 4; i++) {
                // Try different length encodings
                int length1 = data[i] & 0xFF; // 1-byte length
                int length2 = ((data[i] & 0xFF) << 8) | (data[i+1] & 0xFF); // 2-byte big endian
                int length4 = ((data[i] & 0xFF) << 24) | ((data[i+1] & 0xFF) << 16) |
                             ((data[i+2] & 0xFF) << 8) | (data[i+3] & 0xFF); // 4-byte big endian

                // Check 1-byte length prefix
                if (length1 > 4 && length1 < 100 && i + 1 + length1 <= data.length) {
                    String str = new String(data, i + 1, length1, "UTF-8");
                    if (isPrintableString(str)) {
                        params.add(new HttpParameter("length_prefixed_1[" + i + "]", str, HttpParameter.Type.BINARY));
                    }
                }

                // Check 2-byte length prefix
                if (length2 > 4 && length2 < 1000 && i + 2 + length2 <= data.length) {
                    String str = new String(data, i + 2, length2, "UTF-8");
                    if (isPrintableString(str)) {
                        params.add(new HttpParameter("length_prefixed_2[" + i + "]", str, HttpParameter.Type.BINARY));
                    }
                }

                if (params.size() > 10) break; // Limit output
            }

        } catch (Exception e) {
            System.out.println("Error extracting length-prefixed strings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract null-terminated strings
     */
    private List<HttpParameter> extractNullTerminatedStrings(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            StringBuilder currentString = new StringBuilder();
            int stringIndex = 0;

            for (int i = 0; i < data.length; i++) {
                byte b = data[i];

                if (b == 0) {
                    // Null terminator found
                    if (currentString.length() >= 4) {
                        String str = currentString.toString();
                        if (isPrintableString(str)) {
                            params.add(new HttpParameter("null_terminated[" + stringIndex + "]", str, HttpParameter.Type.BINARY));
                            stringIndex++;
                        }
                    }
                    currentString = new StringBuilder();
                } else if (b >= 32 && b <= 126) {
                    // Printable ASCII
                    currentString.append((char) b);
                } else {
                    // Non-printable, reset string
                    currentString = new StringBuilder();
                }

                if (stringIndex >= 10) break; // Limit output
            }

        } catch (Exception e) {
            System.out.println("Error extracting null-terminated strings: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract repeated patterns (arrays/lists)
     */
    private List<HttpParameter> extractRepeatedPatterns(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for repeated byte sequences
            Map<String, Integer> patterns = new HashMap<>();

            for (int len = 4; len <= 16; len++) {
                for (int i = 0; i <= data.length - len; i++) {
                    String pattern = bytesToHex(data, i, len);
                    patterns.put(pattern, patterns.getOrDefault(pattern, 0) + 1);
                }
            }

            // Report patterns that repeat more than twice
            int patternIndex = 0;
            for (Map.Entry<String, Integer> entry : patterns.entrySet()) {
                if (entry.getValue() > 2 && patternIndex < 5) {
                    params.add(new HttpParameter("repeated_pattern[" + patternIndex + "].hex",
                                               entry.getKey(), HttpParameter.Type.BINARY));
                    params.add(new HttpParameter("repeated_pattern[" + patternIndex + "].count",
                                               String.valueOf(entry.getValue()), HttpParameter.Type.BINARY));
                    patternIndex++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error extracting repeated patterns: " + e.getMessage());
        }

        return params;
    }

    /**
     * Extract binary key-value pairs
     */
    private List<HttpParameter> extractBinaryKeyValuePairs(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Look for patterns like: [key_length][key][value_length][value]
            for (int i = 0; i < data.length - 8; i++) {
                int keyLen = data[i] & 0xFF;
                if (keyLen > 0 && keyLen < 50 && i + 1 + keyLen < data.length) {
                    String key = new String(data, i + 1, keyLen, "UTF-8");

                    if (isPrintableString(key)) {
                        int valuePos = i + 1 + keyLen;
                        if (valuePos < data.length) {
                            int valueLen = data[valuePos] & 0xFF;
                            if (valueLen > 0 && valueLen < 200 && valuePos + 1 + valueLen <= data.length) {
                                String value = new String(data, valuePos + 1, valueLen, "UTF-8");
                                if (isPrintableString(value)) {
                                    params.add(new HttpParameter("kv_pair." + key, value, HttpParameter.Type.BINARY));
                                }
                            }
                        }
                    }
                }

                if (params.size() > 10) break; // Limit output
            }

        } catch (Exception e) {
            System.out.println("Error extracting binary key-value pairs: " + e.getMessage());
        }

        return params;
    }

    /**
     * Calculate entropy of binary data
     */
    private double calculateEntropy(byte[] data) {
        try {
            int[] counts = new int[256];
            for (byte b : data) {
                counts[b & 0xFF]++;
            }

            double entropy = 0.0;
            for (int count : counts) {
                if (count > 0) {
                    double probability = (double) count / data.length;
                    entropy -= probability * (Math.log(probability) / Math.log(2));
                }
            }

            return entropy;
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * Check if data has repeating bytes
     */
    private boolean hasRepeatingBytes(byte[] data) {
        try {
            Map<Byte, Integer> counts = new HashMap<>();
            for (byte b : data) {
                counts.put(b, counts.getOrDefault(b, 0) + 1);
            }

            for (int count : counts.values()) {
                if (count > data.length * 0.3) { // More than 30% of the same byte
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if data has null bytes
     */
    private boolean hasNullBytes(byte[] data) {
        for (byte b : data) {
            if (b == 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * Extract Protocol Buffer fields (simplified)
     */
    private List<HttpParameter> extractProtobufFields(byte[] data) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            int i = 0;
            int fieldIndex = 0;

            while (i < data.length && fieldIndex < 20) { // Limit to prevent infinite loops
                if (i + 1 >= data.length) break;

                // Read varint tag
                int tag = data[i] & 0xFF;
                int fieldNumber = tag >> 3;
                int wireType = tag & 0x07;

                params.add(new HttpParameter("protobuf.field[" + fieldIndex + "].number",
                                           String.valueOf(fieldNumber), HttpParameter.Type.PROTOBUF));
                params.add(new HttpParameter("protobuf.field[" + fieldIndex + "].wire_type",
                                           String.valueOf(wireType), HttpParameter.Type.PROTOBUF));

                i++;
                fieldIndex++;

                // Skip field data based on wire type (simplified)
                switch (wireType) {
                    case 0: // Varint
                        while (i < data.length && (data[i] & 0x80) != 0) i++;
                        if (i < data.length) i++;
                        break;
                    case 1: // 64-bit
                        i += 8;
                        break;
                    case 2: // Length-delimited
                        if (i < data.length) {
                            int length = data[i] & 0xFF;
                            i += 1 + length;
                        }
                        break;
                    case 5: // 32-bit
                        i += 4;
                        break;
                    default:
                        i++; // Unknown wire type, skip one byte
                }
            }
        } catch (Exception e) {
            System.out.println("Error extracting Protobuf fields: " + e.getMessage());
        }

        return params;
    }

    /**
     * Convert bytes to hex string
     */
    private String bytesToHex(byte[] bytes, int offset, int length) {
        StringBuilder hex = new StringBuilder();
        for (int i = offset; i < offset + length && i < bytes.length; i++) {
            hex.append(String.format("%02x", bytes[i] & 0xFF));
        }
        return hex.toString();
    }

    /**
     * Parse configuration formats (YAML, TOML, INI)
     */
    private List<HttpParameter> parseConfigurationFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> configParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        if (body.trim().isEmpty()) {
            return configParams;
        }

        // Check Content-Type for format hints
        String contentType = getContentType(requestInfo);

        if (contentType != null) {
            if (contentType.contains("application/yaml") || contentType.contains("text/yaml") ||
                contentType.contains("application/x-yaml")) {
                configParams.addAll(parseYAML(body));
            } else if (contentType.contains("application/toml") || contentType.contains("text/toml")) {
                configParams.addAll(parseTOML(body));
            } else if (contentType.contains("text/plain") && (body.contains("[") || body.contains("="))) {
                // Could be INI format
                configParams.addAll(parseINI(body));
            }
        } else {
            // Try to detect format by content
            if (body.contains("---") || body.contains(":") && body.contains("\n")) {
                configParams.addAll(parseYAML(body));
            } else if (body.contains("[") && body.contains("]") && body.contains("=")) {
                if (body.contains("\"\"\"") || body.contains("'''")) {
                    configParams.addAll(parseTOML(body));
                } else {
                    configParams.addAll(parseINI(body));
                }
            }
        }

        System.out.println("Configuration format parameters found: " + configParams.size());
        return configParams;
    }

    /**
     * Parse YAML format (simplified)
     */
    private List<HttpParameter> parseYAML(String yaml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = yaml.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // Handle YAML key-value pairs
                if (line.contains(":")) {
                    String[] parts = line.split(":", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        } else if (value.startsWith("'") && value.endsWith("'")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("yaml." + fullKey, value, HttpParameter.Type.YAML));
                        params.add(new HttpParameter("yaml." + fullKey + "__NAME__", key, HttpParameter.Type.YAML));

                        // If value is empty, this might be a section header
                        if (value.isEmpty()) {
                            currentSection = key;
                        }
                    }
                }

                // Handle YAML arrays
                if (line.startsWith("-")) {
                    String value = line.substring(1).trim();
                    params.add(new HttpParameter("yaml.array_item", value, HttpParameter.Type.YAML));
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing YAML: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse TOML format (simplified)
     */
    private List<HttpParameter> parseTOML(String toml) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = toml.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // Handle TOML sections [section]
                if (line.startsWith("[") && line.endsWith("]")) {
                    currentSection = line.substring(1, line.length() - 1);
                    params.add(new HttpParameter("toml.section", currentSection, HttpParameter.Type.TOML));
                    continue;
                }

                // Handle TOML key-value pairs
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        } else if (value.startsWith("'") && value.endsWith("'")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("toml." + fullKey, value, HttpParameter.Type.TOML));
                        params.add(new HttpParameter("toml." + fullKey + "__NAME__", key, HttpParameter.Type.TOML));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing TOML: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse INI format (simplified)
     */
    private List<HttpParameter> parseINI(String ini) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            String[] lines = ini.split("\n");
            String currentSection = "";

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith(";") || line.startsWith("#")) {
                    continue;
                }

                // Handle INI sections [section]
                if (line.startsWith("[") && line.endsWith("]")) {
                    currentSection = line.substring(1, line.length() - 1);
                    params.add(new HttpParameter("ini.section", currentSection, HttpParameter.Type.INI));
                    continue;
                }

                // Handle INI key-value pairs
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();

                        // Remove quotes from value
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        }

                        String fullKey = currentSection.isEmpty() ? key : currentSection + "." + key;
                        params.add(new HttpParameter("ini." + fullKey, value, HttpParameter.Type.INI));
                        params.add(new HttpParameter("ini." + fullKey + "__NAME__", key, HttpParameter.Type.INI));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error parsing INI: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse template formats (Handlebars, Jinja2, etc.)
     */
    private List<HttpParameter> parseTemplateFormats(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> templateParams = new ArrayList<>();

        // Get request body
        int bodyOffset = requestInfo.getBodyOffset();
        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        if (body.trim().isEmpty()) {
            return templateParams;
        }

        // Detect template format by patterns
        if (body.contains("{{") && body.contains("}}")) {
            templateParams.addAll(parseHandlebars(body));
        }

        if (body.contains("{%") && body.contains("%}")) {
            templateParams.addAll(parseJinja2(body));
        }

        if (body.contains("<%") && body.contains("%>")) {
            templateParams.addAll(parseERB(body));
        }

        System.out.println("Template format parameters found: " + templateParams.size());
        return templateParams;
    }

    /**
     * Parse Handlebars/Mustache templates
     */
    private List<HttpParameter> parseHandlebars(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for Handlebars expressions: {{variable}}, {{#each items}}, {{helper arg1 arg2}}
            Pattern handlebarsPattern = Pattern.compile("\\{\\{([^}]+)\\}\\}");
            Matcher matcher = handlebarsPattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();

                // Add the full expression
                params.add(new HttpParameter("handlebars.expression[" + index + "]", expression, HttpParameter.Type.HANDLEBARS));

                // Parse different types of expressions
                if (expression.startsWith("#")) {
                    // Block helper: {{#each items}}
                    String[] parts = expression.substring(1).split("\\s+");
                    if (parts.length > 0) {
                        params.add(new HttpParameter("handlebars.block_helper[" + index + "]", parts[0], HttpParameter.Type.HANDLEBARS));
                        if (parts.length > 1) {
                            params.add(new HttpParameter("handlebars.block_context[" + index + "]", parts[1], HttpParameter.Type.HANDLEBARS));
                        }
                    }
                } else if (expression.startsWith("/")) {
                    // Closing block: {{/each}}
                    params.add(new HttpParameter("handlebars.block_end[" + index + "]", expression.substring(1), HttpParameter.Type.HANDLEBARS));
                } else if (expression.contains(" ")) {
                    // Helper with arguments: {{helper arg1 arg2}}
                    String[] parts = expression.split("\\s+");
                    params.add(new HttpParameter("handlebars.helper[" + index + "]", parts[0], HttpParameter.Type.HANDLEBARS));
                    for (int i = 1; i < parts.length; i++) {
                        params.add(new HttpParameter("handlebars.arg[" + index + "][" + (i-1) + "]", parts[i], HttpParameter.Type.HANDLEBARS));
                    }
                } else {
                    // Simple variable: {{variable}}
                    params.add(new HttpParameter("handlebars.variable[" + index + "]", expression, HttpParameter.Type.HANDLEBARS));

                    // Handle dot notation: {{user.name}}
                    if (expression.contains(".")) {
                        String[] parts = expression.split("\\.");
                        for (int i = 0; i < parts.length; i++) {
                            params.add(new HttpParameter("handlebars.path[" + index + "][" + i + "]", parts[i], HttpParameter.Type.HANDLEBARS));
                        }
                    }
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing Handlebars: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse Jinja2 templates
     */
    private List<HttpParameter> parseJinja2(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for Jinja2 expressions: {% for %}, {{ variable }}, {# comment #}
            Pattern jinja2Pattern = Pattern.compile("\\{[%{#]([^%}#]+)[%}#]\\}");
            Matcher matcher = jinja2Pattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();
                String fullMatch = matcher.group(0);

                // Add the full expression
                params.add(new HttpParameter("jinja2.expression[" + index + "]", expression, HttpParameter.Type.JINJA2));

                // Determine expression type
                if (fullMatch.startsWith("{%")) {
                    // Control structure: {% for item in items %}
                    params.add(new HttpParameter("jinja2.control[" + index + "]", expression, HttpParameter.Type.JINJA2));

                    if (expression.startsWith("for ")) {
                        String[] parts = expression.split("\\s+");
                        if (parts.length >= 4 && "in".equals(parts[2])) {
                            params.add(new HttpParameter("jinja2.for_var[" + index + "]", parts[1], HttpParameter.Type.JINJA2));
                            params.add(new HttpParameter("jinja2.for_iterable[" + index + "]", parts[3], HttpParameter.Type.JINJA2));
                        }
                    } else if (expression.startsWith("if ")) {
                        String condition = expression.substring(3).trim();
                        params.add(new HttpParameter("jinja2.if_condition[" + index + "]", condition, HttpParameter.Type.JINJA2));
                    }
                } else if (fullMatch.startsWith("{{")) {
                    // Variable: {{ variable }}
                    params.add(new HttpParameter("jinja2.variable[" + index + "]", expression, HttpParameter.Type.JINJA2));

                    // Handle filters: {{ variable|filter }}
                    if (expression.contains("|")) {
                        String[] parts = expression.split("\\|");
                        params.add(new HttpParameter("jinja2.var_name[" + index + "]", parts[0].trim(), HttpParameter.Type.JINJA2));
                        for (int i = 1; i < parts.length; i++) {
                            params.add(new HttpParameter("jinja2.filter[" + index + "][" + (i-1) + "]", parts[i].trim(), HttpParameter.Type.JINJA2));
                        }
                    }

                    // Handle dot notation: {{ user.profile.name }}
                    if (expression.contains(".")) {
                        String[] parts = expression.split("\\.");
                        for (int i = 0; i < parts.length; i++) {
                            params.add(new HttpParameter("jinja2.path[" + index + "][" + i + "]", parts[i], HttpParameter.Type.JINJA2));
                        }
                    }
                } else if (fullMatch.startsWith("{#")) {
                    // Comment: {# comment #}
                    params.add(new HttpParameter("jinja2.comment[" + index + "]", expression, HttpParameter.Type.JINJA2));
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing Jinja2: " + e.getMessage());
        }

        return params;
    }

    /**
     * Parse ERB (Embedded Ruby) templates
     */
    private List<HttpParameter> parseERB(String template) {
        List<HttpParameter> params = new ArrayList<>();

        try {
            // Pattern for ERB expressions: <% code %>, <%= expression %>
            Pattern erbPattern = Pattern.compile("<%=?([^%]+)%>");
            Matcher matcher = erbPattern.matcher(template);

            int index = 0;
            while (matcher.find()) {
                String expression = matcher.group(1).trim();
                String fullMatch = matcher.group(0);

                // Add the full expression
                params.add(new HttpParameter("erb.expression[" + index + "]", expression, HttpParameter.Type.TEMPLATE));

                // Determine if it's output or code
                if (fullMatch.startsWith("<%=")) {
                    params.add(new HttpParameter("erb.output[" + index + "]", expression, HttpParameter.Type.TEMPLATE));
                } else {
                    params.add(new HttpParameter("erb.code[" + index + "]", expression, HttpParameter.Type.TEMPLATE));
                }

                index++;
            }
        } catch (Exception e) {
            System.out.println("Error parsing ERB: " + e.getMessage());
        }

        return params;
    }

    /**
     * Check if request is gRPC/Protocol Buffers
     */
    private boolean isGrpcRequest(IRequestInfo requestInfo) {
        String contentType = getContentType(requestInfo);
        List<String> headers = requestInfo.getHeaders();

        // Check content type
        if (contentType != null && (
            contentType.contains("application/grpc") ||
            contentType.contains("application/x-protobuf") ||
            contentType.contains("application/protobuf")
        )) {
            return true;
        }

        // Check for gRPC headers
        for (String header : headers) {
            String lowerHeader = header.toLowerCase();
            if (lowerHeader.startsWith("grpc-") ||
                lowerHeader.contains("te: trailers") ||
                lowerHeader.contains("content-type: application/grpc")) {
                return true;
            }
        }

        return false;
    }

    /**
     * Parse gRPC/Protocol Buffers parameters
     */
    private List<HttpParameter> parseGrpcParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> grpcParams = new ArrayList<>();

        try {
            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            byte[] body = Arrays.copyOfRange(request, bodyOffset, request.length);

            if (body.length == 0) {
                return grpcParams;
            }

            // Add the entire gRPC body as a parameter for injection testing
            String bodyString = new String(body, StandardCharsets.UTF_8);
            grpcParams.add(new HttpParameter("grpc.body", bodyString, HttpParameter.Type.GRPC));

            // Try to parse as text-based protobuf (JSON format)
            if (bodyString.trim().startsWith("{")) {
                grpcParams.addAll(parseJsonRecursively(bodyString, "grpc"));
            }

            // Add gRPC headers as parameters
            List<String> headers = requestInfo.getHeaders();
            for (String header : headers) {
                if (header.toLowerCase().startsWith("grpc-")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        grpcParams.add(new HttpParameter("grpc.header." + name, value, HttpParameter.Type.GRPC));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing gRPC parameters: " + e.getMessage());
        }

        return grpcParams;
    }

    /**
     * Parse REST API path parameters and resource identifiers
     */
    private List<HttpParameter> parseRestApiParameters(IRequestInfo requestInfo) {
        List<HttpParameter> restParams = new ArrayList<>();

        try {
            String url = requestInfo.getUrl().getPath();
            String[] pathSegments = url.split("/");

            for (int i = 0; i < pathSegments.length; i++) {
                String segment = pathSegments[i];
                if (!segment.isEmpty()) {
                    // Check if segment looks like an ID or parameter
                    if (isLikelyApiParameter(segment)) {
                        restParams.add(new HttpParameter("rest.path[" + i + "]", segment, HttpParameter.Type.REST_PATH));

                        // Also add as potential ID parameter
                        if (isLikelyId(segment)) {
                            restParams.add(new HttpParameter("rest.id[" + i + "]", segment, HttpParameter.Type.REST_PATH));
                        }
                    }
                }
            }

            // Parse API versioning
            if (url.contains("/v") || url.contains("/api/")) {
                Pattern versionPattern = Pattern.compile("/v(\\d+(?:\\.\\d+)*)/");
                Matcher matcher = versionPattern.matcher(url);
                if (matcher.find()) {
                    restParams.add(new HttpParameter("rest.api_version", matcher.group(1), HttpParameter.Type.REST_PATH));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing REST API parameters: " + e.getMessage());
        }

        return restParams;
    }

    /**
     * Check if a path segment is likely an API parameter
     */
    private boolean isLikelyApiParameter(String segment) {
        // Check for numeric IDs
        if (segment.matches("\\d+")) return true;

        // Check for UUIDs
        if (segment.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")) return true;

        // Check for base64-like strings
        if (segment.length() > 10 && segment.matches("[A-Za-z0-9+/=]+")) return true;

        // Check for hex strings
        if (segment.length() > 8 && segment.matches("[0-9a-fA-F]+")) return true;

        // Check for encoded parameters
        if (segment.contains("%") || segment.contains("+")) return true;

        return false;
    }

    /**
     * Check if a segment looks like an ID
     */
    private boolean isLikelyId(String segment) {
        return segment.matches("\\d+") ||
               segment.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}") ||
               (segment.length() > 10 && segment.matches("[A-Za-z0-9+/=]+"));
    }

    /**
     * Parse authentication parameters (tokens, API keys, etc.)
     */
    private List<HttpParameter> parseAuthenticationParameters(IRequestInfo requestInfo) {
        List<HttpParameter> authParams = new ArrayList<>();

        try {
            List<String> headers = requestInfo.getHeaders();

            for (String header : headers) {
                String lowerHeader = header.toLowerCase();

                // Authorization header
                if (lowerHeader.startsWith("authorization:")) {
                    String authValue = header.substring("authorization:".length()).trim();
                    authParams.add(new HttpParameter("auth.authorization", authValue, HttpParameter.Type.AUTH));

                    // Parse different auth types
                    if (authValue.startsWith("Bearer ")) {
                        String token = authValue.substring("Bearer ".length());
                        authParams.add(new HttpParameter("auth.bearer_token", token, HttpParameter.Type.AUTH));

                        // Check if it's a JWT
                        if (token.contains(".")) {
                            authParams.addAll(parseJWTToken(token, "auth.jwt"));
                        }
                    } else if (authValue.startsWith("Basic ")) {
                        String credentials = authValue.substring("Basic ".length());
                        authParams.add(new HttpParameter("auth.basic_credentials", credentials, HttpParameter.Type.AUTH));
                    } else if (authValue.startsWith("Digest ")) {
                        String digest = authValue.substring("Digest ".length());
                        authParams.add(new HttpParameter("auth.digest", digest, HttpParameter.Type.AUTH));
                    }
                }

                // API Key headers
                if (lowerHeader.startsWith("x-api-key:") ||
                    lowerHeader.startsWith("api-key:") ||
                    lowerHeader.startsWith("apikey:")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        authParams.add(new HttpParameter("auth.api_key", parts[1].trim(), HttpParameter.Type.AUTH));
                    }
                }

                // Custom auth headers
                if (lowerHeader.startsWith("x-auth-") ||
                    lowerHeader.startsWith("x-token-") ||
                    lowerHeader.startsWith("x-session-")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        authParams.add(new HttpParameter("auth.custom." + name, value, HttpParameter.Type.AUTH));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing authentication parameters: " + e.getMessage());
        }

        return authParams;
    }

    /**
     * Parse structured header parameters (like Accept-Language, Cache-Control)
     */
    private List<HttpParameter> parseStructuredHeaderParameters(IRequestInfo requestInfo) {
        List<HttpParameter> structuredParams = new ArrayList<>();

        try {
            List<String> headers = requestInfo.getHeaders();

            for (String header : headers) {
                String lowerHeader = header.toLowerCase();

                // Parse Accept-Language header
                if (lowerHeader.startsWith("accept-language:")) {
                    String value = header.substring("accept-language:".length()).trim();
                    structuredParams.addAll(parseAcceptLanguageHeader(value));
                }

                // Parse Cache-Control header
                if (lowerHeader.startsWith("cache-control:")) {
                    String value = header.substring("cache-control:".length()).trim();
                    structuredParams.addAll(parseCacheControlHeader(value));
                }

                // Parse Accept header
                if (lowerHeader.startsWith("accept:")) {
                    String value = header.substring("accept:".length()).trim();
                    structuredParams.addAll(parseAcceptHeader(value));
                }

                // Parse custom structured headers (comma-separated values)
                if (lowerHeader.startsWith("x-") && header.contains(",")) {
                    String[] parts = header.split(":", 2);
                    if (parts.length == 2) {
                        String name = parts[0].trim();
                        String value = parts[1].trim();
                        structuredParams.addAll(parseCommaSeparatedHeader(name, value));
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing structured headers: " + e.getMessage());
        }

        return structuredParams;
    }

    /**
     * Parse Accept-Language header values
     */
    private List<HttpParameter> parseAcceptLanguageHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] languages = value.split(",");
        for (int i = 0; i < languages.length; i++) {
            String lang = languages[i].trim();
            params.add(new HttpParameter("accept_language[" + i + "]", lang, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse quality values
            if (lang.contains(";q=")) {
                String[] parts = lang.split(";q=");
                if (parts.length == 2) {
                    params.add(new HttpParameter("accept_language.lang[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                    params.add(new HttpParameter("accept_language.quality[" + i + "]", parts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                }
            }
        }

        return params;
    }

    /**
     * Parse Cache-Control header values
     */
    private List<HttpParameter> parseCacheControlHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] directives = value.split(",");
        for (int i = 0; i < directives.length; i++) {
            String directive = directives[i].trim();
            params.add(new HttpParameter("cache_control[" + i + "]", directive, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse directive values
            if (directive.contains("=")) {
                String[] parts = directive.split("=", 2);
                if (parts.length == 2) {
                    params.add(new HttpParameter("cache_control.directive[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                    params.add(new HttpParameter("cache_control.value[" + i + "]", parts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                }
            }
        }

        return params;
    }

    /**
     * Parse Accept header values
     */
    private List<HttpParameter> parseAcceptHeader(String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] mediaTypes = value.split(",");
        for (int i = 0; i < mediaTypes.length; i++) {
            String mediaType = mediaTypes[i].trim();
            params.add(new HttpParameter("accept[" + i + "]", mediaType, HttpParameter.Type.STRUCTURED_HEADER));

            // Parse media type and parameters
            if (mediaType.contains(";")) {
                String[] parts = mediaType.split(";");
                params.add(new HttpParameter("accept.media_type[" + i + "]", parts[0].trim(), HttpParameter.Type.STRUCTURED_HEADER));

                for (int j = 1; j < parts.length; j++) {
                    String param = parts[j].trim();
                    if (param.contains("=")) {
                        String[] paramParts = param.split("=", 2);
                        if (paramParts.length == 2) {
                            params.add(new HttpParameter("accept.param[" + i + "][" + j + "]", paramParts[1].trim(), HttpParameter.Type.STRUCTURED_HEADER));
                        }
                    }
                }
            }
        }

        return params;
    }

    /**
     * Parse comma-separated header values
     */
    private List<HttpParameter> parseCommaSeparatedHeader(String headerName, String value) {
        List<HttpParameter> params = new ArrayList<>();

        String[] values = value.split(",");
        for (int i = 0; i < values.length; i++) {
            String val = values[i].trim();
            params.add(new HttpParameter(headerName + "[" + i + "]", val, HttpParameter.Type.STRUCTURED_HEADER));
        }

        return params;
    }

    /**
     * Parse nested URL-encoded parameters (like param[key]=value)
     */
    private List<HttpParameter> parseNestedUrlEncodedParameters(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> nestedParams = new ArrayList<>();

        try {
            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

            if (body.isEmpty()) {
                return nestedParams;
            }

            // Look for nested parameter patterns
            Pattern nestedPattern = Pattern.compile("([^&=]+)\\[([^\\]]+)\\]=([^&]*)");
            Matcher matcher = nestedPattern.matcher(body);

            while (matcher.find()) {
                String paramName = matcher.group(1);
                String key = matcher.group(2);
                String value = matcher.group(3);

                // Decode URL-encoded values
                try {
                    paramName = java.net.URLDecoder.decode(paramName, "UTF-8");
                    key = java.net.URLDecoder.decode(key, "UTF-8");
                    value = java.net.URLDecoder.decode(value, "UTF-8");
                } catch (Exception e) {
                    // Keep original values if decoding fails
                }

                nestedParams.add(new HttpParameter(paramName + "." + key, value, HttpParameter.Type.NESTED_FORM));
                nestedParams.add(new HttpParameter(paramName + "[" + key + "]", value, HttpParameter.Type.NESTED_FORM));
            }

        } catch (Exception e) {
            System.out.println("Error parsing nested URL-encoded parameters: " + e.getMessage());
        }

        return nestedParams;
    }

    /**
     * Parse form data with file uploads
     */
    private List<HttpParameter> parseFormDataWithFiles(byte[] request, IRequestInfo requestInfo) {
        List<HttpParameter> fileParams = new ArrayList<>();

        try {
            if (!isMultipartRequest(requestInfo)) {
                return fileParams;
            }

            // Get request body
            int bodyOffset = requestInfo.getBodyOffset();
            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

            // Find boundary
            String contentType = getContentType(requestInfo);
            if (contentType == null || !contentType.contains("boundary=")) {
                return fileParams;
            }

            String boundary = contentType.substring(contentType.indexOf("boundary=") + 9);
            if (boundary.contains(";")) {
                boundary = boundary.substring(0, boundary.indexOf(";"));
            }

            // Split by boundary
            String[] parts = body.split("--" + boundary);

            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                if (part.isEmpty() || part.equals("--")) continue;

                // Parse multipart section
                if (part.contains("Content-Disposition:")) {
                    fileParams.addAll(parseMultipartSection(part, i));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing form data with files: " + e.getMessage());
        }

        return fileParams;
    }

    /**
     * Parse individual multipart section
     */
    private List<HttpParameter> parseMultipartSection(String section, int index) {
        List<HttpParameter> sectionParams = new ArrayList<>();

        try {
            String[] lines = section.split("\r?\n");
            String name = null;
            String filename = null;
            String contentType = null;
            StringBuilder content = new StringBuilder();
            boolean inContent = false;

            for (String line : lines) {
                if (line.startsWith("Content-Disposition:")) {
                    // Parse name and filename
                    if (line.contains("name=\"")) {
                        int start = line.indexOf("name=\"") + 6;
                        int end = line.indexOf("\"", start);
                        if (end > start) {
                            name = line.substring(start, end);
                        }
                    }

                    if (line.contains("filename=\"")) {
                        int start = line.indexOf("filename=\"") + 10;
                        int end = line.indexOf("\"", start);
                        if (end > start) {
                            filename = line.substring(start, end);
                        }
                    }
                } else if (line.startsWith("Content-Type:")) {
                    contentType = line.substring("Content-Type:".length()).trim();
                } else if (line.isEmpty() && !inContent) {
                    inContent = true;
                } else if (inContent) {
                    if (content.length() > 0) content.append("\n");
                    content.append(line);
                }
            }

            if (name != null) {
                String value = content.toString();

                if (filename != null) {
                    // File upload
                    sectionParams.add(new HttpParameter("file." + name, value, HttpParameter.Type.FILE_UPLOAD));
                    sectionParams.add(new HttpParameter("file." + name + ".filename", filename, HttpParameter.Type.FILE_UPLOAD));
                    if (contentType != null) {
                        sectionParams.add(new HttpParameter("file." + name + ".content_type", contentType, HttpParameter.Type.FILE_UPLOAD));
                    }
                } else {
                    // Regular form field
                    sectionParams.add(new HttpParameter("form." + name, value, HttpParameter.Type.MULTIPART));
                }
            }

        } catch (Exception e) {
            System.out.println("Error parsing multipart section: " + e.getMessage());
        }

        return sectionParams;
    }



    /**
     * Extract field value from JSON-RPC or similar structured JSON
     */
    private String extractJsonRpcField(String json, String fieldName) {
        try {
            Pattern pattern = Pattern.compile("\"" + fieldName + "\"\\s*:\\s*([^,}\\]]+|\"[^\"]*\"|\\{[^}]*\\}|\\[[^\\]]*\\])");
            Matcher matcher = pattern.matcher(json);

            if (matcher.find()) {
                String value = matcher.group(1).trim();

                // Remove quotes from string values
                if (value.startsWith("\"") && value.endsWith("\"")) {
                    value = value.substring(1, value.length() - 1);
                }

                return value;
            }
        } catch (Exception e) {
            System.out.println("Error extracting JSON-RPC field " + fieldName + ": " + e.getMessage());
        }

        return "";
    }

    /**
     * Apply advanced parameter filtering
     */
    private List<HttpParameter> applyAdvancedParameterFiltering(List<HttpParameter> parameters) {
        List<HttpParameter> filtered = new ArrayList<>();

        try {
            for (HttpParameter param : parameters) {
                // Skip empty parameters
                if (param.getName() == null || param.getName().trim().isEmpty()) {
                    continue;
                }

                // Skip parameters with very long names (likely corrupted)
                if (param.getName().length() > 200) {
                    continue;
                }

                // Skip parameters with only whitespace values
                if (param.getValue() != null && param.getValue().trim().isEmpty() && param.getValue().length() > 0) {
                    continue;
                }

                // Filter out common noise parameters
                if (isNoiseParameter(param)) {
                    continue;
                }

                // Filter out parameters with binary/non-printable content in names
                if (containsNonPrintableChars(param.getName())) {
                    continue;
                }

                // Apply value-based filtering
                if (shouldFilterByValue(param)) {
                    continue;
                }

                filtered.add(param);
            }

            System.out.println("Parameter filtering: " + parameters.size() + " -> " + filtered.size());

        } catch (Exception e) {
            System.out.println("Error in parameter filtering: " + e.getMessage());
            return parameters; // Return original list if filtering fails
        }

        return filtered;
    }

    /**
     * Categorize parameters by type and purpose
     */
    private List<HttpParameter> categorizeParameters(List<HttpParameter> parameters) {
        List<HttpParameter> categorized = new ArrayList<>();

        try {
            for (HttpParameter param : parameters) {
                HttpParameter categorizedParam = addParameterCategory(param);
                categorized.add(categorizedParam);
            }

        } catch (Exception e) {
            System.out.println("Error in parameter categorization: " + e.getMessage());
            return parameters;
        }

        return categorized;
    }

    /**
     * Filter system and duplicate parameters
     */
    private List<HttpParameter> filterSystemAndDuplicateParameters(List<HttpParameter> parameters) {
        List<HttpParameter> filtered = new ArrayList<>();
        Map<String, HttpParameter> uniqueParams = new HashMap<>();

        try {
            for (HttpParameter param : parameters) {
                // Skip system parameters
                if (isSystemParameter(param)) {
                    continue;
                }

                // Handle duplicates - keep the first occurrence or the one with more content
                String key = param.getName() + "|" + param.getType();

                if (!uniqueParams.containsKey(key)) {
                    uniqueParams.put(key, param);
                } else {
                    HttpParameter existing = uniqueParams.get(key);
                    // Keep the parameter with more content
                    if (param.getValue() != null &&
                        (existing.getValue() == null || param.getValue().length() > existing.getValue().length())) {
                        uniqueParams.put(key, param);
                    }
                }
            }

            filtered.addAll(uniqueParams.values());

            System.out.println("Duplicate filtering: " + parameters.size() + " -> " + filtered.size());

        } catch (Exception e) {
            System.out.println("Error in duplicate filtering: " + e.getMessage());
            return parameters;
        }

        return filtered;
    }

    /**
     * Check if parameter is noise/unwanted
     */
    private boolean isNoiseParameter(HttpParameter param) {
        String name = param.getName().toLowerCase();
        String value = param.getValue() != null ? param.getValue().toLowerCase() : "";

        // Common noise parameter patterns
        String[] noisePatterns = {
            "__extracted_",
            "__body__",
            "__header__content-length",
            "__header__host",
            "__header__user-agent",
            "__header__accept",
            "__header__accept-encoding",
            "__header__accept-language",
            "__header__connection",
            "__header__cache-control",
            "__header__pragma",
            "__header__upgrade-insecure-requests",
            "csrf_token",
            "_token",
            "authenticity_token"
        };

        for (String pattern : noisePatterns) {
            if (name.contains(pattern)) {
                return true;
            }
        }

        // Skip parameters with very short values that are likely not useful
        if (value.length() == 1 && (value.equals("0") || value.equals("1") || value.equals(""))) {
            return true;
        }

        return false;
    }

    /**
     * Check if parameter name contains non-printable characters
     */
    private boolean containsNonPrintableChars(String str) {
        if (str == null) return false;

        for (char c : str.toCharArray()) {
            if (c < 32 || c > 126) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if parameter should be filtered by value
     */
    private boolean shouldFilterByValue(HttpParameter param) {
        String value = param.getValue();
        if (value == null) return false;

        // Filter out very long values that are likely binary or encoded data
        if (value.length() > 10000) {
            return true;
        }

        // Filter out values that are mostly non-printable
        int nonPrintableCount = 0;
        for (char c : value.toCharArray()) {
            if (c < 32 || c > 126) {
                nonPrintableCount++;
            }
        }

        // If more than 50% non-printable, filter out
        return nonPrintableCount > value.length() * 0.5;
    }

    /**
     * Add category information to parameter
     */
    private HttpParameter addParameterCategory(HttpParameter param) {
        String name = param.getName().toLowerCase();
        String value = param.getValue() != null ? param.getValue() : "";

        // Determine parameter category
        String category = "GENERAL";

        if (name.contains("id") || name.contains("key") || name.matches(".*\\d+.*")) {
            category = "IDENTIFIER";
        } else if (name.contains("password") || name.contains("pwd") || name.contains("pass")) {
            category = "CREDENTIAL";
        } else if (name.contains("email") || name.contains("mail")) {
            category = "EMAIL";
        } else if (name.contains("url") || name.contains("link") || name.contains("href")) {
            category = "URL";
        } else if (name.contains("file") || name.contains("upload") || name.contains("attachment")) {
            category = "FILE";
        } else if (name.contains("date") || name.contains("time") || name.contains("timestamp")) {
            category = "DATETIME";
        } else if (name.contains("token") || name.contains("csrf") || name.contains("auth")) {
            category = "SECURITY";
        } else if (value.matches("\\d+")) {
            category = "NUMERIC";
        } else if (value.startsWith("{") || value.startsWith("[")) {
            category = "STRUCTURED";
        }

        // Create new parameter with category information
        HttpParameter categorizedParam = new HttpParameter(
            param.getName() + "__CATEGORY__" + category,
            param.getValue(),
            param.getType()
        );

        return categorizedParam;
    }

    /**
     * Check if parameter is a system parameter
     */
    private boolean isSystemParameter(HttpParameter param) {
        String name = param.getName().toLowerCase();

        // System parameter patterns to exclude
        String[] systemPatterns = {
            "__extracted_",
            "__body__",
            "__category__",
            "content-length",
            "content-type",
            "host",
            "user-agent",
            "accept",
            "accept-encoding",
            "accept-language",
            "connection",
            "cache-control"
        };

        for (String pattern : systemPatterns) {
            if (name.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate and sanitize parameters for injection testing
     */
    private List<HttpParameter> validateAndSanitizeParameters(List<HttpParameter> parameters) {
        List<HttpParameter> validatedParams = new ArrayList<>();

        try {
            for (HttpParameter param : parameters) {
                HttpParameter validatedParam = validateParameter(param);
                if (validatedParam != null) {
                    validatedParam = sanitizeParameter(validatedParam);
                    if (isSuitableForInjectionTesting(validatedParam)) {
                        validatedParams.add(validatedParam);
                    }
                }
            }

            System.out.println("Parameter validation: " + parameters.size() + " -> " + validatedParams.size());

        } catch (Exception e) {
            System.out.println("Error in parameter validation: " + e.getMessage());
            return parameters; // Return original list if validation fails
        }

        return validatedParams;
    }

    /**
     * Validate individual parameter
     */
    private HttpParameter validateParameter(HttpParameter param) {
        try {
            // Check for null or empty parameter name
            if (param.getName() == null || param.getName().trim().isEmpty()) {
                return null;
            }

            // Check parameter name length (reasonable limits)
            if (param.getName().length() > 500) {
                return null;
            }

            // Check for malformed parameter names
            if (containsMalformedChars(param.getName())) {
                return null;
            }

            // Validate parameter value
            String value = param.getValue();
            if (value != null) {
                // Check value length (reasonable limits for injection testing)
                if (value.length() > 50000) {
                    // Truncate very long values
                    value = value.substring(0, 50000) + "...[TRUNCATED]";
                }

                // Check for completely binary values
                if (isCompletelyBinary(value)) {
                    return null;
                }
            }

            // Create validated parameter
            return new HttpParameter(param.getName(), value, param.getType());

        } catch (Exception e) {
            System.out.println("Error validating parameter " + param.getName() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Sanitize parameter for injection testing
     */
    private HttpParameter sanitizeParameter(HttpParameter param) {
        try {
            String name = param.getName();
            String value = param.getValue();

            // Sanitize parameter name
            name = sanitizeParameterName(name);

            // Sanitize parameter value
            if (value != null) {
                value = sanitizeParameterValue(value);
            }

            return new HttpParameter(name, value, param.getType());

        } catch (Exception e) {
            System.out.println("Error sanitizing parameter " + param.getName() + ": " + e.getMessage());
            return param; // Return original if sanitization fails
        }
    }

    /**
     * Check if parameter is suitable for injection testing
     */
    private boolean isSuitableForInjectionTesting(HttpParameter param) {
        try {
            String name = param.getName();
            String value = param.getValue();

            // Skip parameters with names that indicate they shouldn't be tested
            String[] skipPatterns = {
                "csrf", "token", "_token", "authenticity_token",
                "nonce", "_nonce", "state", "_state",
                "signature", "hash", "checksum",
                "timestamp", "_timestamp", "time",
                "session", "sessionid", "jsessionid",
                "viewstate", "__viewstate", "__eventvalidation"
            };

            String lowerName = name.toLowerCase();
            for (String pattern : skipPatterns) {
                if (lowerName.contains(pattern)) {
                    return false;
                }
            }

            // Skip parameters with values that look like tokens or hashes
            if (value != null) {
                // Skip long hex strings (likely hashes)
                if (value.matches("^[a-fA-F0-9]{32,}$")) {
                    return false;
                }

                // Skip JWT tokens
                if (value.matches("^[A-Za-z0-9_-]+\\.[A-Za-z0-9_-]+\\.[A-Za-z0-9_-]+$")) {
                    return false;
                }

                // Skip very long base64-like strings
                if (value.length() > 100 && value.matches("^[A-Za-z0-9+/=]+$")) {
                    return false;
                }
            }

            // Parameter is suitable for testing
            return true;

        } catch (Exception e) {
            System.out.println("Error checking parameter suitability: " + e.getMessage());
            return true; // Default to suitable if check fails
        }
    }

    /**
     * Check for malformed characters in parameter name
     */
    private boolean containsMalformedChars(String name) {
        try {
            // Check for control characters (except common ones like tab, newline)
            for (char c : name.toCharArray()) {
                if (Character.isISOControl(c) && c != '\t' && c != '\n' && c != '\r') {
                    return true;
                }
            }

            // Check for suspicious character sequences
            if (name.contains("\u0000") || name.contains("\uFFFD")) {
                return true;
            }

            return false;

        } catch (Exception e) {
            return true; // Consider malformed if check fails
        }
    }

    /**
     * Check if value is completely binary (no printable characters)
     */
    private boolean isCompletelyBinary(String value) {
        try {
            if (value == null || value.isEmpty()) {
                return false;
            }

            int printableCount = 0;
            for (char c : value.toCharArray()) {
                if (c >= 32 && c <= 126) {
                    printableCount++;
                }
            }

            // If less than 10% printable characters, consider it binary
            return (double) printableCount / value.length() < 0.1;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Sanitize parameter name
     */
    private String sanitizeParameterName(String name) {
        try {
            // Remove null bytes and other control characters
            name = name.replaceAll("[\u0000-\u001F\u007F]", "");

            // Normalize whitespace
            name = name.replaceAll("\\s+", "_");

            // Remove leading/trailing special characters
            name = name.replaceAll("^[^a-zA-Z0-9_]+|[^a-zA-Z0-9_]+$", "");

            // Ensure name is not empty after sanitization
            if (name.isEmpty()) {
                name = "sanitized_param";
            }

            return name;

        } catch (Exception e) {
            return "sanitized_param";
        }
    }

    /**
     * Sanitize parameter value
     */
    private String sanitizeParameterValue(String value) {
        try {
            if (value == null) {
                return null;
            }

            // Remove null bytes
            value = value.replace("\u0000", "");

            // Replace other problematic control characters with spaces
            value = value.replaceAll("[\u0001-\u0008\u000B\u000C\u000E-\u001F\u007F]", " ");

            // Normalize excessive whitespace
            value = value.replaceAll("\\s{10,}", " [WHITESPACE] ");

            // Handle very long repeated characters
            value = value.replaceAll("(.)(\\1{50,})", "$1[REPEATED]");

            return value;

        } catch (Exception e) {
            return value; // Return original if sanitization fails
        }
    }
}