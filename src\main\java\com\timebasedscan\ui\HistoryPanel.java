package com.timebasedscan.ui;

import com.timebasedscan.model.HistoryEntry;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.model.VulnerabilityReport;
import com.timebasedscan.model.HttpParameter;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IMessageEditor;
import burp.IMessageEditorController;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.table.AbstractTableModel;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Panel for displaying scan history and individual request tracking
 */
public class HistoryPanel extends JPanel implements IMessageEditorController {
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final HistoryTableModel tableModel;
    private final JTable historyTable;
    private final IMessageEditor requestViewer;
    private final JLabel statusLabel;
    private final JComboBox<String> filterComboBox;
    private final JButton clearHistoryButton;
    private final JEditorPane detailsPane;

    // Request tracking components
    private JTable requestTable;
    private DefaultTableModel requestTableModel;
    private IMessageEditor requestDetailViewer;
    private IMessageEditor responseDetailViewer;
    private final List<RequestEntry> requestEntries;
    private RequestEntry currentlyDisplayedRequest;
    
    public HistoryPanel(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.requestEntries = new ArrayList<>();
        setLayout(new BorderLayout());
        
        // Create top panel with attribution and controls using themed header design
        JPanel topPanel = UITheme.createHeaderPanel();
        
        // Create attribution label with consistent theme styling
        JLabel attributionLabel = UITheme.createAttributionLabel();
        attributionLabel.setHorizontalAlignment(JLabel.RIGHT);
        attributionLabel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 10));
        
        // Add attribution to the east position of a dedicated panel
        JPanel attributionPanel = new JPanel(new BorderLayout());
        attributionPanel.setOpaque(false);
        attributionPanel.add(attributionLabel, BorderLayout.EAST);
        topPanel.add(attributionPanel, BorderLayout.NORTH);
        
        // Create table model and table
        tableModel = new HistoryTableModel();
        historyTable = new JTable(tableModel);
        historyTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        historyTable.setAutoCreateRowSorter(true);

        // Enhanced table styling
        historyTable.setRowHeight(28); // Taller rows for better readability
        historyTable.setShowGrid(true);
        historyTable.setGridColor(new Color(230, 230, 230));
        historyTable.getTableHeader().setReorderingAllowed(false);
        historyTable.setFont(new Font("SansSerif", Font.PLAIN, 12));

        // Set column widths
        historyTable.getColumnModel().getColumn(0).setPreferredWidth(250); // URL
        historyTable.getColumnModel().getColumn(1).setPreferredWidth(60);  // Method
        historyTable.getColumnModel().getColumn(2).setPreferredWidth(130); // Timestamp
        historyTable.getColumnModel().getColumn(3).setPreferredWidth(80);  // Params
        historyTable.getColumnModel().getColumn(4).setPreferredWidth(80);  // Vulnerable
        historyTable.getColumnModel().getColumn(5).setPreferredWidth(80);  // SQL Errors
        historyTable.getColumnModel().getColumn(6).setPreferredWidth(80);  // Duration

        // Add colorful cell renderers
        historyTable.setDefaultRenderer(Object.class, new ColorfulHistoryCellRenderer());
        historyTable.getColumnModel().getColumn(4).setCellRenderer(new VulnerableCountCellRenderer());
        historyTable.getColumnModel().getColumn(5).setCellRenderer(new SqlErrorCountCellRenderer());
        historyTable.getColumnModel().getColumn(6).setCellRenderer(new StatusCellRenderer());

        // Set up sorting
        TableRowSorter<HistoryTableModel> sorter = new TableRowSorter<>(tableModel);
        historyTable.setRowSorter(sorter);

        // Create request viewer BEFORE creating tabs
        requestViewer = callbacks.createMessageEditor(null, false);

        // Create details pane for parameters
        detailsPane = new JEditorPane();
        detailsPane.setContentType("text/html");
        detailsPane.setEditable(false);
        detailsPane.setText("<html><body><div style='font-family: Arial, sans-serif; padding: 10px;'>Select a scan to view details</div></body></html>");
        
        // Create filter control panel with theme styling
        JPanel controlPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        controlPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        controlPanel.setBackground(UITheme.BACKGROUND_MEDIUM);
        
        // Create filter dropdown
        JLabel filterLabel = new JLabel("Filter by: ");
        filterComboBox = new JComboBox<>(new String[] {
            "All Scans",
            "Last Hour",
            "Today",
            "With Vulnerabilities",
            "No Vulnerabilities",
            "With SQL Errors",
            "No SQL Errors"
        });
        
        filterComboBox.addActionListener(e -> applyFilter());
        
        // Create clear history button with enhanced styling
        clearHistoryButton = UITheme.createSecondaryButton("Clear History");
        clearHistoryButton.setToolTipText("Clear all scan history");
        
        clearHistoryButton.addActionListener(e -> {
            int confirm = JOptionPane.showConfirmDialog(
                this,
                "Are you sure you want to clear scan history?",
                "Confirm Clear History",
                JOptionPane.YES_NO_OPTION
            );
            
            if (confirm == JOptionPane.YES_OPTION) {
                clearHistory();
            }
        });
        
        // Create report button with enhanced styling
        JButton reportButton = UITheme.createPrimaryButton("Generate Vulnerability Report");
        reportButton.setToolTipText("Generate a detailed vulnerability report for the selected scan");
        reportButton.setFont(UITheme.HEADER_FONT);
        reportButton.addActionListener(e -> generateVulnerabilityReport());
        
        // Add components to control panel
        controlPanel.add(filterLabel);
        controlPanel.add(filterComboBox);
        controlPanel.add(Box.createHorizontalStrut(20));
        controlPanel.add(reportButton);
        controlPanel.add(Box.createHorizontalStrut(10));
        controlPanel.add(clearHistoryButton);
        
        // Create status panel
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Status label
        statusLabel = new JLabel("No history entries");
        statusPanel.add(statusLabel, BorderLayout.WEST);
        
        // Set up table selection listener
        historyTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = historyTable.getSelectedRow();
                if (row != -1) {
                    row = historyTable.convertRowIndexToModel(row);
                    HistoryEntry entry = tableModel.getHistoryEntryAt(row);
                    if (entry != null) {
                        requestViewer.setMessage(entry.getRequest(), true);
                        updateParameterDetails(entry);
                    }
                }
            }
        });

        // Add controlPanel to the topPanel
        topPanel.add(controlPanel, BorderLayout.CENTER);

        // Create tabbed pane for scan history and request tracking AFTER all components are initialized
        JTabbedPane tabbedPane = new JTabbedPane();

        // Scan History Tab
        try {
            JPanel scanHistoryPanel = createScanHistoryTab();
            tabbedPane.addTab("📊 Scan History", scanHistoryPanel);
        } catch (Exception e) {
            callbacks.printError("Error creating scan history tab: " + e.getMessage());
            e.printStackTrace();
            // Add a simple fallback panel
            JPanel fallbackPanel = new JPanel();
            fallbackPanel.add(new JLabel("Error loading scan history tab"));
            tabbedPane.addTab("📊 Scan History", fallbackPanel);
        }

        // Request Tracking Tab
        try {
            JPanel requestTrackingPanel = createRequestTrackingTab();
            tabbedPane.addTab("🔍 Request Tracking", requestTrackingPanel);
        } catch (Exception e) {
            callbacks.printError("Error creating request tracking tab: " + e.getMessage());
            e.printStackTrace();
            // Add a simple fallback panel
            JPanel fallbackPanel = new JPanel();
            fallbackPanel.add(new JLabel("Error loading request tracking tab"));
            tabbedPane.addTab("🔍 Request Tracking", fallbackPanel);
        }

        // Add components to main panel
        add(topPanel, BorderLayout.NORTH);
        add(tabbedPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * Add a scan to the history
     */
    public void addHistoryEntry(HistoryEntry entry) {
        SwingUtilities.invokeLater(() -> {
            tableModel.addHistoryEntry(entry);
            updateStatusLabel();
        });
    }
    
    /**
     * Clear all history entries
     */
    public void clearHistory() {
        SwingUtilities.invokeLater(() -> {
            tableModel.clearHistory();
            requestViewer.setMessage(new byte[0], true);
            detailsPane.setText("<html><body><div style='font-family: Arial, sans-serif; padding: 10px;'>Select a scan to view details</div></body></html>");
            updateStatusLabel();
        });
    }
    
    /**
     * Apply filter based on selected option
     */
    private void applyFilter() {
        SwingUtilities.invokeLater(() -> {
            String filter = (String) filterComboBox.getSelectedItem();
            if (filter == null) {
                return;
            }
            
            RowFilter<HistoryTableModel, Integer> rowFilter = null;
            
            switch (filter) {
                case "All Scans":
                    // No filter needed
                    break;
                case "Last Hour":
                    long hourAgo = System.currentTimeMillis() - (60 * 60 * 1000);
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getTimestamp() >= hourAgo;
                        }
                    };
                    break;
                case "Today":
                    // Start of today
                    java.util.Calendar calendar = java.util.Calendar.getInstance();
                    calendar.set(java.util.Calendar.HOUR_OF_DAY, 0);
                    calendar.set(java.util.Calendar.MINUTE, 0);
                    calendar.set(java.util.Calendar.SECOND, 0);
                    long startOfDay = calendar.getTimeInMillis();
                    
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getTimestamp() >= startOfDay;
                        }
                    };
                    break;
                case "With Vulnerabilities":
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getVulnerableCount() > 0;
                        }
                    };
                    break;
                case "No Vulnerabilities":
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getVulnerableCount() == 0;
                        }
                    };
                    break;
                case "With SQL Errors":
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getSqlErrorCount() > 0;
                        }
                    };
                    break;
                case "No SQL Errors":
                    rowFilter = new RowFilter<HistoryTableModel, Integer>() {
                        @Override
                        public boolean include(Entry<? extends HistoryTableModel, ? extends Integer> entry) {
                            HistoryEntry historyEntry = tableModel.getHistoryEntryAt(entry.getIdentifier());
                            return historyEntry.getSqlErrorCount() == 0;
                        }
                    };
                    break;
            }
            
            TableRowSorter<HistoryTableModel> sorter = (TableRowSorter<HistoryTableModel>) historyTable.getRowSorter();
            sorter.setRowFilter(rowFilter);
            updateStatusLabel();
        });
    }
    
    /**
     * Update the parameter details display
     */
    private void updateParameterDetails(HistoryEntry entry) {
        StringBuilder html = new StringBuilder();
        html.append("<html><body style='font-family: Arial, sans-serif; margin: 10px;'>");
        
        // Add scan details section
        html.append("<div style='background-color: #f5f5f5; padding: 10px; border-radius: 5px; margin-bottom: 10px;'>");
        html.append("<h3 style='margin-top: 0; color: #0066cc;'>Scan Details</h3>");
        html.append("<table style='width: 100%;'>");
        html.append("<tr><td><b>URL:</b></td><td>").append(entry.getUrl()).append("</td></tr>");
        html.append("<tr><td><b>Method:</b></td><td>").append(entry.getMethod()).append("</td></tr>");
        html.append("<tr><td><b>Timestamp:</b></td><td>").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(entry.getTimestamp()))).append("</td></tr>");
        html.append("<tr><td><b>Parameters Tested:</b></td><td>").append(entry.getParamCount()).append("</td></tr>");
        html.append("<tr><td><b>Vulnerable Parameters:</b></td><td");
        if (entry.getVulnerableCount() > 0) {
            html.append(" style='color: #cc0000; font-weight: bold;'");
        }
        html.append(">").append(entry.getVulnerableCount()).append("</td></tr>");
        html.append("<tr><td><b>SQL Errors Detected:</b></td><td");
        if (entry.getSqlErrorCount() > 0) {
            html.append(" style='color: #cc6600; font-weight: bold;'");
        }
        html.append(">").append(entry.getSqlErrorCount()).append("</td></tr>");
        html.append("</table>");
        html.append("</div>");
        
        // Add parameters section
        if (entry.getTestResults().isEmpty()) {
            html.append("<div style='padding: 10px;'><i>No parameters were tested in this scan.</i></div>");
        } else {
            // Vulnerable parameters first
            List<TestResult> vulnerable = new ArrayList<>();
            List<TestResult> notVulnerable = new ArrayList<>();
            
            for (TestResult result : entry.getTestResults()) {
                if (result.isVulnerable()) {
                    vulnerable.add(result);
                } else {
                    notVulnerable.add(result);
                }
            }
            
            if (!vulnerable.isEmpty()) {
                html.append("<div style='margin-top: 10px;'>");
                html.append("<h3 style='color: #cc0000;'>Vulnerable Parameters</h3>");
                html.append("<table style='width: 100%; border-collapse: collapse;'>");
                html.append("<tr style='background-color: #f5f5f5;'>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Parameter</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Type</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Payload</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Response Time</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Baseline</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Difference</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>SQL Error</th>");
                html.append("</tr>");
                
                for (TestResult result : vulnerable) {
                    html.append("<tr style='background-color: #fff0f0;'>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(result.getParameterName())).append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(result.getParameterType())).append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd; font-family: monospace; font-size: 12px; max-width: 300px; overflow-wrap: break-word;'>")
                        .append(escapeHtml(result.getParameterValue()))
                        .append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(result.getResponseTime()).append(" ms</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(result.getBaselineTime()).append(" ms</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(result.getTimeDifference()).append(" ms</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;");
                    if (result.isSqlErrorDetected()) {
                        html.append(" color: #cc6600; font-weight: bold;'>");
                        html.append("🚨 ").append(result.getSqlErrorType() != null ? result.getSqlErrorType() : "SQL Error");
                    } else {
                        html.append("'>No SQL Error");
                    }
                    html.append("</td>");
                    html.append("</tr>");
                    
                    // Add an expandable row with full payload details
                    html.append("<tr style='background-color: #fff8f8;'>");
                    html.append("<td colspan='7' style='padding: 0; border: 1px solid #ddd;'>");
                    html.append("<div style='padding: 10px; margin: 5px; background-color: #f9f2f2; border-radius: 5px;'>");
                    html.append("<div><strong>Successful Payload:</strong></div>");
                    html.append("<div style='font-family: monospace; background-color: #ffeeee; padding: 5px; border: 1px solid #ffcccc; border-radius: 4px; margin-top: 5px; overflow-x: auto;'>");
                    html.append(escapeHtml(result.getParameterValue()));
                    html.append("</div>");
                    
                    // Add SQL error details if available
                    if (result.isSqlErrorDetected()) {
                        html.append("<div style='margin-top: 8px;'><strong>SQL Error Details:</strong></div>");
                        html.append("<div style='background-color: #fff0e6; padding: 8px; border: 1px solid #ffcc99; border-radius: 4px; margin-top: 5px;'>");
                        html.append("<div><strong>Database Type:</strong> ").append(result.getSqlErrorType() != null ? result.getSqlErrorType() : "Unknown").append("</div>");
                        if (result.getSqlErrorMessage() != null && !result.getSqlErrorMessage().isEmpty()) {
                            html.append("<div style='margin-top: 5px;'><strong>Error Message:</strong></div>");
                            html.append("<div style='font-family: monospace; font-size: 11px; background-color: #ffeeee; padding: 5px; border: 1px solid #ffcccc; border-radius: 4px; margin-top: 3px; overflow-x: auto;'>");
                            html.append(escapeHtml(result.getSqlErrorMessage()));
                            html.append("</div>");
                        }
                        html.append("</div>");
                    }

                    // Add evidence if available
                    if (result.getEvidence() != null && !result.getEvidence().isEmpty()) {
                        html.append("<div style='margin-top: 8px;'><strong>Evidence:</strong></div>");
                        html.append("<div style='font-family: monospace; font-size: 11px; background-color: #f5f5f5; padding: 5px; border: 1px solid #ddd; border-radius: 4px; margin-top: 5px; overflow-x: auto;'>");
                        html.append(escapeHtml(result.getEvidence().replace("\n", "<br/>")));
                        html.append("</div>");
                    }
                    
                    html.append("</div>");
                    html.append("</td>");
                    html.append("</tr>");
                }
                
                html.append("</table>");
                html.append("</div>");
            }
            
            if (!notVulnerable.isEmpty()) {
                html.append("<div style='margin-top: 15px;'>");
                html.append("<h3 style='color: #0066cc;'>Other Tested Parameters</h3>");
                html.append("<table style='width: 100%; border-collapse: collapse;'>");
                html.append("<tr style='background-color: #f5f5f5;'>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Parameter</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Type</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Value</th>");
                html.append("<th style='text-align: left; padding: 5px; border: 1px solid #ddd;'>Result</th>");
                html.append("</tr>");
                
                // Show only first 15 non-vulnerable parameters to prevent overly long display
                int limit = Math.min(15, notVulnerable.size());
                for (int i = 0; i < limit; i++) {
                    TestResult result = notVulnerable.get(i);
                    html.append("<tr>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(result.getParameterName())).append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(result.getParameterType())).append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(truncateValue(result.getParameterValue(), 30))).append("</td>");
                    html.append("<td style='padding: 5px; border: 1px solid #ddd;'>").append(escapeHtml(result.getResult())).append("</td>");
                    html.append("</tr>");
                }
                
                if (notVulnerable.size() > limit) {
                    html.append("<tr><td colspan='4' style='padding: 5px; border: 1px solid #ddd; text-align: center;'><i>").append(notVulnerable.size() - limit).append(" more parameters not shown...</i></td></tr>");
                }
                
                html.append("</table>");
                html.append("</div>");
            }
        }
        
        html.append("</body></html>");
        detailsPane.setText(html.toString());
        
        // Scroll to top
        detailsPane.setCaretPosition(0);
    }
    
    /**
     * Generates a detailed vulnerability report for the selected scan
     */
    private void generateVulnerabilityReport() {
        int selectedRow = historyTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(
                this,
                "Please select a scan from the history table to generate a report.",
                "No Scan Selected",
                JOptionPane.INFORMATION_MESSAGE
            );
            return;
        }
        
        // Get the selected history entry
        int modelRow = historyTable.convertRowIndexToModel(selectedRow);
        HistoryEntry entry = tableModel.getHistoryEntryAt(modelRow);
        
        if (entry == null) {
            JOptionPane.showMessageDialog(
                this,
                "Could not retrieve the selected scan data.",
                "Error",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }
        
        // Check if there are any vulnerable parameters
        List<TestResult> vulnerableResults = new ArrayList<>();
        for (TestResult result : entry.getTestResults()) {
            if (result.isVulnerable()) {
                vulnerableResults.add(result);
            }
        }
        
        if (vulnerableResults.isEmpty()) {
            JOptionPane.showMessageDialog(
                this,
                "The selected scan does not contain any vulnerable parameters.",
                "No Vulnerabilities",
                JOptionPane.INFORMATION_MESSAGE
            );
            return;
        }
        
        // Generate the report
        VulnerabilityReport report = new VulnerabilityReport(entry, vulnerableResults);
        String reportHtml = report.generateFullReport();
        
        // Display the report in the details pane
        detailsPane.setText(reportHtml);
        detailsPane.setCaretPosition(0);
        
        // Offer to save the report
        int saveChoice = JOptionPane.showConfirmDialog(
            this,
            "Vulnerability report generated. Would you like to save it as an HTML file?",
            "Save Report",
            JOptionPane.YES_NO_OPTION
        );
        
        if (saveChoice == JOptionPane.YES_OPTION) {
            try {
                // Create a meaningful filename
                String urlPart = entry.getUrl().replaceAll("[^a-zA-Z0-9.-]", "_");
                if (urlPart.length() > 30) {
                    urlPart = urlPart.substring(0, 30);
                }
                
                String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date(entry.getTimestamp()));
                String filename = "vuln_report_" + urlPart + "_" + timestamp + ".html";
                
                // Save the file using Burp's API and get a temporary file back
                callbacks.saveToTempFile(reportHtml.getBytes());
                
                JOptionPane.showMessageDialog(
                    this,
                    "Report has been saved as a temporary file in Burp Suite.\n" +
                    "You can export it from Burp's Temporary Files tab.",
                    "Report Saved",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } catch (Exception ex) {
                callbacks.printError("Error saving report: " + ex.getMessage());
                JOptionPane.showMessageDialog(
                    this,
                    "Error saving report: " + ex.getMessage(),
                    "Error",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }
    
    /**
     * Update the status label with count information
     */
    private void updateStatusLabel() {
        int total = tableModel.getRowCount();
        int visible = historyTable.getRowCount();
        
        if (total == 0) {
            statusLabel.setText("No history entries");
        } else if (total == visible) {
            statusLabel.setText(total + " history entries");
        } else {
            statusLabel.setText(visible + " of " + total + " history entries");
        }
    }
    
    /**
     * Truncate a string value for display
     */
    private String truncateValue(String value, int maxLength) {
        if (value == null) return "";
        if (value.length() <= maxLength) return value;
        return value.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * Escape HTML special characters
     */
    private String escapeHtml(String input) {
        if (input == null) {
            return "";
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }
    
    /**
     * Table model for history entries
     */
    private class HistoryTableModel extends AbstractTableModel {
        private final List<HistoryEntry> historyEntries = new ArrayList<>();
        private final String[] columnNames = {
            "URL", "Method", "Timestamp", "Parameters", "Vulnerable", "SQL Errors", "Duration (ms)"
        };
        
        /**
         * Add a history entry to the table
         */
        public void addHistoryEntry(HistoryEntry entry) {
            historyEntries.add(entry);
            fireTableRowsInserted(historyEntries.size() - 1, historyEntries.size() - 1);
        }
        
        /**
         * Clear all history entries
         */
        public void clearHistory() {
            historyEntries.clear();
            fireTableDataChanged();
        }
        
        /**
         * Get a history entry at a specific row
         */
        public HistoryEntry getHistoryEntryAt(int row) {
            if (row >= 0 && row < historyEntries.size()) {
                return historyEntries.get(row);
            }
            return null;
        }
        
        @Override
        public int getRowCount() {
            return historyEntries.size();
        }
        
        @Override
        public int getColumnCount() {
            return columnNames.length;
        }
        
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (rowIndex >= historyEntries.size()) {
                return null;
            }
            
            HistoryEntry entry = historyEntries.get(rowIndex);
            
            switch (columnIndex) {
                case 0: return truncateValue(entry.getUrl(), 50);
                case 1: return entry.getMethod();
                case 2: return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(entry.getTimestamp()));
                case 3: return entry.getParamCount();
                case 4: return entry.getVulnerableCount();
                case 5: return entry.getSqlErrorCount();
                case 6: return entry.getDuration();
                default: return null;
            }
        }
        
        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }
        
        @Override
        public Class<?> getColumnClass(int columnIndex) {
            switch (columnIndex) {
                case 3: return Integer.class;
                case 4: return Integer.class;
                case 5: return Integer.class;
                case 6: return Long.class;
                default: return String.class;
            }
        }
    }

    /**
     * Colorful cell renderer for history table
     */
    private class ColorfulHistoryCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (!isSelected) {
                // Get the history entry for this row
                int modelRow = table.convertRowIndexToModel(row);
                HistoryEntry entry = tableModel.getHistoryEntryAt(modelRow);

                if (entry != null) {
                    // Color based on vulnerability status
                    if (entry.getVulnerableCount() > 0) {
                        // Red tint for vulnerable scans
                        c.setBackground(new Color(255, 245, 245));
                        setForeground(new Color(139, 0, 0));
                    } else {
                        // Alternating row colors for non-vulnerable
                        if (row % 2 == 0) {
                            c.setBackground(new Color(248, 255, 248)); // Light green
                        } else {
                            c.setBackground(Color.WHITE);
                        }
                        setForeground(Color.BLACK);
                    }
                } else {
                    // Default alternating colors
                    if (row % 2 == 0) {
                        c.setBackground(new Color(248, 248, 248));
                    } else {
                        c.setBackground(Color.WHITE);
                    }
                    setForeground(Color.BLACK);
                }
            }

            // Add padding
            setBorder(BorderFactory.createEmptyBorder(4, 8, 4, 8));

            return c;
        }
    }

    /**
     * Special renderer for vulnerable count column
     */
    private class VulnerableCountCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (value instanceof Integer) {
                int count = (Integer) value;

                if (!isSelected) {
                    if (count > 0) {
                        // Red background for vulnerable parameters
                        c.setBackground(new Color(255, 230, 230));
                        setForeground(new Color(204, 0, 0));
                        setFont(getFont().deriveFont(Font.BOLD));
                        setText("⚠ " + count);
                    } else {
                        // Green background for safe parameters
                        c.setBackground(new Color(230, 255, 230));
                        setForeground(new Color(0, 128, 0));
                        setFont(getFont().deriveFont(Font.PLAIN));
                        setText("✓ " + count);
                    }
                }
            }

            setHorizontalAlignment(JLabel.CENTER);
            setBorder(BorderFactory.createEmptyBorder(4, 8, 4, 8));

            return c;
        }
    }

    /**
     * Special renderer for SQL error count column
     */
    private class SqlErrorCountCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (value instanceof Integer) {
                int count = (Integer) value;

                if (!isSelected) {
                    if (count > 0) {
                        // Orange background for SQL errors detected
                        c.setBackground(new Color(255, 245, 230));
                        setForeground(new Color(204, 102, 0));
                        setFont(getFont().deriveFont(Font.BOLD));
                        setText("🚨 " + count);
                        setToolTipText(count + " SQL error(s) detected - potential injection points");
                    } else {
                        // Light gray background for no SQL errors
                        c.setBackground(new Color(248, 248, 248));
                        setForeground(new Color(128, 128, 128));
                        setFont(getFont().deriveFont(Font.PLAIN));
                        setText("- " + count);
                        setToolTipText("No SQL errors detected");
                    }
                }
            }

            setHorizontalAlignment(JLabel.CENTER);
            setBorder(BorderFactory.createEmptyBorder(4, 8, 4, 8));

            return c;
        }
    }

    /**
     * Special renderer for status column
     */
    private class StatusCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected,
                boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (value instanceof Long) {
                long duration = (Long) value;

                if (!isSelected) {
                    // Color based on scan duration
                    if (duration < 5000) { // Less than 5 seconds
                        c.setBackground(new Color(230, 255, 230)); // Light green
                        setForeground(new Color(0, 128, 0));
                        setText("⚡ " + duration + "ms");
                    } else if (duration < 30000) { // Less than 30 seconds
                        c.setBackground(new Color(255, 255, 230)); // Light yellow
                        setForeground(new Color(204, 102, 0));
                        setText("⏱ " + duration + "ms");
                    } else { // More than 30 seconds
                        c.setBackground(new Color(255, 240, 230)); // Light orange
                        setForeground(new Color(204, 68, 0));
                        setText("🐌 " + duration + "ms");
                    }
                }
            }

            setHorizontalAlignment(JLabel.CENTER);
            setBorder(BorderFactory.createEmptyBorder(4, 8, 4, 8));

            return c;
        }
    }

    /**
     * Create the scan history tab
     */
    private JPanel createScanHistoryTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // Create split panes for the layout
        JSplitPane rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        rightSplitPane.setTopComponent(requestViewer.getComponent());
        rightSplitPane.setBottomComponent(new JScrollPane(detailsPane));
        rightSplitPane.setResizeWeight(0.7);

        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        mainSplitPane.setLeftComponent(new JScrollPane(historyTable));
        mainSplitPane.setRightComponent(rightSplitPane);
        mainSplitPane.setResizeWeight(0.5);

        panel.add(mainSplitPane, BorderLayout.CENTER);
        return panel;
    }

    /**
     * Create the request tracking tab
     */
    private JPanel createRequestTrackingTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // Create request table
        String[] requestColumns = {"#", "Time", "Method", "URL", "Parameter", "Payload", "Status", "Response Time", "Length"};
        requestTableModel = new DefaultTableModel(requestColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        requestTable = new JTable(requestTableModel);
        requestTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        requestTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);

        // Set column widths
        int[] columnWidths = {50, 80, 60, 200, 120, 150, 80, 100, 80};
        for (int i = 0; i < columnWidths.length && i < requestTable.getColumnCount(); i++) {
            requestTable.getColumnModel().getColumn(i).setPreferredWidth(columnWidths[i]);
        }

        // Add selection listener
        requestTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                displaySelectedRequest();
            }
        });

        // Create request/response viewers
        requestDetailViewer = callbacks.createMessageEditor(this, false);
        responseDetailViewer = callbacks.createMessageEditor(this, false);

        // Create split panes
        JSplitPane viewerSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);

        JPanel requestPanel = new JPanel(new BorderLayout());
        requestPanel.setBorder(BorderFactory.createTitledBorder("Request"));
        requestPanel.add(requestDetailViewer.getComponent(), BorderLayout.CENTER);

        JPanel responsePanel = new JPanel(new BorderLayout());
        responsePanel.setBorder(BorderFactory.createTitledBorder("Response"));
        responsePanel.add(responseDetailViewer.getComponent(), BorderLayout.CENTER);

        viewerSplitPane.setLeftComponent(requestPanel);
        viewerSplitPane.setRightComponent(responsePanel);
        viewerSplitPane.setResizeWeight(0.5);

        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        mainSplitPane.setTopComponent(new JScrollPane(requestTable));
        mainSplitPane.setBottomComponent(viewerSplitPane);
        mainSplitPane.setResizeWeight(0.4);

        // Control panel for request tracking
        JPanel requestControlPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        requestControlPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        JButton clearRequestsButton = UITheme.createSecondaryButton("🗑️ Clear Requests");
        clearRequestsButton.setToolTipText("Clear all tracked requests");
        clearRequestsButton.addActionListener(e -> clearRequestHistory());

        JLabel requestCountLabel = new JLabel("Requests tracked: 0");

        requestControlPanel.add(clearRequestsButton);
        requestControlPanel.add(Box.createHorizontalStrut(20));
        requestControlPanel.add(requestCountLabel);

        panel.add(requestControlPanel, BorderLayout.NORTH);
        panel.add(mainSplitPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Add a request/response to the tracking history
     */
    public void addRequestEntry(IHttpRequestResponse requestResponse, HttpParameter parameter,
                               String payload, long responseTime, String testType) {
        if (requestResponse == null) {
            return;
        }

        try {
            RequestEntry entry = new RequestEntry(
                requestEntries.size() + 1,
                new Date(),
                requestResponse,
                parameter,
                payload,
                responseTime,
                testType
            );

            requestEntries.add(entry);

            SwingUtilities.invokeLater(() -> {
                addRequestToTable(entry);
                updateRequestCountLabel();
            });

        } catch (Exception e) {
            callbacks.printError("Error adding request entry: " + e.getMessage());
        }
    }

    private void addRequestToTable(RequestEntry entry) {
        try {
            String timeStr = new java.text.SimpleDateFormat("HH:mm:ss").format(entry.timestamp);
            String url = entry.getUrl();
            String method = entry.getMethod();
            String parameterName = entry.parameter != null ? entry.parameter.getName() : "N/A";
            String payloadPreview = entry.payload != null ?
                (entry.payload.length() > 30 ? entry.payload.substring(0, 30) + "..." : entry.payload) : "N/A";
            String status = entry.getStatusCode();
            String responseTimeStr = entry.responseTime + "ms";
            String length = entry.getResponseLength() + " bytes";

            Object[] rowData = {
                entry.id,
                timeStr,
                method,
                url,
                parameterName,
                payloadPreview,
                status,
                responseTimeStr,
                length
            };

            requestTableModel.addRow(rowData);

            // Auto-scroll to the new entry
            int lastRow = requestTableModel.getRowCount() - 1;
            requestTable.scrollRectToVisible(requestTable.getCellRect(lastRow, 0, true));

        } catch (Exception e) {
            callbacks.printError("Error adding request to table: " + e.getMessage());
        }
    }

    private void displaySelectedRequest() {
        int selectedRow = requestTable.getSelectedRow();
        if (selectedRow >= 0 && selectedRow < requestEntries.size()) {
            currentlyDisplayedRequest = requestEntries.get(selectedRow);

            // Update viewers
            if (currentlyDisplayedRequest.requestResponse.getRequest() != null) {
                requestDetailViewer.setMessage(currentlyDisplayedRequest.requestResponse.getRequest(), true);
            }

            if (currentlyDisplayedRequest.requestResponse.getResponse() != null) {
                responseDetailViewer.setMessage(currentlyDisplayedRequest.requestResponse.getResponse(), false);
            }
        }
    }

    private void clearRequestHistory() {
        int result = JOptionPane.showConfirmDialog(
            this,
            "Are you sure you want to clear all tracked requests?",
            "Clear Request History",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );

        if (result == JOptionPane.YES_OPTION) {
            requestEntries.clear();
            requestTableModel.setRowCount(0);
            requestDetailViewer.setMessage(new byte[0], true);
            responseDetailViewer.setMessage(new byte[0], false);
            currentlyDisplayedRequest = null;
            updateRequestCountLabel();

            callbacks.printOutput("Request history cleared - " + new Date());
        }
    }

    private void updateRequestCountLabel() {
        // Update status in the main status label
        SwingUtilities.invokeLater(() -> {
            int requestCount = requestEntries.size();
            String baseStatus = statusLabel.getText().split(" \\| Requests tracked:")[0];
            if (requestCount > 0) {
                statusLabel.setText(baseStatus + " | Requests tracked: " + requestCount);
            } else {
                statusLabel.setText(baseStatus);
            }
        });
    }

    // IMessageEditorController implementation
    public IHttpRequestResponse getHttpRequestResponse() {
        return currentlyDisplayedRequest != null ? currentlyDisplayedRequest.requestResponse : null;
    }

    public byte[] getRequest() {
        return currentlyDisplayedRequest != null && currentlyDisplayedRequest.requestResponse != null ?
            currentlyDisplayedRequest.requestResponse.getRequest() : null;
    }

    public byte[] getResponse() {
        return currentlyDisplayedRequest != null && currentlyDisplayedRequest.requestResponse != null ?
            currentlyDisplayedRequest.requestResponse.getResponse() : null;
    }

    public IHttpService getHttpService() {
        return currentlyDisplayedRequest != null && currentlyDisplayedRequest.requestResponse != null ?
            currentlyDisplayedRequest.requestResponse.getHttpService() : null;
    }

    /**
     * Request entry data class for individual request tracking
     */
    private class RequestEntry {
        public final int id;
        public final Date timestamp;
        public final IHttpRequestResponse requestResponse;
        public final HttpParameter parameter;
        public final String payload;
        public final long responseTime;
        public final String testType;

        public RequestEntry(int id, Date timestamp, IHttpRequestResponse requestResponse,
                           HttpParameter parameter, String payload, long responseTime, String testType) {
            this.id = id;
            this.timestamp = timestamp;
            this.requestResponse = requestResponse;
            this.parameter = parameter;
            this.payload = payload;
            this.responseTime = responseTime;
            this.testType = testType;
        }

        public String getUrl() {
            try {
                if (requestResponse != null && requestResponse.getHttpService() != null) {
                    String protocol = requestResponse.getHttpService().getProtocol();
                    String host = requestResponse.getHttpService().getHost();
                    int port = requestResponse.getHttpService().getPort();

                    if (requestResponse.getRequest() != null) {
                        String requestStr = new String(requestResponse.getRequest(), "UTF-8");
                        String[] lines = requestStr.split("\r?\n");
                        if (lines.length > 0) {
                            String[] parts = lines[0].split(" ");
                            if (parts.length >= 2) {
                                String path = parts[1];
                                return protocol + "://" + host +
                                    (port != 80 && port != 443 ? ":" + port : "") + path;
                            }
                        }
                    }

                    return protocol + "://" + host + (port != 80 && port != 443 ? ":" + port : "");
                }
            } catch (Exception e) {
                // Ignore
            }
            return "Unknown";
        }

        public String getMethod() {
            try {
                if (requestResponse != null && requestResponse.getRequest() != null) {
                    String requestStr = new String(requestResponse.getRequest(), "UTF-8");
                    String[] lines = requestStr.split("\r?\n");
                    if (lines.length > 0) {
                        String[] parts = lines[0].split(" ");
                        if (parts.length >= 1) {
                            return parts[0];
                        }
                    }
                }
            } catch (Exception e) {
                // Ignore
            }
            return "Unknown";
        }

        public String getStatusCode() {
            try {
                if (requestResponse != null && requestResponse.getResponse() != null) {
                    String responseStr = new String(requestResponse.getResponse(), "UTF-8");
                    String[] lines = responseStr.split("\r?\n");
                    if (lines.length > 0) {
                        String[] parts = lines[0].split(" ");
                        if (parts.length >= 2) {
                            return parts[1];
                        }
                    }
                }
            } catch (Exception e) {
                // Ignore
            }
            return "Unknown";
        }

        public int getResponseLength() {
            try {
                if (requestResponse != null && requestResponse.getResponse() != null) {
                    return requestResponse.getResponse().length;
                }
            } catch (Exception e) {
                // Ignore
            }
            return 0;
        }
    }
}