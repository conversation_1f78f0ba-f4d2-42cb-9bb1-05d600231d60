package com.timebasedscan.utils;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import javax.imageio.ImageIO;

/**
 * Utility class for loading and managing icons
 * Provides caching and resizing capabilities
 */
public class IconUtils {
    
    // Cache for loaded icons to avoid reloading
    private static final Map<String, ImageIcon> iconCache = new HashMap<>();
    
    /**
     * Load an icon from resources with caching
     * 
     * @param path The resource path to the icon
     * @param width Desired width or -1 for original size
     * @param height Desired height or -1 for original size
     * @return The icon or null if loading failed
     */
    public static ImageIcon loadIcon(String path, int width, int height) {
        // Create cache key
        String cacheKey = path + "_" + width + "_" + height;
        
        // Check cache first
        if (iconCache.containsKey(cacheKey)) {
            return iconCache.get(cacheKey);
        }
        
        try {
            // Try to load the icon as a resource
            InputStream is = IconUtils.class.getResourceAsStream(path);
            if (is == null) {
                System.err.println("Icon not found: " + path);
                return null;
            }
            
            // Read image
            BufferedImage img = ImageIO.read(is);
            is.close();
            
            if (img == null) {
                System.err.println("Failed to read image: " + path);
                return null;
            }
            
            // Resize if needed
            if (width > 0 && height > 0 && (img.getWidth() != width || img.getHeight() != height)) {
                BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g = resized.createGraphics();
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g.drawImage(img, 0, 0, width, height, null);
                g.dispose();
                img = resized;
            }
            
            // Create icon and cache it
            ImageIcon icon = new ImageIcon(img);
            iconCache.put(cacheKey, icon);
            return icon;
            
        } catch (Exception e) {
            System.err.println("Error loading icon " + path + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Get a standard sized menu icon (16x16)
     * 
     * @param path The resource path to the icon
     * @return The icon resized to 16x16 or null if loading failed
     */
    public static ImageIcon getMenuIcon(String path) {
        return loadIcon(path, 16, 16);
    }
    
    /**
     * Get a standard sized toolbar icon (24x24)
     * 
     * @param path The resource path to the icon
     * @return The icon resized to 24x24 or null if loading failed
     */
    public static ImageIcon getToolbarIcon(String path) {
        return loadIcon(path, 24, 24);
    }
    
    /**
     * Get a standard sized button icon (20x20)
     * 
     * @param path The resource path to the icon
     * @return The icon resized to 20x20 or null if loading failed
     */
    public static ImageIcon getButtonIcon(String path) {
        return loadIcon(path, 20, 20);
    }
    
    /**
     * Get a standard sized tab icon (16x16)
     * 
     * @param path The resource path to the icon
     * @return The icon resized to 16x16 or null if loading failed
     */
    public static ImageIcon getTabIcon(String path) {
        return loadIcon(path, 16, 16);
    }
}