package com.timebasedscan.scanner;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import com.timebasedscan.model.HistoryEntry;
import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.ui.HistoryPanel;
import com.timebasedscan.ui.MainPanel;
import com.timebasedscan.ui.ResultsPanel;
import com.timebasedscan.utils.PayloadInjector;
import com.timebasedscan.utils.SqlErrorDetector;
import com.timebasedscan.utils.RateLimitBypass;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Handles efficient parallel execution of time-based tests
 * with optimized parameter ordering and progress tracking
 */
public class ParallelScanner {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private IHttpRequestResponse baseRequestResponse;
    private List<HttpParameter> parameters;
    private String payload;
    private int concurrencyLevel;
    private ResultsPanel resultsPanel;
    private HistoryPanel historyPanel;
    private MainPanel mainPanel;
    private PayloadInjector injector;
    private long startTime;

    // SQL error detection settings
    private boolean sqlErrorDetectionEnabled;
    private boolean showSqlErrorsInResults;

    // Rate limiting bypass
    private RateLimitBypass rateLimitBypass;

    // Advanced payload injection settings
    private boolean enableEncodingBypass = false;
    private boolean enableWafEvasion = false;
    private boolean enableContextAwareInjection = false;
    private boolean enableMultipleEncodingLayers = false;

    // Custom header injection settings
    private boolean enableCustomHeaderInjection = false;
    private String customHeaderName = "";
    private String customHeaderValue = "";

    // Constants for scanner optimization
    private static final int PROGRESS_UPDATE_INTERVAL = 5; // Update progress every 5 tests
    private static final long SCAN_TIMEOUT = 60; // Timeout in minutes

    public ParallelScanner(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                          IHttpRequestResponse baseRequestResponse, List<HttpParameter> parameters,
                          String payload, int concurrencyLevel, ResultsPanel resultsPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameters = parameters;
        this.payload = payload;
        this.concurrencyLevel = concurrencyLevel;
        this.resultsPanel = resultsPanel;
        this.historyPanel = null;  // Not used in this constructor
        this.mainPanel = null;     // Not used in this constructor
        // Corrected: Pass callbacks to PayloadInjector constructor
        this.injector = new PayloadInjector(helpers, callbacks);

        // Default SQL error detection settings
        this.sqlErrorDetectionEnabled = true;
        this.showSqlErrorsInResults = true;

        // Initialize rate limiting bypass
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);
    }

    public ParallelScanner(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                          IHttpRequestResponse baseRequestResponse, List<HttpParameter> parameters,
                          String payload, int concurrencyLevel, ResultsPanel resultsPanel,
                          boolean sqlErrorDetectionEnabled, boolean showSqlErrorsInResults) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameters = parameters;
        this.payload = payload;
        this.concurrencyLevel = concurrencyLevel;
        this.resultsPanel = resultsPanel;
        this.historyPanel = null;  // Not used in this constructor
        this.mainPanel = null;     // Not used in this constructor
        // Corrected: Pass callbacks to PayloadInjector constructor
        this.injector = new PayloadInjector(helpers, callbacks);

        // SQL error detection settings
        this.sqlErrorDetectionEnabled = sqlErrorDetectionEnabled;
        this.showSqlErrorsInResults = showSqlErrorsInResults;

        // Initialize rate limiting bypass
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);
    }

    public ParallelScanner(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                          IHttpRequestResponse baseRequestResponse, List<HttpParameter> parameters,
                          String payload, int concurrencyLevel, MainPanel mainPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameters = parameters;
        this.payload = payload;
        this.concurrencyLevel = concurrencyLevel;
        this.mainPanel = mainPanel;
        this.resultsPanel = null;  // Will be accessed through mainPanel
        this.historyPanel = null;  // Will be accessed through mainPanel
        // Corrected: Pass callbacks to PayloadInjector constructor
        this.injector = new PayloadInjector(helpers, callbacks);

        // Default SQL error detection settings
        this.sqlErrorDetectionEnabled = true;
        this.showSqlErrorsInResults = true;

        // Initialize rate limiting bypass
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);
    }

    public ParallelScanner(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                          IHttpRequestResponse baseRequestResponse, List<HttpParameter> parameters,
                          String payload, int concurrencyLevel, MainPanel mainPanel,
                          boolean sqlErrorDetectionEnabled, boolean showSqlErrorsInResults) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameters = parameters;
        this.payload = payload;
        this.concurrencyLevel = concurrencyLevel;
        this.mainPanel = mainPanel;
        this.resultsPanel = null;  // Will be accessed through mainPanel
        this.historyPanel = null;  // Will be accessed through mainPanel
        // Corrected: Pass callbacks to PayloadInjector constructor
        this.injector = new PayloadInjector(helpers, callbacks);

        // SQL error detection settings
        this.sqlErrorDetectionEnabled = sqlErrorDetectionEnabled;
        this.showSqlErrorsInResults = showSqlErrorsInResults;

        // Initialize rate limiting bypass
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);
    }

    /**
     * Configure rate limiting bypass settings
     */
    public void configureRateLimitBypass(boolean enabled, String bypassMethod, int baseDelay,
                                       boolean randomizeUserAgents, boolean randomizeXForwardedFor,
                                       boolean useProxyRotation) {
        if (rateLimitBypass != null) {
            rateLimitBypass.configure(enabled, bypassMethod, baseDelay,
                                    randomizeUserAgents, randomizeXForwardedFor, useProxyRotation);
        }
    }

    /**
     * Configure advanced payload injection settings for all tests
     */
    public void configureAdvancedPayloadInjection(boolean enableEncodingBypass, boolean enableWafEvasion,
                                                 boolean enableContextAwareInjection, boolean enableMultipleEncodingLayers) {
        // Store the settings for use in individual tests
        this.enableEncodingBypass = enableEncodingBypass;
        this.enableWafEvasion = enableWafEvasion;
        this.enableContextAwareInjection = enableContextAwareInjection;
        this.enableMultipleEncodingLayers = enableMultipleEncodingLayers;

        // Also configure the main injector
        if (injector != null) {
            injector.configureAdvancedInjection(enableEncodingBypass, enableWafEvasion,
                                              enableContextAwareInjection, enableMultipleEncodingLayers);
        }
    }

    /**
     * Configure custom header injection settings for all tests
     */
    public void configureCustomHeaderInjection(boolean enabled, String headerName, String headerValue) {
        // Store the settings for use in individual tests
        this.enableCustomHeaderInjection = enabled;
        this.customHeaderName = headerName != null ? headerName.trim() : "";
        this.customHeaderValue = headerValue != null ? headerValue.trim() : "";

        // Also configure the main injector
        if (injector != null) {
            injector.configureCustomHeaderInjection(enabled, headerName, headerValue);
        }
    }

    /**
     * Start the scanning process with optimized parameter ordering
     */
    public void startScan() {
        // Record start time for duration calculation
        startTime = System.currentTimeMillis();

        // Get the right resultsPanel if we're using mainPanel
        if (resultsPanel == null && mainPanel != null) {
            // Get resultsPanel from mainPanel
            resultsPanel = mainPanel.getResultsPanel();
        }

        // Calculate total tests
        int totalTests = parameters.size();
        if (resultsPanel != null) {
            resultsPanel.setTotalTests(totalTests);
        }

        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(concurrencyLevel);

        callbacks.printOutput("Starting time-based scan with concurrency level: " + concurrencyLevel);
        callbacks.printOutput("Testing " + parameters.size() + " parameters with payload: " + payload);

        // Optimize parameter testing order for better efficiency
        List<HttpParameter> optimizedParameters = optimizeParameterOrder(parameters);

        // Progress tracking
        AtomicInteger completedTests = new AtomicInteger(0);
        List<CompletableFuture<TestResult>> futures = new ArrayList<>();
        List<TestResult> allResults = Collections.synchronizedList(new ArrayList<>());

        // Create a test for each parameter
        for (HttpParameter parameter : optimizedParameters) {
            TimeBasedTest test = new TimeBasedTest(
                callbacks,
                helpers,
                baseRequestResponse,
                parameter,
                payload,
                injector,
                mainPanel);

            // Configure rate limiting bypass for this test
            if (rateLimitBypass != null) {
                test.configureRateLimitBypass(
                    rateLimitBypass.isEnabled(),
                    rateLimitBypass.getBypassMethod(),
                    rateLimitBypass.getBaseDelay(),
                    true, // Enable User agent randomization at request level
                    true, // Enable X-Forwarded-For randomization at request level
                    false  // Proxy rotation not implemented yet
                );
            }

            // Configure advanced payload injection for this test using stored settings
            test.configureAdvancedPayloadInjection(
                enableEncodingBypass,
                enableWafEvasion,
                enableContextAwareInjection,
                enableMultipleEncodingLayers
            );

            // Configure custom header injection for this test using stored settings
            test.configureCustomHeaderInjection(
                enableCustomHeaderInjection,
                customHeaderName,
                customHeaderValue
            );

            // Submit test to executor and store the future
            CompletableFuture<TestResult> future = CompletableFuture.supplyAsync(() -> {
                callbacks.printOutput("Testing parameter: " + parameter.getName() + " (" + parameter.getType() + ")");
                TestResult result = test.execute();

                // Update progress periodically
                int completed = completedTests.incrementAndGet();
                if (completed % PROGRESS_UPDATE_INTERVAL == 0 || completed == totalTests) {
                    callbacks.printOutput("Progress: " + completed + "/" + totalTests +
                                        " (" + (completed * 100 / totalTests) + "%)");
                }

                // Add to results panel if available
                if (resultsPanel != null) {
                    resultsPanel.addResult(result);
                }

                // Add to our complete list for history
                allResults.add(result);

                return result;
            }, executor);

            futures.add(future);
        }

        // Wait for all futures to complete or timeout
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            // Set a timeout for the entire scan
            allFutures.get(SCAN_TIMEOUT, TimeUnit.MINUTES);

            // Process results - count vulnerabilities
            long vulnerableCount = futures.stream()
                .map(CompletableFuture::join)
                .filter(result -> "Potentially Vulnerable".equals(result.getResult()))
                .count();

            callbacks.printOutput("Scan completed. Found " + vulnerableCount +
                                " potentially vulnerable parameter(s) out of " + totalTests + " tested.");

            // Add the scan to history
            addToHistory(allResults);

        } catch (Exception e) {
            callbacks.printError("Error during scan execution: " + e.getMessage());
        } finally {
            // Calculate scan duration
            long duration = System.currentTimeMillis() - startTime;

            // Shutdown executor
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                callbacks.printError("Executor shutdown interrupted: " + e.getMessage());
                executor.shutdownNow();
            }

            callbacks.printOutput("Time-based scan completed in " + (duration / 1000) + " seconds");
        }
    }

    /**
     * Optimize the order of parameters to test for better efficiency
     * This prioritizes parameters more likely to be vulnerable
     */
    private List<HttpParameter> optimizeParameterOrder(List<HttpParameter> inputParameters) {
        List<HttpParameter> optimized = new ArrayList<>(inputParameters);

        // Sort parameters by priority for testing
        Collections.sort(optimized, new Comparator<HttpParameter>() {
            @Override
            public int compare(HttpParameter p1, HttpParameter p2) {
                // First empty parameters (more likely to be injectable)
                if (p1.isEmpty() != p2.isEmpty()) {
                    return p1.isEmpty() ? -1 : 1;
                }

                // Then by parameter type
                int typePriority1 = getParameterTypePriority(p1.getType().toString());
                int typePriority2 = getParameterTypePriority(p2.getType().toString());
                if (typePriority1 != typePriority2) {
                    return Integer.compare(typePriority1, typePriority2);
                }

                // Then alphabetically by name
                return p1.getName().compareTo(p2.getName());
            }
        });

        return optimized;
    }

    /**
     * Get priority score for parameter type (lower is higher priority)
     */
    private int getParameterTypePriority(String paramType) {
        if (paramType == null) return 999;

        switch (paramType) {
            case "BODY": return 10;     // Form parameters (high priority)
            case "JSON": return 20;     // JSON parameters
            case "URL": return 30;      // URL query parameters
            case "XML": return 40;      // XML parameters
            case "REST_ID": return 50;  // REST API parameters
            case "RESOURCE_ID": return 60; // Resource IDs
            case "PATH": return 70;     // Path parameters
            case "HEADER_AUTH": return 80; // Auth headers
            case "HEADER": return 90;   // Other headers
            case "COOKIE": return 100;  // Cookies (lower priority)
            case "ENCODED": return 110; // Encoded parameters
            case "GRAPHQL": return 120; // GraphQL
            default: return 500;        // Other types
        }
    }

    /**
     * Add scan results to history
     */
    private void addToHistory(List<TestResult> results) {
        if (results == null || results.isEmpty()) {
            callbacks.printOutput("No results to add to history");
            return;
        }

        // Get URL and method directly from the base request
        String url = "";
        String method = "";

        if (baseRequestResponse != null) {
            // Extract URL from the base request response
            IHttpService service = baseRequestResponse.getHttpService();
            if (service != null) {
                String protocol = service.getProtocol();
                String host = service.getHost();
                int port = service.getPort();

                // Build the URL
                url = protocol + "://" + host;
                if ((protocol.equals("http") && port != 80) ||
                    (protocol.equals("https") && port != 443)) {
                    url += ":" + port;
                }

                // Add the path from the request if available
                byte[] request = baseRequestResponse.getRequest();
                if (request != null) {
                    // Get the path from the request line
                    String requestStr = new String(request);
                    String[] lines = requestStr.split("\r\n");
                    if (lines.length > 0) {
                        String[] parts = lines[0].split(" ");
                        if (parts.length > 1) {
                            method = parts[0]; // GET, POST, etc.

                            // Extract path - remove query parameters for cleaner URL display
                            String path = parts[1];
                            if (path.contains("?")) {
                                url += path.substring(0, path.indexOf("?"));
                            } else {
                                url += path;
                            }
                        }
                    }
                }
            }
        }

        // Fallback to TestResult data if we couldn't extract from base request
        if (url.isEmpty() && !results.isEmpty()) {
            TestResult firstResult = results.get(0);
            // Need to implement getUrl() and getMethod() in TestResult or pass request info differently
            // For now, using placeholder
            url = "[URL not extracted]";
            method = "[Method not extracted]";
        }

        // Calculate scan duration
        long duration = System.currentTimeMillis() - startTime;

        // Create history entry
        HistoryEntry historyEntry = new HistoryEntry(
            url,
            method,
            baseRequestResponse.getRequest(),
            results,
            duration
        );

        // Add to history panel
        if (historyPanel != null) {
            historyPanel.addHistoryEntry(historyEntry);
            callbacks.printOutput("Added scan to history");
        } else if (mainPanel != null) {
            // Access history panel through main panel
            HistoryPanel panel = mainPanel.getHistoryPanel();
            if (panel != null) {
                panel.addHistoryEntry(historyEntry);
                callbacks.printOutput("Added scan to history through main panel");
            } else {
                callbacks.printOutput("History panel not available from main panel");
            }
        } else {
            callbacks.printOutput("No history panel available");
        }
    }
}

