package com.timebasedscan.ui;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

/**
 * Custom renderer for displaying HTML content in the Evidence column
 * Improved version that ensures HTML is properly rendered
 */
public class EvidenceHtmlRenderer implements TableCellRenderer {
    
    @Override
    public Component getTableCellRendererComponent(JTable table, Object value, 
                                                 boolean isSelected, boolean hasFocus, 
                                                 int row, int column) {
        // Create a specialized JEditorPane for each rendering request
        JEditorPane editor = new JEditorPane();
        editor.setContentType("text/html");
        editor.setEditable(false);
        editor.setBorder(new EmptyBorder(2, 5, 2, 5));
        editor.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, Boolean.TRUE);
        editor.setFont(table.getFont());
        
        // Set proper text
        if (value == null) {
            editor.setText("<html><body>No evidence available</body></html>");
        } else {
            String text = value.toString();
            if (!text.trim().startsWith("<html>")) {
                text = "<html><body>" + text + "</body></html>";
            }
            editor.setText(text);
        }
        
        // Match the table's appearance
        if (isSelected) {
            editor.setBackground(table.getSelectionBackground());
            editor.setForeground(table.getSelectionForeground());
        } else {
            editor.setBackground(table.getBackground());
            editor.setForeground(table.getForeground());
        }
        
        // Pre-size for better rendering
        int width = table.getColumnModel().getColumn(column).getWidth();
        editor.setSize(width, Short.MAX_VALUE);
        
        return editor;
    }
}