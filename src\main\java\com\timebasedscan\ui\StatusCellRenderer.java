package com.timebasedscan.ui;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;

/**
 * Custom renderer for the Status column to highlight vulnerable entries
 * with special formatting and colors
 */
public class StatusCellRenderer extends DefaultTableCellRenderer {
    
    private static final Color VULNERABLE_BG = new Color(255, 240, 240);
    private static final Color VULNERABLE_FG = new Color(170, 0, 0);
    private static final Color SAFE_BG = new Color(240, 255, 240);
    private static final Color SAFE_FG = new Color(0, 120, 0);
    
    public StatusCellRenderer() {
        setHorizontalAlignment(JLabel.CENTER);
    }
    
    @Override
    public Component getTableCellRendererComponent(JTable table, Object value,
                                                 boolean isSelected, boolean hasFocus,
                                                 int row, int column) {
        
        Component cell = super.getTableCellRendererComponent(
            table, value, isSelected, hasFocus, row, column);
        
        if (value == null) {
            return cell;
        }
        
        String status = value.toString();
        
        if (!isSelected) {
            // Apply special styling only if the cell is not selected
            if ("Potentially Vulnerable".equals(status)) {
                cell.setBackground(VULNERABLE_BG);
                cell.setForeground(VULNERABLE_FG);
                
                // Use bold font for vulnerable entries
                Font originalFont = cell.getFont();
                Font boldFont = new Font(originalFont.getName(), Font.BOLD, originalFont.getSize());
                cell.setFont(boldFont);
                
                // Add a rounded border for emphasis
                ((JComponent)cell).setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(new Color(255, 200, 200)),
                    BorderFactory.createEmptyBorder(2, 5, 2, 5)
                ));
                
            } else if ("Not Vulnerable".equals(status)) {
                cell.setBackground(SAFE_BG);
                cell.setForeground(SAFE_FG);
                
                // Add a subtle border
                ((JComponent)cell).setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(new Color(220, 240, 220)),
                    BorderFactory.createEmptyBorder(2, 5, 2, 5)
                ));
            }
        }
        
        // Add tooltip with additional information
        if ("Potentially Vulnerable".equals(status)) {
            ((JComponent)cell).setToolTipText(
                "<html><body>This parameter showed time-based response differences<br>" +
                "that may indicate a vulnerability.</body></html>"
            );
        } else {
            ((JComponent)cell).setToolTipText(
                "<html><body>No significant time-based differences detected.</body></html>"
            );
        }
        
        return cell;
    }
}