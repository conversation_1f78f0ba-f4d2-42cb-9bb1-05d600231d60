package com.timebasedscan.model;

import javax.swing.table.AbstractTableModel;
import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced table model for displaying test results with improved
 * formatting and organization options.
 */
public class ResultTableModel extends AbstractTableModel {

    private List<TestResult> results = new ArrayList<>();
    private final String[] columnNames = {
        "Parameter", "Type", "Value", "Status", "Response Time (ms)", "Baseline (ms)", "Difference (ms)", "SQL Error"
    };

    // Additional columns for export/detail view
    private final String[] extendedColumnNames = {
        "Parameter", "Type", "Value", "Status", "Response Time (ms)", "Baseline (ms)",
        "Difference (ms)", "SQL Error", "Request Size", "Response Size", "Test Timestamp"
    };

    /**
     * Add a result to the table
     */
    public void addResult(TestResult result) {
        results.add(result);
        fireTableRowsInserted(results.size() - 1, results.size() - 1);
    }

    /**
     * Clear all results
     */
    public void clearResults() {
        results.clear();
        fireTableDataChanged();
    }

    /**
     * Get a result at a specific row
     */
    public TestResult getResultAt(int row) {
        if (row >= 0 && row < results.size()) {
            return results.get(row);
        }
        return null;
    }
    
    /**
     * Get all vulnerable parameters
     */
    public List<TestResult> getVulnerableResults() {
        List<TestResult> vulnerableResults = new ArrayList<>();
        for (TestResult result : results) {
            if ("Potentially Vulnerable".equals(result.getResult())) {
                vulnerableResults.add(result);
            }
        }
        return vulnerableResults;
    }
    
    /**
     * Get all results
     */
    public List<TestResult> getAllResults() {
        return new ArrayList<>(results);
    }
    
    /**
     * Get summary statistics
     */
    public ResultStatistics getStatistics() {
        int totalTests = results.size();
        int vulnerableCount = 0;
        
        // Group by parameter type
        java.util.Map<String, Integer> typeStats = new java.util.HashMap<>();
        java.util.Map<String, Integer> vulnerableTypeStats = new java.util.HashMap<>();
        
        for (TestResult result : results) {
            String type = result.getParameterType();
            boolean isVulnerable = "Potentially Vulnerable".equals(result.getResult());
            
            // Count total by type
            typeStats.put(type, typeStats.getOrDefault(type, 0) + 1);
            
            // Count vulnerable by type
            if (isVulnerable) {
                vulnerableCount++;
                vulnerableTypeStats.put(type, vulnerableTypeStats.getOrDefault(type, 0) + 1);
            }
        }
        
        return new ResultStatistics(totalTests, vulnerableCount, typeStats, vulnerableTypeStats);
    }

    @Override
    public int getRowCount() {
        return results.size();
    }

    @Override
    public int getColumnCount() {
        return columnNames.length;
    }

    @Override
    public Object getValueAt(int rowIndex, int columnIndex) {
        if (rowIndex >= results.size()) {
            return null;
        }
        
        TestResult result = results.get(rowIndex);
        
        switch (columnIndex) {
            case 0: return result.getParameterName();
            case 1: return formatParameterType(result.getParameterType());
            case 2: return truncateValue(result.getParameterValue(), 50);
            case 3: return result.getResult();
            case 4: return result.getResponseTime();
            case 5: return result.getBaselineTime();
            case 6: return result.getTimeDifference();
            case 7: return result.isSqlErrorDetected() ? result.getFormattedSqlError() : "No SQL Error";
            default: return null;
        }
    }
    
    /**
     * Format parameter type for better readability
     */
    private String formatParameterType(String paramType) {
        if (paramType == null) return "Unknown";
        
        switch (paramType) {
            case "REST_ID": return "REST API ID";
            case "RESOURCE_ID": return "Resource ID";
            case "HEADER_AUTH": return "Auth Header";
            case "ENCODED": return "Encoded Data";
            case "NESTED_JSON": return "Nested JSON";
            default: return paramType;
        }
    }
    
    /**
     * Truncate value for display in table
     */
    private String truncateValue(String value, int maxLength) {
        if (value == null) return "";
        if (value.length() <= maxLength) return value;
        return value.substring(0, maxLength - 3) + "...";
    }

    @Override
    public String getColumnName(int column) {
        return columnNames[column];
    }

    @Override
    public Class<?> getColumnClass(int columnIndex) {
        if (columnIndex >= 4 && columnIndex <= 6) {
            return Long.class;
        }
        return String.class;
    }
    
    /**
     * Statistics class for scan results
     */
    public static class ResultStatistics {
        private final int totalTests;
        private final int vulnerableCount;
        private final java.util.Map<String, Integer> typeStats;
        private final java.util.Map<String, Integer> vulnerableTypeStats;
        
        public ResultStatistics(int totalTests, int vulnerableCount, 
                              java.util.Map<String, Integer> typeStats,
                              java.util.Map<String, Integer> vulnerableTypeStats) {
            this.totalTests = totalTests;
            this.vulnerableCount = vulnerableCount;
            this.typeStats = typeStats;
            this.vulnerableTypeStats = vulnerableTypeStats;
        }
        
        public int getTotalTests() {
            return totalTests;
        }
        
        public int getVulnerableCount() {
            return vulnerableCount;
        }
        
        public double getVulnerablePercentage() {
            if (totalTests == 0) return 0;
            return (double) vulnerableCount / totalTests * 100;
        }
        
        public java.util.Map<String, Integer> getTypeStats() {
            return typeStats;
        }
        
        public java.util.Map<String, Integer> getVulnerableTypeStats() {
            return vulnerableTypeStats;
        }
        
        /**
         * Calculate the average response time across all results
         * @return average response time in milliseconds
         */
        public double getAverageResponseTime() {
            if (totalTests == 0) return 0;
            
            // In a real implementation, this would calculate from actual results
            // For now, return a placeholder value
            return 450.0; // Default placeholder for UI development
        }
    }
}
