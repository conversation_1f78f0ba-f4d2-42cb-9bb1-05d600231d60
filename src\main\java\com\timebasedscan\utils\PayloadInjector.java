package com.timebasedscan.utils;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IParameter;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Advanced utility class to inject payloads into HTTP requests
 * with support for various parameter types, encoding bypass techniques,
 * WAF evasion methods, and sophisticated injection strategies.
 */
public class PayloadInjector {

    private IExtensionHelpers helpers;
    private IBurpExtenderCallbacks callbacks;

    // Advanced injection configuration
    private boolean enableEncodingBypass = true;
    private boolean enableWafEvasion = true;
    private boolean enableContextAwareInjection = true;
    private boolean enableMultipleEncodingLayers = false;

    // Custom header injection configuration
    private boolean enableCustomHeaderInjection = false;
    private String customHeaderName = "";
    private String customHeaderValue = "";

    public PayloadInjector(IExtensionHelpers helpers, IBurpExtenderCallbacks callbacks) {
        this.helpers = helpers;
        this.callbacks = callbacks;
    }

    /**
     * Configure advanced injection settings
     */
    public void configureAdvancedInjection(boolean enableEncodingBypass, boolean enableWafEvasion,
                                         boolean enableContextAwareInjection, boolean enableMultipleEncodingLayers) {
        this.enableEncodingBypass = enableEncodingBypass;
        this.enableWafEvasion = enableWafEvasion;
        this.enableContextAwareInjection = enableContextAwareInjection;
        this.enableMultipleEncodingLayers = enableMultipleEncodingLayers;

        callbacks.printOutput("Advanced Payload Injection configured: " +
                            "EncodingBypass=" + enableEncodingBypass +
                            ", WafEvasion=" + enableWafEvasion +
                            ", ContextAware=" + enableContextAwareInjection +
                            ", MultipleEncoding=" + enableMultipleEncodingLayers);
    }

    /**
     * Configure custom header injection settings
     */
    public void configureCustomHeaderInjection(boolean enabled, String headerName, String headerValue) {
        this.enableCustomHeaderInjection = enabled;
        this.customHeaderName = headerName != null ? headerName.trim() : "";
        this.customHeaderValue = headerValue != null ? headerValue.trim() : "";

        if (enabled && !customHeaderName.isEmpty()) {
            callbacks.printOutput("Custom Header Injection configured: " + customHeaderName + ": " + customHeaderValue);
        } else if (enabled) {
            callbacks.printOutput("Custom Header Injection enabled but no header name provided");
        } else {
            callbacks.printOutput("Custom Header Injection disabled");
        }
    }

    /**
     * Inject a payload into a specific parameter in an HTTP request,
     * preserving the parameter's original position and injecting the payload raw (no encoding).
     */
    public byte[] injectPayload(byte[] request, HttpParameter parameter, String payload) {
        byte[] modifiedRequest = request;
        byte burpType = parameter.getBurpType();
        String paramTypeStr = parameter.getType().toString();
        String originalValue = parameter.getValue();
        String paramName = parameter.getName();

        // Pre-process payload with advanced techniques
        String processedPayload = preprocessPayloadAdvanced(payload, parameter);

        // Determine the final value to inject (usually original + payload)
        String finalValue = determineFinalValue(originalValue, processedPayload);

        // Apply custom header injection if enabled
        modifiedRequest = applyCustomHeaderInjection(modifiedRequest, processedPayload);

        // Check if this is a parameter name injection
        boolean isNameInjection = paramName.endsWith("__NAME__");

        // Handle different parameter types
        if (burpType == IParameter.PARAM_URL || burpType == IParameter.PARAM_BODY || burpType == IParameter.PARAM_COOKIE) {
            // Standard Burp types: Use manual injection to preserve order and avoid encoding
            try {
                if (isNameInjection) {
                    modifiedRequest = injectIntoParameterName(request, parameter, finalValue);
                } else {
                    modifiedRequest = injectStandardParameterManuallyNoEncoding(request, parameter, finalValue);
                }
            } catch (Exception e) {
                callbacks.printError("Error during manual parameter injection (no encoding) for " + paramName + ": " + e.getMessage());
                // Fallback: Try Burp's updateParameter, but it might reorder and potentially encode
                if (!isNameInjection) {
                    try {
                        callbacks.printOutput("Warning: Falling back to helpers.updateParameter for " + paramName + ". Order/encoding might change.");
                        IParameter burpParameter = helpers.buildParameter(paramName, finalValue, burpType);
                        modifiedRequest = helpers.updateParameter(request, burpParameter);
                    } catch (Exception burpE) {
                         callbacks.printError("Fallback helpers.updateParameter also failed for " + paramName + ": " + burpE.getMessage());
                         modifiedRequest = request; // Return original if both fail
                    }
                } else {
                    modifiedRequest = request; // Can't fallback for name injection
                }
            }
        } else if (burpType == -1) {
            // Header parameter - handle specially
            if (isNameInjection) {
                modifiedRequest = injectIntoHeaderName(request, parameter, finalValue);
            } else {
                modifiedRequest = injectIntoHeader(request, parameter, finalValue);
            }
        } else if (burpType == -2) {
            // URL path parameter - handle specially
            modifiedRequest = injectIntoUrlPath(request, parameter, finalValue);
        } else {
            // Handle custom parameter types (injecting raw value)
            switch (paramTypeStr) {
                case "PATH":
                case "REST_ID":
                case "RESOURCE_ID":
                    modifiedRequest = injectIntoPathParameterNoEncoding(request, parameter, finalValue);
                    break;
                case "HEADER_AUTH":
                case "HEADER":
                    // Headers generally don't need URL encoding, inject raw
                    modifiedRequest = injectIntoHeader(request, parameter, finalValue);
                    break;
                case "GRAPHQL":
                    // GraphQL needs specific JSON string escaping, not URL encoding
                    modifiedRequest = injectIntoGraphQL(request, parameter, finalValue);
                    break;
                case "ENCODED":
                    modifiedRequest = injectIntoEncodedParameter(request, parameter, finalValue);
                    break;
                case "NESTED_JSON":
                    modifiedRequest = injectIntoNestedJson(request, parameter, finalValue);
                    break;
                case "JSON":
                    // Handle JSON parameters (including nested ones)
                    if (isNameInjection) {
                        modifiedRequest = injectIntoJsonParameterName(request, parameter, finalValue);
                    } else {
                        modifiedRequest = injectIntoJsonParameter(request, parameter, finalValue);
                    }
                    break;
                case "BODY":
                    // Handle special body parameters
                    if (paramName.equals("__BODY__")) {
                        modifiedRequest = injectIntoFullBody(request, parameter, finalValue);
                    } else if (isNameInjection) {
                        modifiedRequest = injectIntoParameterName(request, parameter, finalValue);
                    } else {
                        // Regular body parameter, use standard injection
                        try {
                            modifiedRequest = injectStandardParameterManuallyNoEncoding(request, parameter, finalValue);
                        } catch (Exception e) {
                            callbacks.printError("Error injecting into body parameter '" + paramName + "': " + e.getMessage());
                            // For __BODY__ parameters that fail standard injection, try full body injection
                            if (paramName.contains("__BODY__") || paramName.startsWith("__")) {
                                callbacks.printOutput("Attempting full body injection for synthetic parameter: " + paramName);
                                modifiedRequest = injectIntoFullBody(request, parameter, finalValue);
                            } else {
                                modifiedRequest = request;
                            }
                        }
                    }
                    break;
                case "WEBSOCKET":
                    modifiedRequest = injectIntoWebSocket(request, parameter, finalValue);
                    break;
                case "GRPC":
                case "PROTOBUF":
                    modifiedRequest = injectIntoProtobuf(request, parameter, finalValue);
                    break;
                case "MESSAGEPACK":
                case "CBOR":
                case "AVRO":
                case "BINARY":
                    modifiedRequest = injectIntoBinaryFormat(request, parameter, finalValue);
                    break;
                case "SSE":
                    modifiedRequest = injectIntoServerSentEvents(request, parameter, finalValue);
                    break;
                case "YAML":
                case "TOML":
                case "INI":
                    modifiedRequest = injectIntoConfigurationFormat(request, parameter, finalValue);
                    break;
                case "TEMPLATE":
                case "HANDLEBARS":
                case "JINJA2":
                    modifiedRequest = injectIntoTemplateFormat(request, parameter, finalValue);
                    break;
                case "JWT":
                    modifiedRequest = injectIntoJWT(request, parameter, finalValue);
                    break;
                case "BASE64":
                    modifiedRequest = injectIntoBase64(request, parameter, finalValue);
                    break;
                case "REST_PATH":
                    modifiedRequest = injectIntoRestPath(request, parameter, finalValue);
                    break;
                case "AUTH":
                    modifiedRequest = injectIntoAuthParameter(request, parameter, finalValue);
                    break;
                case "STRUCTURED_HEADER":
                    modifiedRequest = injectIntoStructuredHeader(request, parameter, finalValue);
                    break;
                case "NESTED_FORM":
                    modifiedRequest = injectIntoNestedForm(request, parameter, finalValue);
                    break;
                case "FILE_UPLOAD":
                    modifiedRequest = injectIntoFileUpload(request, parameter, finalValue);
                    break;
                default:
                    callbacks.printOutput("Warning: Unknown custom parameter type '" + paramTypeStr + "'. Attempting path injection without encoding.");
                    modifiedRequest = injectIntoPathParameterNoEncoding(request, parameter, finalValue);
            }
        }

        return modifiedRequest;
    }

    /**
     * Determines the final value to be injected, typically appending the payload.
     */
    private String determineFinalValue(String originalValue, String processedPayload) {
        // Logic remains the same: decide how to combine original value and payload
        if (processedPayload.matches("(?i)^(\\+|\'|\")\\s*AND.*") ||
            processedPayload.matches("(?i)^.*(SLEEP|WAITFOR|pg_sleep)\\s*\\(.*") ) {
            return originalValue + processedPayload;
        } else {
            return originalValue.isEmpty() ? processedPayload : originalValue + processedPayload;
        }
    }

    /**
     * Manually injects payload into standard URL, Body, or Cookie parameters
     * by rebuilding the request string to preserve parameter order and WITHOUT URL encoding.
     */
    private byte[] injectStandardParameterManuallyNoEncoding(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<IParameter> originalParams = requestInfo.getParameters();
        String paramName = parameter.getName();
        byte paramBurpType = parameter.getBurpType();

        // Find the specific parameter instance
        IParameter targetParam = null;
        for (IParameter p : originalParams) {
            if (p.getName().equals(paramName) && p.getType() == paramBurpType && p.getValue().equals(parameter.getValue())) {
                targetParam = p;
                break;
            }
        }
        if (targetParam == null) {
            for (IParameter p : originalParams) {
                if (p.getName().equals(paramName) && p.getType() == paramBurpType) {
                    targetParam = p;
                    callbacks.printOutput("Warning: Exact parameter value match failed for '" + paramName + "'. Using first match by name/type.");
                    break;
                }
            }
        }
        if (targetParam == null) {
            // Check if this is a synthetic parameter that we should handle differently
            if (paramName.equals("__BODY__") || paramName.startsWith("__") || paramName.endsWith("__")) {
                callbacks.printOutput("Skipping synthetic parameter '" + paramName + "' - not found in standard parameters");
                return request; // Return original request instead of throwing error
            } else {
                callbacks.printError("Could not find parameter '" + paramName + "' with type " + paramBurpType + " in request to inject manually.");
                return request;
            }
        }

        int valueStartOffset = targetParam.getValueStart();
        int valueEndOffset = targetParam.getValueEnd();

        // --- CHANGE: Use raw finalValue without URL encoding ---
        byte[] newValueBytes = (finalValue != null ? finalValue : "").getBytes(StandardCharsets.UTF_8);
        // --- END CHANGE ---

        // Rebuild the request manually
        byte[] requestBytes = request;
        byte[] prefix = Arrays.copyOfRange(requestBytes, 0, valueStartOffset);
        byte[] suffix = Arrays.copyOfRange(requestBytes, valueEndOffset, requestBytes.length);

        byte[] newRequest = new byte[prefix.length + newValueBytes.length + suffix.length];
        System.arraycopy(prefix, 0, newRequest, 0, prefix.length);
        System.arraycopy(newValueBytes, 0, newRequest, prefix.length, newValueBytes.length);
        System.arraycopy(suffix, 0, newRequest, prefix.length + newValueBytes.length, suffix.length);

        // Update Content-Length header if it's a body parameter modification
        if (paramBurpType == IParameter.PARAM_BODY) {
             List<String> headers = requestInfo.getHeaders();
             // Extract the potentially modified body from the rebuilt request
             int currentBodyOffset = helpers.analyzeRequest(newRequest).getBodyOffset();
             byte[] newBody = Arrays.copyOfRange(newRequest, currentBodyOffset, newRequest.length);
             // Let Burp rebuild with correct Content-Length
             return helpers.buildHttpMessage(headers, newBody);
        } else {
            // For URL/Cookie params, the manual rebuild is sufficient
            return newRequest;
        }
    }

    /**
     * Advanced payload preprocessing with encoding bypass and WAF evasion techniques
     */
    private String preprocessPayloadAdvanced(String payload, HttpParameter parameter) {
        String processedPayload = payload;
        byte burpType = parameter.getBurpType();
        HttpParameter.Type paramType = parameter.getType();

        // Check if ANY advanced injection technique is enabled
        boolean anyAdvancedTechniqueEnabled = enableContextAwareInjection || enableEncodingBypass ||
                                            enableWafEvasion || enableMultipleEncodingLayers;

        if (anyAdvancedTechniqueEnabled) {
            callbacks.printOutput("Advanced Payload Injection: Processing payload for parameter '" +
                                parameter.getName() + "' (type: " + paramType + ")");

            // Apply context-aware injection based on parameter type and location
            if (enableContextAwareInjection) {
                processedPayload = applyContextAwareInjection(processedPayload, parameter);
            }

            // Apply encoding bypass techniques
            if (enableEncodingBypass) {
                processedPayload = applyEncodingBypass(processedPayload, parameter);
            }

            // Apply WAF evasion techniques
            if (enableWafEvasion) {
                processedPayload = applyWafEvasion(processedPayload, parameter);
            }

            // Apply multiple encoding layers if enabled
            if (enableMultipleEncodingLayers) {
                processedPayload = applyMultipleEncodingLayers(processedPayload, parameter);
            }

            if (!processedPayload.equals(payload)) {
                callbacks.printOutput("Advanced Payload Injection: Payload transformed from '" +
                                    payload + "' to '" + processedPayload + "'");
            }
        } else {
            callbacks.printOutput("Advanced Payload Injection: DISABLED - Using original payload as-is for parameter '" +
                                parameter.getName() + "'");
        }

        // Legacy space handling for URL parameters (only if no advanced techniques are applied)
        if (!anyAdvancedTechniqueEnabled && (burpType == IParameter.PARAM_URL || paramType == HttpParameter.Type.URL)) {
            if (processedPayload.contains("SLEEP") || processedPayload.contains("sleep")) {
                 if (processedPayload.matches(".*(?i)sleep\\s*\\(.*") && !processedPayload.contains("%")) {
                     processedPayload = processedPayload.replace(" ", "+");
                     callbacks.printOutput("Applied minimal URL space encoding for compatibility");
                 }
            }
        }

        return processedPayload;
    }

    /**
     * Apply context-aware injection based on parameter type and location
     */
    private String applyContextAwareInjection(String payload, HttpParameter parameter) {
        String contextPayload = payload;
        HttpParameter.Type paramType = parameter.getType();
        String paramName = parameter.getName().toLowerCase();

        // Database context detection
        if (paramName.contains("db") || paramName.contains("database") || paramName.contains("sql")) {
            // Database-specific context
            contextPayload = enhanceForDatabaseContext(contextPayload);
        }

        // Authentication context detection
        if (paramName.contains("auth") || paramName.contains("login") || paramName.contains("user") ||
            paramName.contains("pass") || paramName.contains("token")) {
            // Authentication-specific context
            contextPayload = enhanceForAuthContext(contextPayload);
        }

        // JSON context detection
        if (paramType == HttpParameter.Type.JSON) {
            // JSON-specific context
            contextPayload = enhanceForJsonContext(contextPayload);
        }

        // Header context detection
        if (paramType == HttpParameter.Type.HEADER || paramType == HttpParameter.Type.AUTH) {
            // Header-specific context
            contextPayload = enhanceForHeaderContext(contextPayload);
        }

        return contextPayload;
    }

    /**
     * Apply encoding bypass techniques
     */
    private String applyEncodingBypass(String payload, HttpParameter parameter) {
        String bypassPayload = payload;

        // URL encoding bypass techniques
        if (parameter.getBurpType() == IParameter.PARAM_URL) {
            bypassPayload = applyUrlEncodingBypass(bypassPayload);
        }

        // HTML encoding bypass techniques
        if (payload.contains("<") || payload.contains(">") || payload.contains("&")) {
            bypassPayload = applyHtmlEncodingBypass(bypassPayload);
        }

        // Unicode encoding bypass techniques
        if (payload.contains("'") || payload.contains("\"")) {
            bypassPayload = applyUnicodeEncodingBypass(bypassPayload);
        }

        // Base64 encoding bypass techniques
        if (parameter.getName().toLowerCase().contains("base64") ||
            parameter.getName().toLowerCase().contains("encoded")) {
            bypassPayload = applyBase64EncodingBypass(bypassPayload);
        }

        return bypassPayload;
    }

    /**
     * Apply WAF evasion techniques
     */
    private String applyWafEvasion(String payload, HttpParameter parameter) {
        String evasionPayload = payload;

        // Comment-based evasion
        evasionPayload = applyCommentEvasion(evasionPayload);

        // Case variation evasion
        evasionPayload = applyCaseVariationEvasion(evasionPayload);

        // Whitespace evasion
        evasionPayload = applyWhitespaceEvasion(evasionPayload);

        // Function name obfuscation
        evasionPayload = applyFunctionObfuscation(evasionPayload);

        // Keyword splitting
        evasionPayload = applyKeywordSplitting(evasionPayload);

        return evasionPayload;
    }

    /**
     * Apply multiple encoding layers
     */
    private String applyMultipleEncodingLayers(String payload, HttpParameter parameter) {
        String layeredPayload = payload;

        // Apply double URL encoding
        if (parameter.getBurpType() == IParameter.PARAM_URL) {
            try {
                layeredPayload = URLEncoder.encode(URLEncoder.encode(layeredPayload, "UTF-8"), "UTF-8");
                callbacks.printOutput("Applied double URL encoding to payload");
            } catch (UnsupportedEncodingException e) {
                callbacks.printError("Error applying double URL encoding: " + e.getMessage());
            }
        }

        // Apply HTML + URL encoding combination
        if (parameter.getType() == HttpParameter.Type.BODY) {
            layeredPayload = layeredPayload.replace("'", "&#39;");
            layeredPayload = layeredPayload.replace("\"", "&#34;");
            layeredPayload = layeredPayload.replace("<", "&#60;");
            layeredPayload = layeredPayload.replace(">", "&#62;");
            callbacks.printOutput("Applied HTML entity encoding to payload");
        }

        return layeredPayload;
    }

    /**
     * Enhance payload for database context
     */
    private String enhanceForDatabaseContext(String payload) {
        String enhanced = payload;

        // Add database-specific comment styles
        if (enhanced.contains("SLEEP") || enhanced.contains("sleep")) {
            // MySQL-style comments
            enhanced = enhanced.replace("SLEEP", "/**/SLEEP/**/");
            enhanced = enhanced.replace("sleep", "/**/sleep/**/");
        }

        // Add database function variations
        if (enhanced.contains("UNION")) {
            enhanced = enhanced.replace("UNION", "/*!UNION*/");
        }

        return enhanced;
    }

    /**
     * Enhance payload for authentication context
     */
    private String enhanceForAuthContext(String payload) {
        String enhanced = payload;

        // Add authentication bypass patterns
        if (enhanced.contains("'")) {
            // Add common auth bypass patterns
            enhanced = enhanced + "/*auth bypass*/";
        }

        return enhanced;
    }

    /**
     * Enhance payload for JSON context
     */
    private String enhanceForJsonContext(String payload) {
        String enhanced = payload;

        // Escape JSON special characters properly
        enhanced = enhanced.replace("\\", "\\\\");
        enhanced = enhanced.replace("\"", "\\\"");

        return enhanced;
    }

    /**
     * Enhance payload for header context
     */
    private String enhanceForHeaderContext(String payload) {
        String enhanced = payload;

        // Remove newlines that could break headers
        enhanced = enhanced.replace("\n", "").replace("\r", "");

        return enhanced;
    }

    /**
     * Apply URL encoding bypass techniques
     */
    private String applyUrlEncodingBypass(String payload) {
        String bypassed = payload;

        // Double encoding bypass
        bypassed = bypassed.replace("'", "%2527");  // Double-encoded single quote
        bypassed = bypassed.replace("\"", "%2522"); // Double-encoded double quote
        bypassed = bypassed.replace(" ", "%2520");  // Double-encoded space

        // Mixed case encoding
        bypassed = bypassed.replace("SELECT", "Se%4cect");
        bypassed = bypassed.replace("UNION", "Un%49on");

        return bypassed;
    }

    /**
     * Apply HTML encoding bypass techniques
     */
    private String applyHtmlEncodingBypass(String payload) {
        String bypassed = payload;

        // HTML entity encoding
        bypassed = bypassed.replace("<", "&#60;");
        bypassed = bypassed.replace(">", "&#62;");
        bypassed = bypassed.replace("&", "&#38;");

        // Hex entity encoding
        bypassed = bypassed.replace("'", "&#x27;");
        bypassed = bypassed.replace("\"", "&#x22;");

        return bypassed;
    }

    /**
     * Apply Unicode encoding bypass techniques
     */
    private String applyUnicodeEncodingBypass(String payload) {
        String bypassed = payload;

        // Unicode normalization bypass
        bypassed = bypassed.replace("'", "\\u0027");
        bypassed = bypassed.replace("\"", "\\u0022");

        // Unicode overlong encoding
        bypassed = bypassed.replace("SELECT", "S\u0045LECT");
        bypassed = bypassed.replace("UNION", "U\u004eION");

        return bypassed;
    }

    /**
     * Apply Base64 encoding bypass techniques
     */
    private String applyBase64EncodingBypass(String payload) {
        String bypassed = payload;

        try {
            // Base64 encode the payload
            String encoded = java.util.Base64.getEncoder().encodeToString(bypassed.getBytes("UTF-8"));
            callbacks.printOutput("Applied Base64 encoding bypass: " + bypassed + " -> " + encoded);
            return encoded;
        } catch (UnsupportedEncodingException e) {
            callbacks.printError("Error applying Base64 encoding bypass: " + e.getMessage());
            return bypassed;
        }
    }

    /**
     * Apply comment-based evasion
     */
    private String applyCommentEvasion(String payload) {
        String evaded = payload;

        // MySQL comment evasion
        evaded = evaded.replace("SELECT", "SE/**/LECT");
        evaded = evaded.replace("UNION", "UN/**/ION");
        evaded = evaded.replace("WHERE", "WH/**/ERE");

        // SQL Server comment evasion
        evaded = evaded.replace("AND", "AN/**/D");
        evaded = evaded.replace("OR", "O/**/R");

        return evaded;
    }

    /**
     * Apply case variation evasion
     */
    private String applyCaseVariationEvasion(String payload) {
        String evaded = payload;

        // Mixed case variations
        evaded = evaded.replace("SELECT", "SeLeCt");
        evaded = evaded.replace("UNION", "UnIoN");
        evaded = evaded.replace("WHERE", "WhErE");
        evaded = evaded.replace("ORDER", "OrDeR");
        evaded = evaded.replace("GROUP", "GrOuP");

        return evaded;
    }

    /**
     * Apply whitespace evasion
     */
    private String applyWhitespaceEvasion(String payload) {
        String evaded = payload;

        // Tab and newline evasion
        evaded = evaded.replace(" ", "\t");
        evaded = evaded.replace("SELECT", "SELECT\n");
        evaded = evaded.replace("UNION", "UNION\t");

        // Multiple spaces
        evaded = evaded.replace(" AND ", "  AND  ");
        evaded = evaded.replace(" OR ", "  OR  ");

        return evaded;
    }

    /**
     * Apply function name obfuscation
     */
    private String applyFunctionObfuscation(String payload) {
        String obfuscated = payload;

        // Function name variations
        obfuscated = obfuscated.replace("SLEEP(", "BENCHMARK(1000000,MD5(1))#");
        obfuscated = obfuscated.replace("sleep(", "pg_sleep(");
        obfuscated = obfuscated.replace("WAITFOR", "WAIT/**/FOR");

        return obfuscated;
    }

    /**
     * Apply keyword splitting
     */
    private String applyKeywordSplitting(String payload) {
        String split = payload;

        // Split keywords with comments
        split = split.replace("SELECT", "SEL/**/ECT");
        split = split.replace("UNION", "UNI/**/ON");
        split = split.replace("INSERT", "INS/**/ERT");
        split = split.replace("UPDATE", "UPD/**/ATE");
        split = split.replace("DELETE", "DEL/**/ETE");

        return split;
    }

    /**
     * Apply custom header injection to the HTTP request
     */
    private byte[] applyCustomHeaderInjection(byte[] request, String payload) {
        if (!enableCustomHeaderInjection || customHeaderName.isEmpty()) {
            return request;
        }

        try {
            // Parse the request to get headers
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());

            // Prepare the header value (replace {PAYLOAD} placeholder if present)
            String headerValue = customHeaderValue;
            if (headerValue.contains("{PAYLOAD}")) {
                headerValue = headerValue.replace("{PAYLOAD}", payload);
                callbacks.printOutput("Custom Header Injection: Injecting payload into header value");
            }

            // Remove existing header with the same name (case-insensitive)
            headers.removeIf(header -> {
                String[] parts = header.split(":", 2);
                return parts.length >= 1 && parts[0].trim().equalsIgnoreCase(customHeaderName);
            });

            // Add the custom header
            String customHeader = customHeaderName + ": " + headerValue;
            headers.add(customHeader);

            callbacks.printOutput("Custom Header Injection: Added header '" + customHeader + "'");

            // Get the body
            byte[] body = null;
            int bodyOffset = requestInfo.getBodyOffset();
            if (bodyOffset < request.length) {
                body = new byte[request.length - bodyOffset];
                System.arraycopy(request, bodyOffset, body, 0, body.length);
            }

            // Rebuild the request with the new headers
            return helpers.buildHttpMessage(headers, body);

        } catch (Exception e) {
            callbacks.printError("Error applying custom header injection: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject a payload into a path parameter WITHOUT URL encoding.
     */
    private byte[] injectIntoPathParameterNoEncoding(byte[] request, HttpParameter parameter, String finalValue) {
        String requestString = new String(request, StandardCharsets.UTF_8);
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());
        String requestLine = headers.get(0);
        String originalValue = parameter.getValue();
        String paramName = parameter.getName();

        boolean replaced = false;
        try {
            // --- CHANGE: Use raw values, no URLEncoder.encode --- 
            String rawOriginalValue = originalValue;
            String rawFinalValue = finalValue;
            // --- END CHANGE ---

            if (paramName.startsWith("path_segment_")) {
                String[] requestParts = requestLine.split(" ", 3);
                if (requestParts.length >= 2) {
                    String path = requestParts[1];
                    String[] segments = path.split("/");
                    int segmentIndex = Integer.parseInt(paramName.substring("path_segment_".length()));
                    // Compare with raw original value
                    if (segmentIndex >= 0 && segmentIndex < segments.length && segments[segmentIndex].equals(rawOriginalValue)) {
                        // Inject raw final value
                        segments[segmentIndex] = rawFinalValue;
                        String newPath = String.join("/", segments);
                        headers.set(0, requestParts[0] + " " + newPath + (requestParts.length > 2 ? " " + requestParts[2] : ""));
                        replaced = true;
                    }
                }
            } else if (paramName.endsWith("_id") && !paramName.startsWith("path_segment_")) {
                 String potentialPathSegment = "/" + rawOriginalValue;
                 // Use regex with Pattern.quote for the raw original value
                 String regex = "(/|" + Pattern.quote(rawOriginalValue) + ")(?=[/?#]|$)"; 
                 Pattern p = Pattern.compile(regex);
                 Matcher m = p.matcher(requestLine);
                 StringBuffer sb = new StringBuffer();
                 if (m.find()) { 
                     if (m.group(1).equals(rawOriginalValue)) { 
                         // Replace with raw final value, using Matcher.quoteReplacement for safety
                         m.appendReplacement(sb, Matcher.quoteReplacement(rawFinalValue));
                         replaced = true;
                     } else { 
                         m.appendReplacement(sb, Matcher.quoteReplacement("/" + rawFinalValue));
                         replaced = true;
                     }
                     m.appendTail(sb);
                     requestLine = sb.toString();
                     headers.set(0, requestLine);
                 }
            }

            if (!replaced && !rawOriginalValue.isEmpty()) {
                // Fallback: simple string replacement of the raw value
                if (requestLine.contains(rawOriginalValue)) {
                    requestLine = requestLine.replaceFirst(Pattern.quote(rawOriginalValue), Matcher.quoteReplacement(rawFinalValue));
                    headers.set(0, requestLine);
                    replaced = true;
                } else {
                     callbacks.printOutput("Warning: Could not find raw original path value '" + rawOriginalValue + "' in request line for replacement.");
                }
            }
        } catch (NumberFormatException | IndexOutOfBoundsException e) {
            callbacks.printError("Error during path parameter injection (no encoding) for " + paramName + ": " + e.getMessage());
            return request;
        }

        if (!replaced) {
             callbacks.printError("Failed to inject into path parameter '" + paramName + "' (no encoding). Returning original request.");
             return request;
        }

        return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
    }

    /**
     * Inject a payload into an HTTP header (no encoding needed).
     */
    private byte[] injectIntoHeader(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());
        String headerName = parameter.getName();
        boolean headerFound = false;

        for (int i = 1; i < headers.size(); i++) {
            String header = headers.get(i);
            int colonIndex = header.indexOf(':');
            if (colonIndex > 0) {
                String name = header.substring(0, colonIndex).trim();
                if (name.equalsIgnoreCase(headerName)) {
                    if (name.equalsIgnoreCase("Authorization") && header.contains(" ")) {
                        String scheme = header.substring(colonIndex + 1).trim().split("\\s+", 2)[0];
                        headers.set(i, name + ": " + scheme + " " + finalValue);
                    } else {
                        headers.set(i, name + ": " + finalValue);
                    }
                    headerFound = true;
                    break;
                }
            }
        }

        if (!headerFound) {
            headers.add(headerName + ": " + finalValue);
        }

        return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
    }

    /**
     * Inject a payload into a GraphQL query (uses JSON string escaping, not URL encoding).
     */
    private byte[] injectIntoGraphQL(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        String requestString = new String(request, StandardCharsets.UTF_8);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset < requestString.length()) {
            String body = requestString.substring(bodyOffset);
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            if (paramName.contains("_")) {
                String[] parts = paramName.split("_", 2);
                String argName = parts[1];

                Pattern argPattern = Pattern.compile(
                    "(" + Pattern.quote(argName) + "\\s*:\\s*\\\")" + 
                    Pattern.quote(originalValue) +                             
                    "(\\\")"                                               
                );

                Matcher matcher = argPattern.matcher(body);
                if (matcher.find()) {
                    String escapedFinalValue = finalValue.replace("\\", "\\\\").replace("\"", "\\\"");
                    String replacement = matcher.group(1) + escapedFinalValue + matcher.group(2);
                    body = matcher.replaceFirst(Matcher.quoteReplacement(replacement));

                    List<String> headers = requestInfo.getHeaders();
                    return helpers.buildHttpMessage(headers, body.getBytes(StandardCharsets.UTF_8));
                } else {
                    callbacks.printOutput("Warning: Could not find GraphQL argument '" + argName + "' with original value in request body.");
                }
            } else {
                 callbacks.printOutput("Warning: GraphQL parameter name '" + paramName + "' does not follow Operation_Argument format.");
            }
        } else {
             callbacks.printOutput("Warning: No body found in GraphQL request.");
        }
        return request;
    }

    /**
     * Placeholder for injecting into encoded parameters.
     */
     private byte[] injectIntoEncodedParameter(byte[] request, HttpParameter parameter, String finalValue) {
         callbacks.printError("Injecting into ENCODED parameters is not fully implemented yet. Parameter: " + parameter.getName());
         return request;
     }

    /**
     * Inject into JSON parameters (including nested ones)
     */
    private byte[] injectIntoJsonParameter(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for JSON parameter injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            // Handle nested JSON paths like "user.profile.name" or "items[0].id"
            String modifiedBody = injectIntoJsonPath(body, paramName, originalValue, finalValue);

            if (!modifiedBody.equals(body)) {
                List<String> headers = requestInfo.getHeaders();
                return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
            } else {
                callbacks.printOutput("Warning: Could not find JSON parameter '" + paramName + "' in body for injection");
                return request;
            }
        } catch (Exception e) {
            callbacks.printError("Error injecting into JSON parameter '" + paramName + "': " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into the full body content
     */
    private byte[] injectIntoFullBody(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for full body injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
        String paramName = parameter.getName();

        try {
            String modifiedBody;

            if (paramName.equals("__BODY__")) {
                // Replace the entire body with the payload
                modifiedBody = finalValue;
            } else {
                // Fallback: shouldn't happen with current implementation
                callbacks.printError("Unexpected parameter name in injectIntoFullBody: " + paramName);
                return request;
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into full body parameter '" + paramName + "': " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into a specific JSON path
     */
    private String injectIntoJsonPath(String jsonBody, String jsonPath, String originalValue, String finalValue) {
        try {
            // Handle simple key replacement first
            if (!jsonPath.contains(".") && !jsonPath.contains("[")) {
                // Simple key like "username"
                String pattern = "\"" + Pattern.quote(jsonPath) + "\"\\s*:\\s*\"" + Pattern.quote(originalValue) + "\"";
                String replacement = "\"" + jsonPath + "\": \"" + finalValue.replace("\"", "\\\"") + "\"";
                return jsonBody.replaceFirst(pattern, replacement);
            }

            // Handle nested paths like "user.name" or "items[0].id"
            String[] pathParts = jsonPath.split("\\.");
            String currentPattern = jsonBody;

            // For now, use a simple approach - look for the final key and original value
            String finalKey = pathParts[pathParts.length - 1];

            // Remove array indices if present
            if (finalKey.contains("[")) {
                finalKey = finalKey.substring(0, finalKey.indexOf("["));
            }

            // Try to replace the value
            String pattern = "\"" + Pattern.quote(finalKey) + "\"\\s*:\\s*\"" + Pattern.quote(originalValue) + "\"";
            String replacement = "\"" + finalKey + "\": \"" + finalValue.replace("\"", "\\\"") + "\"";
            String result = jsonBody.replaceFirst(pattern, replacement);

            if (result.equals(jsonBody)) {
                // Try without quotes (for non-string values)
                pattern = "\"" + Pattern.quote(finalKey) + "\"\\s*:\\s*" + Pattern.quote(originalValue);
                replacement = "\"" + finalKey + "\": \"" + finalValue.replace("\"", "\\\"") + "\"";
                result = jsonBody.replaceFirst(pattern, replacement);
            }

            return result;
        } catch (Exception e) {
            callbacks.printError("Error in injectIntoJsonPath: " + e.getMessage());
            return jsonBody;
        }
    }

    /**
     * Inject payload into parameter name (for URL, body, cookie parameters)
     */
    private byte[] injectIntoParameterName(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        String requestString = new String(request, StandardCharsets.UTF_8);

        // Get the original parameter name (remove __NAME__ suffix)
        String paramName = parameter.getName();
        if (paramName.endsWith("__NAME__")) {
            paramName = paramName.substring(0, paramName.length() - "__NAME__".length());
        }
        String originalName = parameter.getValue(); // The "value" contains the original name

        try {
            // Replace parameter name in the request
            String modifiedRequest = requestString;

            // For URL parameters: replace in query string
            if (parameter.getBurpType() == IParameter.PARAM_URL) {
                modifiedRequest = modifiedRequest.replaceAll(
                    "([?&])" + Pattern.quote(originalName) + "=",
                    "$1" + finalValue + "="
                );
            }
            // For body parameters: replace in body
            else if (parameter.getBurpType() == IParameter.PARAM_BODY) {
                modifiedRequest = modifiedRequest.replaceAll(
                    "([&]|^)" + Pattern.quote(originalName) + "=",
                    "$1" + finalValue + "="
                );
            }
            // For cookie parameters: replace in Cookie header
            else if (parameter.getBurpType() == IParameter.PARAM_COOKIE) {
                modifiedRequest = modifiedRequest.replaceAll(
                    "([;\\s]|^)" + Pattern.quote(originalName) + "=",
                    "$1" + finalValue + "="
                );
            }

            return modifiedRequest.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            callbacks.printError("Error injecting into parameter name '" + paramName + "': " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into header name
     */
    private byte[] injectIntoHeaderName(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());

        // Get the original header name (remove __NAME__ suffix)
        String paramName = parameter.getName();
        if (paramName.endsWith("__NAME__")) {
            paramName = paramName.substring(0, paramName.length() - "__NAME__".length());
        }
        String originalName = parameter.getValue(); // The "value" contains the original name

        boolean headerFound = false;

        // Find and replace the header name
        for (int i = 1; i < headers.size(); i++) {
            String header = headers.get(i);
            int colonIndex = header.indexOf(':');
            if (colonIndex > 0) {
                String name = header.substring(0, colonIndex).trim();
                if (name.equalsIgnoreCase(originalName)) {
                    String value = header.substring(colonIndex + 1);
                    headers.set(i, finalValue + ":" + value);
                    headerFound = true;
                    break;
                }
            }
        }

        if (!headerFound) {
            callbacks.printError("Could not find header '" + originalName + "' to inject into name");
            return request;
        }

        return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
    }

    /**
     * Inject payload into JSON parameter name
     */
    private byte[] injectIntoJsonParameterName(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for JSON parameter name injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));

        // Get the original parameter name (remove __NAME__ suffix)
        String paramName = parameter.getName();
        if (paramName.endsWith("__NAME__")) {
            paramName = paramName.substring(0, paramName.length() - "__NAME__".length());
        }
        String originalName = parameter.getValue(); // The "value" contains the original name

        try {
            // Replace JSON key name
            String modifiedBody = body;

            // Simple approach: replace quoted key names
            String pattern = "\"" + Pattern.quote(originalName) + "\"\\s*:";
            String replacement = "\"" + finalValue + "\":";
            modifiedBody = modifiedBody.replaceAll(pattern, replacement);

            if (!modifiedBody.equals(body)) {
                List<String> headers = requestInfo.getHeaders();
                return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
            } else {
                callbacks.printOutput("Warning: Could not find JSON key '" + originalName + "' to inject into name");
                return request;
            }
        } catch (Exception e) {
            callbacks.printError("Error injecting into JSON parameter name '" + paramName + "': " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into URL path
     */
    private byte[] injectIntoUrlPath(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());
        String requestLine = headers.get(0);

        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            String[] requestLineParts = requestLine.split(" ");
            if (requestLineParts.length >= 2) {
                String method = requestLineParts[0];
                String urlPath = requestLineParts[1];
                String httpVersion = requestLineParts.length > 2 ? requestLineParts[2] : "HTTP/1.1";

                String modifiedPath = urlPath;

                if (paramName.startsWith("URL_PATH[") && paramName.endsWith("]")) {
                    // Individual path segment injection
                    // Extract segment index
                    String indexStr = paramName.substring("URL_PATH[".length(), paramName.length() - 1);
                    try {
                        int segmentIndex = Integer.parseInt(indexStr);
                        modifiedPath = replacePathSegment(urlPath, segmentIndex, finalValue);
                    } catch (NumberFormatException e) {
                        callbacks.printError("Invalid path segment index: " + indexStr);
                        return request;
                    }
                } else if (paramName.startsWith("URL_PATH_FULL[") && paramName.endsWith("]")) {
                    // Full path replacement at specific position
                    modifiedPath = finalValue;
                } else if (paramName.equals("URL_PATH_COMPLETE")) {
                    // Complete path replacement
                    modifiedPath = finalValue;
                } else {
                    // Fallback: replace original value in path
                    modifiedPath = urlPath.replace(originalValue, finalValue);
                }

                // Reconstruct request line
                String newRequestLine = method + " " + modifiedPath + " " + httpVersion;
                headers.set(0, newRequestLine);

                // Rebuild the request
                return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
            } else {
                callbacks.printError("Invalid request line format for URL path injection");
                return request;
            }
        } catch (Exception e) {
            callbacks.printError("Error injecting into URL path '" + paramName + "': " + e.getMessage());
            return request;
        }
    }

    /**
     * Replace a specific path segment with the payload
     */
    private String replacePathSegment(String urlPath, int segmentIndex, String payload) {
        try {
            // Split the path into segments
            String[] parts = urlPath.split("\\?", 2); // Separate path from query string
            String path = parts[0];
            String queryString = parts.length > 1 ? "?" + parts[1] : "";

            String[] segments = path.split("/");

            // Find the actual segment (accounting for empty segments)
            int actualIndex = 0;
            for (int i = 0; i < segments.length; i++) {
                if (!segments[i].isEmpty()) {
                    if (actualIndex == segmentIndex) {
                        segments[i] = payload;
                        break;
                    }
                    actualIndex++;
                }
            }

            // Reconstruct the path
            String newPath = String.join("/", segments);
            return newPath + queryString;
        } catch (Exception e) {
            callbacks.printError("Error replacing path segment: " + e.getMessage());
            return urlPath;
        }
    }

    /**
     * Placeholder for injecting into nested JSON.
     */
     private byte[] injectIntoNestedJson(byte[] request, HttpParameter parameter, String finalValue) {
         // Redirect to the new JSON parameter injection method
         return injectIntoJsonParameter(request, parameter, finalValue);
     }

    /**
     * Inject payload into WebSocket parameters
     */
    private byte[] injectIntoWebSocket(byte[] request, HttpParameter parameter, String finalValue) {
        // WebSocket upgrade requests are HTTP requests, so we can modify headers
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());
        String paramName = parameter.getName();

        // Remove "ws." prefix if present
        if (paramName.startsWith("ws.")) {
            paramName = paramName.substring(3);
        }

        boolean headerFound = false;

        // Find and modify WebSocket-specific headers
        for (int i = 1; i < headers.size(); i++) {
            String header = headers.get(i);
            int colonIndex = header.indexOf(':');
            if (colonIndex > 0) {
                String name = header.substring(0, colonIndex).trim();
                if (name.equalsIgnoreCase(paramName)) {
                    headers.set(i, name + ": " + finalValue);
                    headerFound = true;
                    break;
                }
            }
        }

        if (!headerFound) {
            // Add new WebSocket header
            headers.add(paramName + ": " + finalValue);
        }

        return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
    }

    /**
     * Inject payload into Protocol Buffer/gRPC parameters
     */
    private byte[] injectIntoProtobuf(byte[] request, HttpParameter parameter, String finalValue) {
        // For protobuf, we'll inject into the binary body
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for Protobuf injection");
            return request;
        }

        // For now, replace the entire body with the payload (as binary data)
        // In a real implementation, you'd need proper protobuf parsing
        try {
            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, finalValue.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into Protobuf parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into binary format parameters
     */
    private byte[] injectIntoBinaryFormat(byte[] request, HttpParameter parameter, String finalValue) {
        // For binary formats, we'll inject into the body
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for binary format injection");
            return request;
        }

        String paramName = parameter.getName();

        try {
            // If it's a string parameter extracted from binary data, inject the string
            if (paramName.contains(".string[")) {
                // Replace the entire body with the payload
                List<String> headers = requestInfo.getHeaders();
                return helpers.buildHttpMessage(headers, finalValue.getBytes(StandardCharsets.UTF_8));
            } else {
                // For other binary parameters, inject as-is
                List<String> headers = requestInfo.getHeaders();
                return helpers.buildHttpMessage(headers, finalValue.getBytes(StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            callbacks.printError("Error injecting into binary format parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into Server-Sent Events parameters
     */
    private byte[] injectIntoServerSentEvents(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for SSE injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            String modifiedBody = body;

            // Replace SSE data content
            if (paramName.contains(".data")) {
                modifiedBody = body.replace("data: " + originalValue, "data: " + finalValue);
            } else if (paramName.contains(".type")) {
                modifiedBody = body.replace("event: " + originalValue, "event: " + finalValue);
            } else if (paramName.contains(".id")) {
                modifiedBody = body.replace("id: " + originalValue, "id: " + finalValue);
            } else {
                // Generic replacement
                modifiedBody = body.replace(originalValue, finalValue);
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into SSE parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into JWT token parameters
     */
    private byte[] injectIntoJWT(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        List<String> headers = new ArrayList<>(requestInfo.getHeaders());
        String paramName = parameter.getName();

        // Handle JWT in Authorization header
        if (paramName.contains("auth.bearer") || paramName.contains("auth.jwt")) {
            for (int i = 1; i < headers.size(); i++) {
                String header = headers.get(i);
                if (header.toLowerCase().startsWith("authorization:")) {
                    if (paramName.contains(".token")) {
                        // Replace entire token
                        headers.set(i, "Authorization: Bearer " + finalValue);
                    } else if (paramName.contains(".payload") || paramName.contains(".header")) {
                        // For payload/header injection, we'd need to rebuild the JWT
                        // For now, replace the entire token
                        headers.set(i, "Authorization: Bearer " + finalValue);
                    }
                    break;
                }
            }
        } else {
            // Handle JWT in custom headers
            String headerName = paramName.substring(paramName.indexOf(".") + 1);
            if (headerName.contains(".")) {
                headerName = headerName.substring(0, headerName.indexOf("."));
            }

            for (int i = 1; i < headers.size(); i++) {
                String header = headers.get(i);
                int colonIndex = header.indexOf(':');
                if (colonIndex > 0) {
                    String name = header.substring(0, colonIndex).trim();
                    if (name.equalsIgnoreCase(headerName)) {
                        headers.set(i, name + ": " + finalValue);
                        break;
                    }
                }
            }
        }

        return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
    }

    /**
     * Inject payload into Base64 encoded parameters
     */
    private byte[] injectIntoBase64(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        String requestString = new String(request, StandardCharsets.UTF_8);
        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            String modifiedRequest = requestString;

            if (paramName.contains(".decoded")) {
                // Encode the final value and replace the base64 string
                String encodedValue = java.util.Base64.getEncoder().encodeToString(finalValue.getBytes(StandardCharsets.UTF_8));
                // Find the original base64 string and replace it
                String base64ParamName = paramName.replace(".decoded", "");
                // This is a simplified approach - in practice you'd need to find the exact base64 string
                modifiedRequest = requestString.replace(originalValue, finalValue);
            } else {
                // Direct base64 replacement
                modifiedRequest = requestString.replace(originalValue, finalValue);
            }

            return modifiedRequest.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            callbacks.printError("Error injecting into Base64 parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into configuration format parameters (YAML, TOML, INI)
     */
    private byte[] injectIntoConfigurationFormat(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for configuration format injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            String modifiedBody = body;

            // Handle different configuration formats
            if (paramName.startsWith("yaml.")) {
                // YAML format: key: value
                modifiedBody = body.replace(": " + originalValue, ": " + finalValue);
            } else if (paramName.startsWith("toml.") || paramName.startsWith("ini.")) {
                // TOML/INI format: key = value
                modifiedBody = body.replace("= " + originalValue, "= " + finalValue);
                if (modifiedBody.equals(body)) {
                    modifiedBody = body.replace("=" + originalValue, "=" + finalValue);
                }
            } else {
                // Generic replacement
                modifiedBody = body.replace(originalValue, finalValue);
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into configuration format parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject payload into template format parameters (Handlebars, Jinja2, etc.)
     */
    private byte[] injectIntoTemplateFormat(byte[] request, HttpParameter parameter, String finalValue) {
        IRequestInfo requestInfo = helpers.analyzeRequest(request);
        int bodyOffset = requestInfo.getBodyOffset();

        if (bodyOffset >= request.length) {
            callbacks.printError("No body found for template format injection");
            return request;
        }

        String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
        String paramName = parameter.getName();
        String originalValue = parameter.getValue();

        try {
            String modifiedBody = body;

            // Handle different template formats
            if (paramName.startsWith("handlebars.")) {
                // Handlebars: {{variable}}
                if (paramName.contains("variable")) {
                    modifiedBody = body.replace("{{" + originalValue + "}}", "{{" + finalValue + "}}");
                } else {
                    modifiedBody = body.replace(originalValue, finalValue);
                }
            } else if (paramName.startsWith("jinja2.")) {
                // Jinja2: {{ variable }} or {% control %}
                if (paramName.contains("variable")) {
                    modifiedBody = body.replace("{{ " + originalValue + " }}", "{{ " + finalValue + " }}");
                    if (modifiedBody.equals(body)) {
                        modifiedBody = body.replace("{{" + originalValue + "}}", "{{" + finalValue + "}}");
                    }
                } else if (paramName.contains("control")) {
                    modifiedBody = body.replace("{% " + originalValue + " %}", "{% " + finalValue + " %}");
                } else {
                    modifiedBody = body.replace(originalValue, finalValue);
                }
            } else if (paramName.startsWith("erb.")) {
                // ERB: <%= expression %> or <% code %>
                if (paramName.contains("output")) {
                    modifiedBody = body.replace("<%= " + originalValue + " %>", "<%= " + finalValue + " %>");
                } else if (paramName.contains("code")) {
                    modifiedBody = body.replace("<% " + originalValue + " %>", "<% " + finalValue + " %>");
                } else {
                    modifiedBody = body.replace(originalValue, finalValue);
                }
            } else {
                // Generic replacement
                modifiedBody = body.replace(originalValue, finalValue);
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, modifiedBody.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into template format parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into REST API path parameters with intelligent path segment detection
     */
    private byte[] injectIntoRestPath(byte[] request, HttpParameter parameter, String finalValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());
            String requestLine = headers.get(0);
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            // Handle different REST path parameter types
            if (paramName.startsWith("rest.path[")) {
                // Extract path segment index
                Pattern indexPattern = Pattern.compile("rest\\.path\\[(\\d+)\\]");
                Matcher matcher = indexPattern.matcher(paramName);
                if (matcher.find()) {
                    int segmentIndex = Integer.parseInt(matcher.group(1));
                    String[] requestParts = requestLine.split(" ", 3);
                    if (requestParts.length >= 2) {
                        String path = requestParts[1];
                        String[] segments = path.split("/");
                        if (segmentIndex < segments.length && segments[segmentIndex].equals(originalValue)) {
                            segments[segmentIndex] = finalValue;
                            String newPath = String.join("/", segments);
                            headers.set(0, requestParts[0] + " " + newPath + (requestParts.length > 2 ? " " + requestParts[2] : ""));
                        }
                    }
                }
            } else if (paramName.startsWith("rest.id[")) {
                // Handle ID parameters in path
                if (requestLine.contains("/" + originalValue)) {
                    requestLine = requestLine.replace("/" + originalValue, "/" + finalValue);
                    headers.set(0, requestLine);
                } else if (requestLine.contains(originalValue)) {
                    requestLine = requestLine.replace(originalValue, finalValue);
                    headers.set(0, requestLine);
                }
            } else if (paramName.equals("rest.api_version")) {
                // Handle API version parameters
                Pattern versionPattern = Pattern.compile("/v" + Pattern.quote(originalValue) + "/");
                Matcher matcher = versionPattern.matcher(requestLine);
                if (matcher.find()) {
                    requestLine = matcher.replaceFirst("/v" + finalValue + "/");
                    headers.set(0, requestLine);
                }
            }

            return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
        } catch (Exception e) {
            callbacks.printError("Error injecting into REST path parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into authentication parameters (tokens, API keys, etc.)
     */
    private byte[] injectIntoAuthParameter(byte[] request, HttpParameter parameter, String finalValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            if (paramName.equals("auth.authorization")) {
                // Replace entire Authorization header
                for (int i = 1; i < headers.size(); i++) {
                    if (headers.get(i).toLowerCase().startsWith("authorization:")) {
                        headers.set(i, "Authorization: " + finalValue);
                        break;
                    }
                }
            } else if (paramName.equals("auth.bearer_token")) {
                // Replace just the token part of Bearer authentication
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith("authorization:") && header.contains("Bearer ")) {
                        headers.set(i, "Authorization: Bearer " + finalValue);
                        break;
                    }
                }
            } else if (paramName.equals("auth.basic_credentials")) {
                // Replace Basic authentication credentials
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith("authorization:") && header.contains("Basic ")) {
                        headers.set(i, "Authorization: Basic " + finalValue);
                        break;
                    }
                }
            } else if (paramName.equals("auth.api_key")) {
                // Replace API key in various headers
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i).toLowerCase();
                    if (header.startsWith("x-api-key:") || header.startsWith("api-key:") || header.startsWith("apikey:")) {
                        String headerName = headers.get(i).split(":", 2)[0];
                        headers.set(i, headerName + ": " + finalValue);
                        break;
                    }
                }
            } else if (paramName.startsWith("auth.custom.")) {
                // Handle custom authentication headers
                String headerName = paramName.substring("auth.custom.".length());
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith(headerName.toLowerCase() + ":")) {
                        headers.set(i, headerName + ": " + finalValue);
                        break;
                    }
                }
            }

            return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
        } catch (Exception e) {
            callbacks.printError("Error injecting into authentication parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into structured header parameters (Accept-Language, Cache-Control, etc.)
     */
    private byte[] injectIntoStructuredHeader(byte[] request, HttpParameter parameter, String finalValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            if (paramName.startsWith("accept_language[")) {
                // Handle Accept-Language header modifications
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith("accept-language:")) {
                        String value = header.substring("accept-language:".length()).trim();
                        String newValue = value.replace(originalValue, finalValue);
                        headers.set(i, "Accept-Language: " + newValue);
                        break;
                    }
                }
            } else if (paramName.startsWith("cache_control[")) {
                // Handle Cache-Control header modifications
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith("cache-control:")) {
                        String value = header.substring("cache-control:".length()).trim();
                        String newValue = value.replace(originalValue, finalValue);
                        headers.set(i, "Cache-Control: " + newValue);
                        break;
                    }
                }
            } else if (paramName.startsWith("accept[")) {
                // Handle Accept header modifications
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith("accept:")) {
                        String value = header.substring("accept:".length()).trim();
                        String newValue = value.replace(originalValue, finalValue);
                        headers.set(i, "Accept: " + newValue);
                        break;
                    }
                }
            } else {
                // Generic structured header handling
                String headerName = paramName.split("\\[")[0];
                for (int i = 1; i < headers.size(); i++) {
                    String header = headers.get(i);
                    if (header.toLowerCase().startsWith(headerName.toLowerCase() + ":")) {
                        String value = header.substring(header.indexOf(":") + 1).trim();
                        String newValue = value.replace(originalValue, finalValue);
                        headers.set(i, headerName + ": " + newValue);
                        break;
                    }
                }
            }

            return helpers.buildHttpMessage(headers, Arrays.copyOfRange(request, requestInfo.getBodyOffset(), request.length));
        } catch (Exception e) {
            callbacks.printError("Error injecting into structured header parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into nested form parameters (like param[key]=value)
     */
    private byte[] injectIntoNestedForm(byte[] request, HttpParameter parameter, String finalValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            int bodyOffset = requestInfo.getBodyOffset();

            if (bodyOffset >= request.length) {
                callbacks.printError("No body found for nested form parameter injection");
                return request;
            }

            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            // Handle nested parameter patterns
            if (paramName.contains(".")) {
                // Handle dot notation: param.key
                String[] parts = paramName.split("\\.", 2);
                String baseParam = parts[0];
                String key = parts[1];

                // Look for pattern: baseParam[key]=originalValue
                Pattern pattern = Pattern.compile(Pattern.quote(baseParam) + "\\[" + Pattern.quote(key) + "\\]=" + Pattern.quote(originalValue));
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    body = matcher.replaceFirst(baseParam + "[" + key + "]=" + finalValue);
                }
            } else if (paramName.contains("[") && paramName.contains("]")) {
                // Handle bracket notation: param[key]
                Pattern pattern = Pattern.compile(Pattern.quote(paramName) + "=" + Pattern.quote(originalValue));
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    body = matcher.replaceFirst(paramName + "=" + finalValue);
                }
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, body.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into nested form parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Inject into file upload parameters
     */
    private byte[] injectIntoFileUpload(byte[] request, HttpParameter parameter, String finalValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            int bodyOffset = requestInfo.getBodyOffset();

            if (bodyOffset >= request.length) {
                callbacks.printError("No body found for file upload parameter injection");
                return request;
            }

            String body = new String(Arrays.copyOfRange(request, bodyOffset, request.length));
            String paramName = parameter.getName();
            String originalValue = parameter.getValue();

            if (paramName.startsWith("file.") && paramName.endsWith(".filename")) {
                // Inject into filename
                String fieldName = paramName.substring(5, paramName.length() - 9); // Remove "file." and ".filename"
                Pattern pattern = Pattern.compile("(Content-Disposition:[^\\r\\n]*name=\"" + Pattern.quote(fieldName) + "\"[^\\r\\n]*filename=\")" + Pattern.quote(originalValue) + "(\"[^\\r\\n]*)");
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    body = matcher.replaceFirst("$1" + finalValue + "$2");
                }
            } else if (paramName.startsWith("file.") && paramName.endsWith(".content_type")) {
                // Inject into content type
                String fieldName = paramName.substring(5, paramName.length() - 13); // Remove "file." and ".content_type"
                Pattern pattern = Pattern.compile("(Content-Type:\\s*)" + Pattern.quote(originalValue));
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    body = matcher.replaceFirst("$1" + finalValue);
                }
            } else if (paramName.startsWith("file.")) {
                // Inject into file content
                String fieldName = paramName.substring(5); // Remove "file."

                // Find the file content section
                Pattern sectionPattern = Pattern.compile("(Content-Disposition:[^\\r\\n]*name=\"" + Pattern.quote(fieldName) + "\"[^\\r\\n]*\\r?\\n(?:Content-Type:[^\\r\\n]*\\r?\\n)?\\r?\\n)" + Pattern.quote(originalValue) + "(\\r?\\n--)", Pattern.DOTALL);
                Matcher matcher = sectionPattern.matcher(body);
                if (matcher.find()) {
                    body = matcher.replaceFirst("$1" + finalValue + "$2");
                }
            }

            List<String> headers = requestInfo.getHeaders();
            return helpers.buildHttpMessage(headers, body.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            callbacks.printError("Error injecting into file upload parameter: " + e.getMessage());
            return request;
        }
    }

    /**
     * Enhanced JSON path injection with support for complex nested structures
     */
    private String injectIntoJsonPathEnhanced(String jsonBody, String paramPath, String originalValue, String finalValue) {
        try {
            // Handle different JSON path formats
            if (paramPath.contains("[") && paramPath.contains("]")) {
                // Array notation: items[0].name or data[key]
                return injectIntoJsonArrayPath(jsonBody, paramPath, originalValue, finalValue);
            } else if (paramPath.contains(".")) {
                // Dot notation: user.profile.name
                return injectIntoJsonDotPath(jsonBody, paramPath, originalValue, finalValue);
            } else {
                // Simple key: name
                return injectIntoJsonSimpleKey(jsonBody, paramPath, originalValue, finalValue);
            }
        } catch (Exception e) {
            callbacks.printError("Error in JSON path injection: " + e.getMessage());
            return jsonBody;
        }
    }

    /**
     * Inject into JSON array paths like items[0].name
     */
    private String injectIntoJsonArrayPath(String jsonBody, String paramPath, String originalValue, String finalValue) {
        // Convert array notation to regex pattern
        String regexPath = paramPath.replace("[", "\\[").replace("]", "\\]").replace(".", "\\.");

        // Create pattern to match the JSON structure
        Pattern pattern = Pattern.compile("(\"" + regexPath.split("\\.")[regexPath.split("\\.").length - 1] + "\"\\s*:\\s*\")" + Pattern.quote(originalValue) + "(\")", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(jsonBody);

        if (matcher.find()) {
            return matcher.replaceFirst("$1" + finalValue + "$2");
        }

        return jsonBody;
    }

    /**
     * Inject into JSON dot paths like user.profile.name
     */
    private String injectIntoJsonDotPath(String jsonBody, String paramPath, String originalValue, String finalValue) {
        String[] pathParts = paramPath.split("\\.");
        String lastKey = pathParts[pathParts.length - 1];

        // Create pattern to match the final key-value pair
        Pattern pattern = Pattern.compile("(\"" + Pattern.quote(lastKey) + "\"\\s*:\\s*\")" + Pattern.quote(originalValue) + "(\")", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(jsonBody);

        if (matcher.find()) {
            return matcher.replaceFirst("$1" + finalValue + "$2");
        }

        // Try without quotes for non-string values
        Pattern nonStringPattern = Pattern.compile("(\"" + Pattern.quote(lastKey) + "\"\\s*:\\s*)" + Pattern.quote(originalValue) + "([,}\\]])", Pattern.CASE_INSENSITIVE);
        Matcher nonStringMatcher = nonStringPattern.matcher(jsonBody);

        if (nonStringMatcher.find()) {
            return nonStringMatcher.replaceFirst("$1" + finalValue + "$2");
        }

        return jsonBody;
    }

    /**
     * Inject into simple JSON keys
     */
    private String injectIntoJsonSimpleKey(String jsonBody, String paramPath, String originalValue, String finalValue) {
        // Try string value first
        Pattern stringPattern = Pattern.compile("(\"" + Pattern.quote(paramPath) + "\"\\s*:\\s*\")" + Pattern.quote(originalValue) + "(\")", Pattern.CASE_INSENSITIVE);
        Matcher stringMatcher = stringPattern.matcher(jsonBody);

        if (stringMatcher.find()) {
            return stringMatcher.replaceFirst("$1" + finalValue + "$2");
        }

        // Try non-string value
        Pattern nonStringPattern = Pattern.compile("(\"" + Pattern.quote(paramPath) + "\"\\s*:\\s*)" + Pattern.quote(originalValue) + "([,}\\]])", Pattern.CASE_INSENSITIVE);
        Matcher nonStringMatcher = nonStringPattern.matcher(jsonBody);

        if (nonStringMatcher.find()) {
            return nonStringMatcher.replaceFirst("$1" + finalValue + "$2");
        }

        return jsonBody;
    }

} // End of PayloadInjector class

