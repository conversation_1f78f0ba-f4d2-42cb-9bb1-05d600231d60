package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IMessageEditor;
import com.timebasedscan.model.HttpParameter;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableModel;
import javax.swing.table.TableRowSorter;
import javax.swing.RowFilter;
import java.awt.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Panel for displaying the HTTP request and parameter selection
 */
public class RequestPanel extends JPanel {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private IMessageEditor requestViewer;
    private JTable parameterTable;
    private ParameterTableModel parameterTableModel;
    private IHttpRequestResponse currentRequest;
    private int originalParameterCount = 0; // Track original count before filtering

    public RequestPanel(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        
        initializeUI();
    }

    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Add attribution label at the top right with consistent theme styling
        JLabel attributionLabel = UITheme.createAttributionLabel();
        add(attributionLabel, BorderLayout.NORTH);
        
        // Create request viewer with improved styling
        requestViewer = callbacks.createMessageEditor(null, false);
        
        // Create parameter table with enhanced styling
        parameterTableModel = new ParameterTableModel();
        parameterTable = new JTable(parameterTableModel);
        parameterTable.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        parameterTable.setRowHeight(25); // Increase row height for better readability
        parameterTable.setIntercellSpacing(new Dimension(5, 2)); // Add space between cells
        parameterTable.setShowGrid(true); // Show table grid
        parameterTable.setGridColor(new Color(220, 220, 220)); // Light grid color
        parameterTable.setFillsViewportHeight(true);

        // Enable proper checkbox editing
        parameterTable.putClientProperty("terminateEditOnFocusLost", Boolean.TRUE);

        // Add table model listener to update statistics when selections change
        parameterTableModel.addTableModelListener(e -> {
            if (e.getColumn() == 0) { // Selection column changed
                SwingUtilities.invokeLater(() -> updateSelectionStats());
            }
        });

        // Set custom renderers for specific columns
        parameterTable.getColumnModel().getColumn(0).setMaxWidth(80); // Select column
        parameterTable.getColumnModel().getColumn(3).setPreferredWidth(100); // Type column
        parameterTable.getColumnModel().getColumn(4).setMaxWidth(80); // Empty column
        parameterTable.getColumnModel().getColumn(5).setPreferredWidth(120); // Likelihood column
        
        // Alternating row colors
        parameterTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, 
                                                          boolean isSelected, boolean hasFocus, 
                                                          int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Custom formatting based on content type
                if (column == 5) { // Likelihood column
                    if (value != null) {
                        String text = value.toString();
                        if (text.equals("High")) {
                            c.setForeground(new Color(204, 0, 0)); // Red
                            setFont(getFont().deriveFont(Font.BOLD));
                        } else if (text.equals("Medium-High")) {
                            c.setForeground(new Color(255, 102, 0)); // Orange
                            setFont(getFont().deriveFont(Font.BOLD));
                        } else if (text.equals("Medium")) {
                            c.setForeground(new Color(204, 102, 0)); // Brown-orange
                        } else {
                            c.setForeground(Color.BLACK);
                        }
                    }
                } else if (column == 4) { // Empty column
                    if (value != null && value.toString().equals("Yes")) {
                        setFont(getFont().deriveFont(Font.BOLD));
                    } else {
                        setFont(getFont().deriveFont(Font.PLAIN));
                    }
                    c.setForeground(Color.BLACK);
                } else {
                    setFont(getFont().deriveFont(Font.PLAIN));
                    c.setForeground(Color.BLACK);
                }
                
                // Alternating row colors (except when selected)
                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? Color.WHITE : new Color(240, 240, 250));
                }
                
                return c;
            }
        });
        
        // Create parameter control panel with improved styling
        JPanel paramControlPanel = new JPanel();
        paramControlPanel.setLayout(new BorderLayout());
        
        // Left panel with select/deselect controls
        JPanel selectPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0));
        
        // Create select all checkbox with improved styling
        JCheckBox selectAllCheckbox = new JCheckBox("Select All");
        selectAllCheckbox.setFont(selectAllCheckbox.getFont().deriveFont(Font.BOLD));
        selectAllCheckbox.addActionListener(e -> {
            boolean selected = selectAllCheckbox.isSelected();
            for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
                parameterTableModel.setValueAt(selected, i, 0);
            }
            // Force table refresh and update statistics
            parameterTable.revalidate();
            parameterTable.repaint();
            updateSelectionStats();
        });
        
        // Create enhanced selection buttons with modern styling
        JButton selectEmptyButton = UITheme.createSecondaryButton("Empty");
        selectEmptyButton.setToolTipText("Select only empty parameters (often more vulnerable)");
        selectEmptyButton.addActionListener(e -> selectParametersByType("empty"));

        JButton selectHighRiskButton = UITheme.createSecondaryButton("High Risk");
        selectHighRiskButton.setToolTipText("Select parameters with high likelihood of vulnerability");
        selectHighRiskButton.addActionListener(e -> selectParametersByType("high-risk"));

        JButton selectBodyButton = UITheme.createSecondaryButton("Body");
        selectBodyButton.setToolTipText("Select only body/form parameters");
        selectBodyButton.addActionListener(e -> selectParametersByType("body"));

        JButton selectJsonButton = UITheme.createSecondaryButton("JSON");
        selectJsonButton.setToolTipText("Select only JSON parameters");
        selectJsonButton.addActionListener(e -> selectParametersByType("json"));

        JButton selectUrlButton = UITheme.createSecondaryButton("URL");
        selectUrlButton.setToolTipText("Select only URL query parameters");
        selectUrlButton.addActionListener(e -> selectParametersByType("url"));

        JButton selectHeaderButton = UITheme.createSecondaryButton("Headers");
        selectHeaderButton.setToolTipText("Select only header parameters");
        selectHeaderButton.addActionListener(e -> selectParametersByType("header"));

        JButton selectCookieButton = UITheme.createSecondaryButton("Cookies");
        selectCookieButton.setToolTipText("Select only cookie parameters");
        selectCookieButton.addActionListener(e -> selectParametersByType("cookie"));

        // Smart selection button
        JButton smartSelectButton = UITheme.createPrimaryButton("Smart Select");
        smartSelectButton.setToolTipText("Intelligently select the most promising parameters for testing");
        smartSelectButton.addActionListener(e -> smartParameterSelection());
        
        // Add selection controls to panel in organized groups
        selectPanel.add(selectAllCheckbox);
        selectPanel.add(Box.createHorizontalStrut(10));

        // Quick selection group
        selectPanel.add(new JLabel("Quick:"));
        selectPanel.add(selectEmptyButton);
        selectPanel.add(selectHighRiskButton);
        selectPanel.add(smartSelectButton);
        selectPanel.add(Box.createHorizontalStrut(10));

        // Type selection group
        selectPanel.add(new JLabel("Type:"));
        selectPanel.add(selectBodyButton);
        selectPanel.add(selectJsonButton);
        selectPanel.add(selectUrlButton);
        selectPanel.add(selectHeaderButton);
        selectPanel.add(selectCookieButton);
        
        // Search field for filtering parameters
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        JLabel searchLabel = new JLabel("Search: ");
        JTextField searchField = new JTextField(15);
        searchField.setToolTipText("Filter parameters by name or type");
        
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            @Override
            public void insertUpdate(javax.swing.event.DocumentEvent e) { filterTable(); }
            
            @Override
            public void removeUpdate(javax.swing.event.DocumentEvent e) { filterTable(); }
            
            @Override
            public void changedUpdate(javax.swing.event.DocumentEvent e) { filterTable(); }
            
            private void filterTable() {
                String text = searchField.getText().toLowerCase();
                TableRowSorter<ParameterTableModel> sorter = 
                    new TableRowSorter<>(parameterTableModel);
                parameterTable.setRowSorter(sorter);
                
                if (text.trim().length() == 0) {
                    sorter.setRowFilter(null);
                } else {
                    // Match against name (col 1) or type (col 3)
                    sorter.setRowFilter(RowFilter.orFilter(Arrays.asList(
                        RowFilter.regexFilter("(?i)" + text, 1),
                        RowFilter.regexFilter("(?i)" + text, 3)
                    )));
                }
            }
        });
        
        // Add search to panel
        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        
        // Add both panels to control panel
        paramControlPanel.add(selectPanel, BorderLayout.WEST);
        paramControlPanel.add(searchPanel, BorderLayout.EAST);
        
        // Add enhanced parameter statistics panel
        JPanel statsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 5));
        statsPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        JLabel paramCountLabel = new JLabel(" 0 parameters found");
        paramCountLabel.setFont(paramCountLabel.getFont().deriveFont(Font.BOLD));

        JLabel selectedCountLabel = new JLabel(" 0 selected");
        selectedCountLabel.setFont(selectedCountLabel.getFont().deriveFont(Font.ITALIC));
        selectedCountLabel.setForeground(new Color(0, 120, 0)); // Green color

        JLabel emptyCountLabel = new JLabel(" 0 empty");
        emptyCountLabel.setFont(emptyCountLabel.getFont().deriveFont(Font.ITALIC));
        emptyCountLabel.setForeground(new Color(200, 100, 0)); // Orange color

        JLabel highRiskCountLabel = new JLabel(" 0 high-risk");
        highRiskCountLabel.setFont(highRiskCountLabel.getFont().deriveFont(Font.ITALIC));
        highRiskCountLabel.setForeground(new Color(180, 0, 0)); // Red color

        JLabel filteredCountLabel = new JLabel(" 0 filtered");
        filteredCountLabel.setFont(filteredCountLabel.getFont().deriveFont(Font.ITALIC));
        filteredCountLabel.setForeground(new Color(120, 120, 120)); // Gray color
        filteredCountLabel.setToolTipText("Parameters filtered out (headers, synthetic parameters, etc.)");

        statsPanel.add(paramCountLabel);
        statsPanel.add(new JSeparator(JSeparator.VERTICAL));
        statsPanel.add(selectedCountLabel);
        statsPanel.add(new JSeparator(JSeparator.VERTICAL));
        statsPanel.add(emptyCountLabel);
        statsPanel.add(new JSeparator(JSeparator.VERTICAL));
        statsPanel.add(highRiskCountLabel);
        statsPanel.add(new JSeparator(JSeparator.VERTICAL));
        statsPanel.add(filteredCountLabel);

        // Update the parameter statistics when they change
        parameterTableModel.addTableModelListener(e -> {
            SwingUtilities.invokeLater(() -> {
                updateParameterStats(paramCountLabel, selectedCountLabel, emptyCountLabel, highRiskCountLabel, filteredCountLabel);
            });
        });

        // Also update stats when table selection changes
        parameterTable.getModel().addTableModelListener(e -> {
            SwingUtilities.invokeLater(() -> {
                updateParameterStats(paramCountLabel, selectedCountLabel, emptyCountLabel, highRiskCountLabel, filteredCountLabel);
            });
        });
        
        // Layout components in a more appealing way
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        
        // Request viewer panel with modern styling
        JPanel requestViewerPanel = new JPanel(new BorderLayout());
        requestViewerPanel.setBorder(UITheme.createPanelBorder("HTTP Request"));
        requestViewerPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        requestViewerPanel.add(requestViewer.getComponent(), BorderLayout.CENTER);
        
        // Parameter panel with modern styling
        JPanel parameterPanel = new JPanel(new BorderLayout());
        parameterPanel.setBorder(UITheme.createPanelBorder("Parameters"));
        parameterPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        
        // Add components to parameter panel
        parameterPanel.add(paramControlPanel, BorderLayout.NORTH);
        parameterPanel.add(new JScrollPane(parameterTable), BorderLayout.CENTER);
        parameterPanel.add(statsPanel, BorderLayout.SOUTH);
        
        // Configure split pane
        splitPane.setTopComponent(requestViewerPanel);
        splitPane.setBottomComponent(parameterPanel);
        splitPane.setResizeWeight(0.6);
        
        // Add to main panel
        add(splitPane, BorderLayout.CENTER);
    }

    public void setRequest(IHttpRequestResponse requestResponse) {
        this.currentRequest = requestResponse;
        
        if (requestResponse != null) {
            requestViewer.setMessage(requestResponse.getRequest(), true);
        } else {
            requestViewer.setMessage(new byte[0], true);
        }
    }

    public void setParameters(List<HttpParameter> parameters) {
        parameterTableModel.setParameters(parameters);
    }

    public void setParameters(List<HttpParameter> parameters, boolean includeNameInjection) {
        setParameters(parameters, includeNameInjection, false);
    }

    public void setParameters(List<HttpParameter> parameters, boolean includeNameInjection, boolean includeUrlPathInjection) {
        setParameters(parameters, includeNameInjection, includeUrlPathInjection, false);
    }

    public void setParameters(List<HttpParameter> parameters, boolean includeNameInjection, boolean includeUrlPathInjection, boolean includeHeaderParameters) {
        // Track original count before filtering
        originalParameterCount = parameters.size();

        // Filter parameters based on settings
        List<HttpParameter> filteredParameters = new ArrayList<>();
        int filteredCount = 0;

        for (HttpParameter param : parameters) {
            boolean shouldInclude = true;

            // Always include basic parameter types (URL, BODY, COOKIE, JSON, XML, MULTIPART)
            boolean isBasicParameterType = param.getType() == HttpParameter.Type.URL ||
                                         param.getType() == HttpParameter.Type.BODY ||
                                         param.getType() == HttpParameter.Type.COOKIE ||
                                         param.getType() == HttpParameter.Type.JSON ||
                                         param.getType() == HttpParameter.Type.XML ||
                                         param.getType() == HttpParameter.Type.MULTIPART;

            // Filter out __NAME__ parameters if name injection is disabled
            if (param.getName().endsWith("__NAME__") && !includeNameInjection) {
                shouldInclude = false;
                filteredCount++;
            }

            // Filter out URL_PATH parameters if URL path injection is disabled (but only if not a basic type)
            else if (param.getType() == HttpParameter.Type.URL_PATH && !includeUrlPathInjection && !isBasicParameterType) {
                shouldInclude = false;
                filteredCount++;
            }

            // Filter out HEADER parameters if header parameters are disabled (but only if not a basic type)
            else if (param.getType() == HttpParameter.Type.HEADER && !includeHeaderParameters && !isBasicParameterType) {
                shouldInclude = false;
                filteredCount++;
                callbacks.printOutput("Filtering out header parameter: " + param.getName() + " (enable 'Include Header Parameters' to test headers)");
            }

            // Filter out problematic synthetic parameters that cause injection errors
            else if (param.getName().startsWith("__EXTRACTED") ||
                (param.getName().equals("__BODY__") && param.getType() == HttpParameter.Type.BODY)) {
                shouldInclude = false;
                filteredCount++;
                callbacks.printOutput("Filtering out problematic synthetic parameter: " + param.getName());
            }

            if (shouldInclude) {
                filteredParameters.add(param);
            }
        }

        parameterTableModel.setParameters(filteredParameters);

        // Log filtering summary
        if (filteredCount > 0) {
            callbacks.printOutput("Parameter filtering: " + filteredParameters.size() + " parameters shown, " +
                                filteredCount + " filtered out (" + originalParameterCount + " total detected)");
        }
    }

    public List<HttpParameter> getSelectedParameters() {
        return parameterTableModel.getSelectedParameters();
    }
    
    /**
     * Get the currently highlighted parameter in the table
     * @return The highlighted parameter or null if none is selected
     */
    public HttpParameter getHighlightedParameter() {
        int selectedRow = parameterTable.getSelectedRow();
        if (selectedRow >= 0 && selectedRow < parameterTableModel.getRowCount()) {
            return parameterTableModel.getParameterAt(selectedRow);
        }
        return null;
    }
    
    /**
     * Select all parameters in the table
     * Used for quick scanning functionality
     */
    public void selectAllParameters() {
        if (parameterTableModel != null) {
            for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
                parameterTableModel.setValueAt(true, i, 0);
            }
            // Force table refresh and update statistics
            parameterTable.revalidate();
            parameterTable.repaint();
            updateSelectionStats();
        }
    }
    
    /**
     * Select only empty parameters
     * Used for targeted quick scanning functionality
     */
    public void selectOnlyEmptyParameters() {
        if (parameterTableModel != null) {
            // First deselect all
            for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
                parameterTableModel.setValueAt(false, i, 0);
            }

            // Then select only empty parameters
            for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
                Object isEmpty = parameterTableModel.getValueAt(i, 4); // Empty column
                if (isEmpty != null && "Yes".equals(isEmpty.toString())) {
                    parameterTableModel.setValueAt(true, i, 0);
                }
            }

            // Force table refresh and update statistics
            parameterTable.revalidate();
            parameterTable.repaint();
            updateSelectionStats();
        }
    }

    /**
     * Enhanced parameter selection by type or criteria
     */
    private void selectParametersByType(String selectionType) {
        if (parameterTableModel == null) return;

        // First deselect all
        for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
            parameterTableModel.setValueAt(false, i, 0);
        }

        // Then select based on criteria
        for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
            boolean shouldSelect = false;

            switch (selectionType.toLowerCase()) {
                case "empty":
                    Object isEmpty = parameterTableModel.getValueAt(i, 4); // Empty column
                    shouldSelect = isEmpty != null && "Yes".equals(isEmpty.toString());
                    break;

                case "high-risk":
                    Object likelihood = parameterTableModel.getValueAt(i, 5); // Likelihood column
                    shouldSelect = likelihood != null &&
                        (likelihood.toString().equals("High") || likelihood.toString().equals("Medium-High"));
                    break;

                case "body":
                    Object bodyType = parameterTableModel.getValueAt(i, 3); // Type column
                    shouldSelect = bodyType != null && bodyType.toString().equalsIgnoreCase("BODY");
                    break;

                case "json":
                    Object jsonType = parameterTableModel.getValueAt(i, 3); // Type column
                    shouldSelect = jsonType != null &&
                        (jsonType.toString().equalsIgnoreCase("JSON") || jsonType.toString().contains("JSON"));
                    break;

                case "url":
                    Object urlType = parameterTableModel.getValueAt(i, 3); // Type column
                    shouldSelect = urlType != null && urlType.toString().equalsIgnoreCase("URL");
                    break;

                case "header":
                    Object headerType = parameterTableModel.getValueAt(i, 3); // Type column
                    shouldSelect = headerType != null && headerType.toString().toLowerCase().contains("header");
                    break;

                case "cookie":
                    Object cookieType = parameterTableModel.getValueAt(i, 3); // Type column
                    shouldSelect = cookieType != null && cookieType.toString().equalsIgnoreCase("COOKIE");
                    break;
            }

            if (shouldSelect) {
                parameterTableModel.setValueAt(true, i, 0);
            }
        }

        // Force table refresh and update statistics
        parameterTable.revalidate();
        parameterTable.repaint();
        updateSelectionStats();
    }

    /**
     * Smart parameter selection based on vulnerability likelihood and best practices
     */
    private void smartParameterSelection() {
        if (parameterTableModel == null) return;

        // First deselect all
        for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
            parameterTableModel.setValueAt(false, i, 0);
        }

        // Smart selection criteria (in order of priority)
        for (int i = 0; i < parameterTableModel.getRowCount(); i++) {
            boolean shouldSelect = false;

            Object isEmpty = parameterTableModel.getValueAt(i, 4); // Empty column
            Object likelihood = parameterTableModel.getValueAt(i, 5); // Likelihood column
            Object type = parameterTableModel.getValueAt(i, 3); // Type column
            Object name = parameterTableModel.getValueAt(i, 1); // Name column

            // Priority 1: Empty parameters (highest priority)
            if (isEmpty != null && "Yes".equals(isEmpty.toString())) {
                shouldSelect = true;
            }
            // Priority 2: High/Medium-High likelihood parameters
            else if (likelihood != null &&
                    (likelihood.toString().equals("High") || likelihood.toString().equals("Medium-High"))) {
                shouldSelect = true;
            }
            // Priority 3: Common vulnerable parameter types
            else if (type != null) {
                String typeStr = type.toString().toLowerCase();
                if (typeStr.contains("body") || typeStr.contains("json") || typeStr.contains("url")) {
                    shouldSelect = true;
                }
            }
            // Priority 4: Common vulnerable parameter names
            else if (name != null) {
                String nameStr = name.toString().toLowerCase();
                if (nameStr.contains("id") || nameStr.contains("user") || nameStr.contains("search") ||
                    nameStr.contains("query") || nameStr.contains("cmd") || nameStr.contains("exec") ||
                    nameStr.contains("sql") || nameStr.contains("db")) {
                    shouldSelect = true;
                }
            }

            if (shouldSelect) {
                parameterTableModel.setValueAt(true, i, 0);
            }
        }

        // Force table refresh and update statistics
        parameterTable.revalidate();
        parameterTable.repaint();
        updateSelectionStats();

        // Show selection summary
        int selectedCount = getSelectedParameters().size();
        callbacks.printOutput("Smart Selection: Selected " + selectedCount + " promising parameters for testing");
    }

    /**
     * Update selection statistics display
     */
    private void updateSelectionStats() {
        if (parameterTableModel == null) return;

        int totalParams = parameterTableModel.getRowCount();
        int selectedParams = getSelectedParameters().size();
        int emptyParams = 0;
        int highRiskParams = 0;

        // Count parameter types
        for (int i = 0; i < totalParams; i++) {
            Object isEmpty = parameterTableModel.getValueAt(i, 4);
            Object likelihood = parameterTableModel.getValueAt(i, 5);

            if (isEmpty != null && "Yes".equals(isEmpty.toString())) {
                emptyParams++;
            }
            if (likelihood != null &&
                (likelihood.toString().equals("High") || likelihood.toString().equals("Medium-High"))) {
                highRiskParams++;
            }
        }

        callbacks.printOutput("Parameter Selection Stats: " + selectedParams + "/" + totalParams +
                            " selected (" + emptyParams + " empty, " + highRiskParams + " high-risk)");
    }

    /**
     * Update parameter statistics labels
     */
    private void updateParameterStats(JLabel paramCountLabel, JLabel selectedCountLabel,
                                    JLabel emptyCountLabel, JLabel highRiskCountLabel, JLabel filteredCountLabel) {
        if (parameterTableModel == null) return;

        int totalParams = parameterTableModel.getRowCount();
        int selectedParams = getSelectedParameters().size();
        int emptyParams = 0;
        int highRiskParams = 0;
        int filteredParams = originalParameterCount - totalParams;

        // Count parameter types
        for (int i = 0; i < totalParams; i++) {
            Object isEmpty = parameterTableModel.getValueAt(i, 4);
            Object likelihood = parameterTableModel.getValueAt(i, 5);

            if (isEmpty != null && "Yes".equals(isEmpty.toString())) {
                emptyParams++;
            }
            if (likelihood != null &&
                (likelihood.toString().equals("High") || likelihood.toString().equals("Medium-High"))) {
                highRiskParams++;
            }
        }

        // Update labels
        paramCountLabel.setText(" " + totalParams + " parameters shown");
        selectedCountLabel.setText(" " + selectedParams + " selected");
        emptyCountLabel.setText(" " + emptyParams + " empty");
        highRiskCountLabel.setText(" " + highRiskParams + " high-risk");
        filteredCountLabel.setText(" " + filteredParams + " filtered");

        // Update tooltip with more details
        if (filteredParams > 0) {
            filteredCountLabel.setToolTipText(filteredParams + " parameters filtered out (headers, synthetic, etc.). " +
                                            "Total detected: " + originalParameterCount);
        } else {
            filteredCountLabel.setToolTipText("No parameters filtered out");
        }
    }

    /**
     * Enhanced table model for displaying parameters with better organization and formatting
     */
    private class ParameterTableModel extends AbstractTableModel {
        
        private List<HttpParameter> parameters = new ArrayList<>();
        private List<Boolean> selected = new ArrayList<>();
        private final String[] columnNames = {"Selected", "Name", "Value", "Type", "Empty", "Likelihood"};
        
        public void setParameters(List<HttpParameter> parameters) {
            System.out.println("=== ParameterTableModel.setParameters() ===");
            System.out.println("Setting " + parameters.size() + " parameters in table model");

            this.parameters = new ArrayList<>(parameters);
            this.selected = new ArrayList<>();

            // Initially select all parameters
            for (int i = 0; i < parameters.size(); i++) {
                selected.add(true);
            }

            // Sort parameters for better display (empty parameters first, then by type)
            sortParameters();

            // Fire table data changed and ensure UI updates
            fireTableDataChanged();

            // Update selection statistics after table is refreshed
            SwingUtilities.invokeLater(() -> updateSelectionStats());
        }
        
        /**
         * Sort parameters for better user experience
         */
        private void sortParameters() {
            // Sort parameters - empty first, then by type priority, then by name
            parameters.sort((p1, p2) -> {
                // Empty parameters first
                if (p1.isEmpty() != p2.isEmpty()) {
                    return p1.isEmpty() ? -1 : 1;
                }
                
                // Then by parameter type
                int typePriority1 = getParameterTypePriority(p1.getType().toString());
                int typePriority2 = getParameterTypePriority(p2.getType().toString());
                if (typePriority1 != typePriority2) {
                    return Integer.compare(typePriority1, typePriority2);
                }
                
                // Finally by name
                return p1.getName().compareTo(p2.getName());
            });
            
            // Reorder the selected list to match
            List<Boolean> newSelected = new ArrayList<>(parameters.size());
            for (int i = 0; i < parameters.size(); i++) {
                newSelected.add(true); // Keep all selected after sorting
            }
            selected = newSelected;
        }
        
        /**
         * Get priority score for parameter type (lower is higher priority)
         */
        private int getParameterTypePriority(String paramType) {
            if (paramType == null) return 999;
            
            switch (paramType) {
                case "BODY": return 10;     // Form parameters (high priority)
                case "JSON": return 20;     // JSON parameters
                case "URL": return 30;      // URL query parameters
                case "XML": return 40;      // XML parameters
                case "REST_ID": return 50;  // REST API parameters
                case "RESOURCE_ID": return 60; // Resource IDs
                case "PATH": return 70;     // Path parameters
                case "HEADER_AUTH": return 80; // Auth headers
                case "HEADER": return 90;   // Other headers
                case "COOKIE": return 100;  // Cookies (lower priority)
                case "ENCODED": return 110; // Encoded parameters
                case "GRAPHQL": return 120; // GraphQL
                case "NESTED_JSON": return 130; // Nested JSON
                default: return 500;        // Other types
            }
        }
        
        /**
         * Get injectable likelihood score based on parameter type and emptiness
         */
        private String getInjectableLikelihood(HttpParameter parameter) {
            // Empty parameters are often more vulnerable
            if (parameter.isEmpty()) {
                return "High";
            }
            
            // Assess by type
            HttpParameter.Type type = parameter.getType();
            if (type == null) return "Unknown";
            
            if (type == HttpParameter.Type.BODY || type == HttpParameter.Type.URL || type == HttpParameter.Type.JSON) {
                return "Medium-High";
            } else if (type == HttpParameter.Type.XML) {
                return "Medium";
            } else if (type == HttpParameter.Type.HEADER) {
                return "Medium-Low";
            } else {
                return "Low";
            }
        }
        
        /**
         * Get display name for parameter (clean up __NAME__ suffix)
         */
        private String getDisplayName(HttpParameter parameter) {
            String name = parameter.getName();
            if (name.endsWith("__NAME__")) {
                // Remove __NAME__ suffix and add indicator
                String baseName = name.substring(0, name.length() - "__NAME__".length());
                return baseName + " [NAME]";
            }
            return name;
        }

        /**
         * Get formatted display value for parameter type
         */
        private String getFormattedType(String paramType) {
            if (paramType == null) return "Unknown";

            switch (paramType) {
                case "REST_ID": return "REST API ID";
                case "RESOURCE_ID": return "Resource ID";
                case "HEADER_AUTH": return "Auth Header";
                case "ENCODED": return "Encoded Data";
                case "NESTED_JSON": return "Nested JSON";
                default: return paramType;
            }
        }
        
        public List<HttpParameter> getSelectedParameters() {
            List<HttpParameter> selectedParams = new ArrayList<>();
            
            for (int i = 0; i < parameters.size(); i++) {
                if (selected.get(i)) {
                    selectedParams.add(parameters.get(i));
                }
            }
            
            return selectedParams;
        }
        
        /**
         * Get the parameter at the specified row
         * @param row The row index
         * @return The parameter at the specified row
         */
        public HttpParameter getParameterAt(int row) {
            if (row >= 0 && row < parameters.size()) {
                return parameters.get(row);
            }
            return null;
        }
        
        @Override
        public int getRowCount() {
            return parameters.size();
        }
        
        @Override
        public int getColumnCount() {
            return columnNames.length;
        }
        
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (rowIndex >= parameters.size()) {
                return null;
            }
            
            HttpParameter parameter = parameters.get(rowIndex);
            
            switch (columnIndex) {
                case 0: return selected.get(rowIndex);
                case 1: return getDisplayName(parameter);
                case 2: return parameter.getValue();
                case 3: return getFormattedType(parameter.getType().toString());
                case 4: return parameter.isEmpty() ? "Yes" : "No";
                case 5: return getInjectableLikelihood(parameter);
                default: return null;
            }
        }
        
        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }
        
        @Override
        public Class<?> getColumnClass(int columnIndex) {
            if (columnIndex == 0) {
                return Boolean.class;
            }
            return String.class;
        }
        
        @Override
        public boolean isCellEditable(int rowIndex, int columnIndex) {
            return columnIndex == 0;
        }
        
        @Override
        public void setValueAt(Object aValue, int rowIndex, int columnIndex) {
            if (columnIndex == 0 && aValue instanceof Boolean && rowIndex < selected.size()) {
                Boolean newValue = (Boolean) aValue;
                Boolean oldValue = selected.get(rowIndex);

                // Only update if value actually changed
                if (!newValue.equals(oldValue)) {
                    selected.set(rowIndex, newValue);
                    fireTableCellUpdated(rowIndex, columnIndex);

                    // Update selection statistics
                    SwingUtilities.invokeLater(() -> updateSelectionStats());
                }
            }
        }
    }
}
