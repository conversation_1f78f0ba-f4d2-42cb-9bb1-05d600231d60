package com.timebasedscan.scanner;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.utils.PayloadInjector;
import com.timebasedscan.utils.SqlErrorDetector;
import com.timebasedscan.utils.RateLimitBypass;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Implements time-based testing for a single parameter
 */
public class TimeBasedTest {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private IHttpRequestResponse baseRequestResponse;
    private HttpParameter parameter;
    private String payload;
    private PayloadInjector injector;
    private Object mainPanel;
    private RateLimitBypass rateLimitBypass; // Reference to MainPanel for history tracking

    // Baseline caching to avoid redundant measurements
    private static final Map<String, Long> baselineCache = new ConcurrentHashMap<>();
    private static final Map<String, Long> baselineCacheTimestamp = new ConcurrentHashMap<>();
    private static final long BASELINE_CACHE_TTL = 300000; // 5 minutes cache TTL

    // Optimized thresholds for efficient time-based detection (in milliseconds)
    private static final int TIME_THRESHOLD = 3000; // Minimum absolute delay for generic check
    private static final int MIN_BASELINE_RUNS = 3; // Reduced baseline runs for efficiency
    private static final int MAX_BASELINE_RUNS = 5; // Reduced maximum baseline runs
    private static final double VARIANCE_THRESHOLD = 0.2; // Relaxed variance threshold for efficiency
    private static final double MIN_DELAY_FACTOR = 2.5; // Minimum factor for generic check
    private static final int ABSOLUTE_MIN_DELAY = 1000; // Absolute minimum delay difference
    private static final double CONFIDENCE_THRESHOLD = 0.95; // Statistical confidence threshold
    private static final int MIN_PAYLOAD_TESTS = 3; // Minimum number of payload tests for validation
    private static final int MAX_PAYLOAD_TESTS = 5; // Maximum number of payload tests

    // Enhanced detection parameters - More practical thresholds for real-world scenarios
    private static final double STATISTICAL_SIGNIFICANCE = 0.05; // p-value threshold (95% confidence) - More practical
    private static final double EFFECT_SIZE_THRESHOLD = 0.8; // Cohen's d threshold for medium-large effect - More sensitive
    private static final int CONFIRMATION_TESTS = 1; // Reduced confirmation tests for efficiency
    private static final double NETWORK_STABILITY_THRESHOLD = 0.2; // 20% network stability requirement - More tolerant
    private static final double TEMPORAL_CONSISTENCY_THRESHOLD = 0.7; // 70% temporal consistency requirement - More practical
    private static final int ADAPTIVE_BASELINE_WINDOW = 20; // Adaptive baseline measurement window

    public TimeBasedTest(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                        IHttpRequestResponse baseRequestResponse, HttpParameter parameter,
                        String payload, PayloadInjector injector) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameter = parameter;
        this.payload = payload;
        this.injector = injector;
        this.mainPanel = null; // Will be set by ParallelScanner if needed
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);

        // Configure advanced payload injection (disabled by default - user must enable)
        this.injector.configureAdvancedInjection(
            false,  // Disable encoding bypass by default
            false,  // Disable WAF evasion by default
            false,  // Disable context-aware injection by default
            false   // Disable multiple encoding layers by default
        );
    }

    public TimeBasedTest(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                        IHttpRequestResponse baseRequestResponse, HttpParameter parameter,
                        String payload, PayloadInjector injector, Object mainPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.baseRequestResponse = baseRequestResponse;
        this.parameter = parameter;
        this.payload = payload;
        this.injector = injector;
        this.mainPanel = mainPanel;
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);

        // Configure advanced payload injection (disabled by default - user must enable)
        this.injector.configureAdvancedInjection(
            false,  // Disable encoding bypass by default
            false,  // Disable WAF evasion by default
            false,  // Disable context-aware injection by default
            false   // Disable multiple encoding layers by default
        );
    }

    /**
     * Configure rate limiting bypass settings
     */
    public void configureRateLimitBypass(boolean enabled, String bypassMethod, int baseDelay,
                                       boolean randomizeUserAgents, boolean randomizeXForwardedFor,
                                       boolean useProxyRotation) {
        if (rateLimitBypass != null) {
            rateLimitBypass.configure(enabled, bypassMethod, baseDelay,
                                    randomizeUserAgents, randomizeXForwardedFor, useProxyRotation);
        }
    }

    /**
     * Configure advanced payload injection settings
     */
    public void configureAdvancedPayloadInjection(boolean enableEncodingBypass, boolean enableWafEvasion,
                                                 boolean enableContextAwareInjection, boolean enableMultipleEncodingLayers) {
        if (injector != null) {
            injector.configureAdvancedInjection(enableEncodingBypass, enableWafEvasion,
                                              enableContextAwareInjection, enableMultipleEncodingLayers);
        }
    }

    /**
     * Configure custom header injection settings
     */
    public void configureCustomHeaderInjection(boolean enabled, String headerName, String headerValue) {
        if (injector != null) {
            injector.configureCustomHeaderInjection(enabled, headerName, headerValue);
        }
    }

    /**
     * Apply header randomization to the HTTP request if rate limiting bypass is enabled
     */
    private byte[] applyHeaderRandomization(byte[] request) {
        if (rateLimitBypass == null || !rateLimitBypass.isEnabled()) {
            return request;
        }

        try {
            // Parse the request to get headers
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());
            boolean headersModified = false;

            // Randomize User-Agent if enabled
            String randomUserAgent = rateLimitBypass.getRandomUserAgent();
            if (randomUserAgent != null) {
                // Remove existing User-Agent header
                headers.removeIf(header -> header.toLowerCase().startsWith("user-agent:"));
                // Add randomized User-Agent
                headers.add("User-Agent: " + randomUserAgent);
                headersModified = true;
                callbacks.printOutput("Rate Limiting Bypass: Applied User-Agent: " + randomUserAgent);
            }

            // Randomize X-Forwarded-For if enabled
            String randomXForwardedFor = rateLimitBypass.getRandomXForwardedFor();
            if (randomXForwardedFor != null) {
                // Remove existing X-Forwarded-For header
                headers.removeIf(header -> header.toLowerCase().startsWith("x-forwarded-for:"));
                // Add randomized X-Forwarded-For
                headers.add("X-Forwarded-For: " + randomXForwardedFor);
                headersModified = true;
                callbacks.printOutput("Rate Limiting Bypass: Applied X-Forwarded-For: " + randomXForwardedFor);
            }

            // If headers were modified, rebuild the request
            if (headersModified) {
                byte[] body = null;
                int bodyOffset = requestInfo.getBodyOffset();
                if (bodyOffset < request.length) {
                    body = new byte[request.length - bodyOffset];
                    System.arraycopy(request, bodyOffset, body, 0, body.length);
                }

                return helpers.buildHttpMessage(headers, body);
            }

        } catch (Exception e) {
            callbacks.printError("Error applying header randomization: " + e.getMessage());
        }

        return request;
    }

    /**
     * Execute the enhanced time-based test with multiple validations
     */
    public TestResult execute() {
        long baselineResponseTime = measureBaselineResponseTime();

        if (baselineResponseTime < 0) {
             callbacks.printError("Baseline measurement failed for parameter: " + parameter.getName() + ". Aborting test.");
             return new TestResult(
                 parameter.getName(),
                 parameter.getType().toString(),
                 payload,
                 "Error - Baseline Failed",
                 "Could not establish a stable baseline response time.",
                 baseRequestResponse.getRequest(),
                 baseRequestResponse.getResponse(),
                 -1,
                 -1
             );
        }

        // Perform multiple payload tests for validation
        List<Long> payloadResponseTimes = new ArrayList<>();
        List<IHttpRequestResponse> responses = new ArrayList<>();

        byte[] baseRequest = baseRequestResponse.getRequest();
        IHttpService service = baseRequestResponse.getHttpService();

        callbacks.printOutput("=== Optimized Time-Based Test ===");
        callbacks.printOutput("Parameter: " + parameter.getName() + " (" + parameter.getType() + ")");
        callbacks.printOutput("Payload: " + payload);
        callbacks.printOutput("Baseline: " + baselineResponseTime + "ms (cached: " + (baselineCache.containsKey(createBaselineCacheKey()) ? "yes" : "no") + ")");

        // SQL error detection tracking
        boolean sqlErrorDetected = false;
        String sqlErrorType = null;
        String sqlErrorMessage = null;
        String sqlErrorSnippet = null;

        // Perform multiple payload tests
        for (int i = 0; i < MIN_PAYLOAD_TESTS; i++) {
            try {
                // Apply rate limiting bypass delay before request
                if (rateLimitBypass != null && rateLimitBypass.isEnabled()) {
                    rateLimitBypass.applyDelay();
                }

                byte[] modifiedRequest = injector.injectPayload(baseRequest, parameter, payload);

                // Apply header randomization if enabled
                modifiedRequest = applyHeaderRandomization(modifiedRequest);

                long startTime = System.currentTimeMillis();
                IHttpRequestResponse response = callbacks.makeHttpRequest(service, modifiedRequest);
                long endTime = System.currentTimeMillis();
                long responseTime = endTime - startTime;

                if (response != null && response.getResponse() != null) {
                    // Track this request in history if mainPanel is available
                    trackRequest(response, parameter, payload, responseTime, "Payload Test #" + (i + 1));

                    // Check for SQL errors in response
                    SqlErrorDetector.SqlErrorResult errorResult = SqlErrorDetector.detectSqlError(response.getResponse());
                    if (errorResult.isErrorDetected()) {
                        sqlErrorDetected = true;
                        sqlErrorType = errorResult.getDatabaseType();
                        sqlErrorMessage = errorResult.getErrorMessage();
                        sqlErrorSnippet = errorResult.getErrorSnippet();

                        callbacks.printOutput("Payload test #" + (i + 1) + ": " + responseTime + "ms (SQL ERROR DETECTED: " +
                                            errorResult.getDatabaseType() + ")");
                    } else {
                        // Check for error indicators that might suggest false positives
                        if (hasErrorIndicators(response)) {
                            callbacks.printOutput("Payload test #" + (i + 1) + ": " + responseTime + "ms (ERROR INDICATORS DETECTED)");
                            // Still add the time but flag it for analysis
                        } else {
                            callbacks.printOutput("Payload test #" + (i + 1) + ": " + responseTime + "ms");
                        }
                    }

                    payloadResponseTimes.add(responseTime);
                    responses.add(response);
                } else {
                    callbacks.printError("Payload test #" + (i + 1) + " failed (no response)");
                }

                // Small delay between tests (rate limiting bypass is applied before each request)
                if (i < MIN_PAYLOAD_TESTS - 1) {
                    Thread.sleep(100); // Minimal delay for stability
                }
            } catch (Exception e) {
                callbacks.printError("Error in payload test #" + (i + 1) + ": " + e.getMessage());
            }
        }

        if (payloadResponseTimes.isEmpty()) {
            return new TestResult(
                parameter.getName(),
                parameter.getType().toString(),
                payload,
                "Error",
                "All payload tests failed",
                null,
                null,
                0,
                baselineResponseTime
            );
        }

        // Analyze results with advanced statistical validation
        boolean vulnerable = isVulnerableAdvanced(baselineResponseTime, payloadResponseTimes);

        // If initially vulnerable, perform confirmation tests
        if (vulnerable) {
            callbacks.printOutput("=== Performing Confirmation Tests ===");
            vulnerable = performConfirmationTests(baselineResponseTime, service, baseRequest);
        }

        // Use the first response for the result
        IHttpRequestResponse firstResponse = responses.get(0);
        long avgResponseTime = payloadResponseTimes.stream().mapToLong(Long::longValue).sum() / payloadResponseTimes.size();

        // Enhanced evidence with advanced statistical analysis
        String evidence = buildAdvancedEvidence(baselineResponseTime, payloadResponseTimes, vulnerable, sqlErrorDetected, sqlErrorType);

        return new TestResult(
            parameter.getName(),
            parameter.getType().toString(),
            payload,
            vulnerable ? "Potentially Vulnerable" : "Not Vulnerable",
            evidence,
            firstResponse.getRequest(),
            firstResponse.getResponse(),
            avgResponseTime,
            baselineResponseTime,
            sqlErrorDetected,
            sqlErrorType,
            sqlErrorMessage,
            sqlErrorSnippet
        );
    }

    /**
     * Measure the baseline response time with caching to avoid redundant measurements.
     */
    private long measureBaselineResponseTime() {
        // Create cache key based on request URL and method
        String cacheKey = createBaselineCacheKey();

        // Check if we have a cached baseline that's still valid
        Long cachedBaseline = baselineCache.get(cacheKey);
        Long cacheTimestamp = baselineCacheTimestamp.get(cacheKey);

        if (cachedBaseline != null && cacheTimestamp != null) {
            long age = System.currentTimeMillis() - cacheTimestamp;
            if (age < BASELINE_CACHE_TTL) {
                callbacks.printOutput("Using cached baseline for " + cacheKey + ": " + cachedBaseline + "ms (age: " + (age/1000) + "s)");
                return cachedBaseline;
            } else {
                callbacks.printOutput("Cached baseline expired for " + cacheKey + " (age: " + (age/1000) + "s), measuring new baseline");
                baselineCache.remove(cacheKey);
                baselineCacheTimestamp.remove(cacheKey);
            }
        }

        List<Long> responseTimes = new ArrayList<>();
        long totalTime = 0;
        int baselineRuns = MIN_BASELINE_RUNS;

        callbacks.printOutput("Starting baseline measurement for parameter: " + parameter.getName() + " (min runs: " + MIN_BASELINE_RUNS + ")");

        for (int i = 0; i < baselineRuns; i++) {
            // Apply rate limiting bypass delay before baseline request
            if (rateLimitBypass != null && rateLimitBypass.isEnabled()) {
                rateLimitBypass.applyDelay();
            }

            // Apply header randomization to baseline request
            byte[] baselineRequest = applyHeaderRandomization(baseRequestResponse.getRequest());

            long startTime = System.currentTimeMillis();
            IHttpRequestResponse baselineResp = callbacks.makeHttpRequest(
                baseRequestResponse.getHttpService(),
                baselineRequest
            );
            long endTime = System.currentTimeMillis();

            if (baselineResp == null || baselineResp.getResponse() == null) {
                 callbacks.printError("Baseline request #" + (i+1) + " failed (no response).");
                 continue;
            }

            long responseTime = endTime - startTime;
            callbacks.printOutput("Baseline run #" + (i+1) + ": " + responseTime + "ms");

            // Track only the first baseline request in history to avoid clutter
            if (i == 0) {
                trackRequest(baselineResp, null, null, responseTime, "Baseline Measurement");
            }

            if (responseTime > 10000) {
                 callbacks.printOutput("Warning: Baseline run #" + (i+1) + " took > 10 seconds.");
            }

            responseTimes.add(responseTime);
            totalTime += responseTime;

            // Small delay between baseline measurements (rate limiting bypass is applied before each request)
            try {
                Thread.sleep(100); // Minimal delay for stability
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                callbacks.printError("Baseline measurement interrupted.");
                return -1;
            }
        }

        if (responseTimes.size() < MIN_BASELINE_RUNS) {
             callbacks.printError("Could not complete minimum baseline runs (" + responseTimes.size() + "/" + MIN_BASELINE_RUNS + "). Aborting.");
             return -1;
        }

        if (responseTimes.size() >= 4) {
            Collections.sort(responseTimes);
            long lowestTime = responseTimes.remove(0);
            long highestTime = responseTimes.remove(responseTimes.size() - 1);
            callbacks.printOutput("Removed outliers from baseline: lowest=" + lowestTime +
                                "ms, highest=" + highestTime + "ms");
            totalTime = 0;
            for (long time : responseTimes) {
                totalTime += time;
            }
        }

         if (responseTimes.isEmpty()) {
             callbacks.printError("Baseline response time list empty after outlier removal. Aborting.");
             return -1;
         }
        long currentAverage = totalTime / responseTimes.size();

        if (responseTimes.size() >= 2) {
            double stdDev = calculateStandardDeviation(responseTimes, currentAverage);
            double relativeStdDev = (currentAverage > 0) ? (stdDev / currentAverage) : 0;

            callbacks.printOutput(String.format("Baseline variance check: Average=%dms, StdDev=%.2f, Relative StdDev=%.2f%% (threshold: %.2f%%)",
                                             currentAverage, stdDev, relativeStdDev * 100, VARIANCE_THRESHOLD * 100));

            int additionalRuns = 0;
            while (relativeStdDev > VARIANCE_THRESHOLD &&
                   responseTimes.size() < MAX_BASELINE_RUNS) {

                callbacks.printOutput("High baseline variance detected. Performing additional run...");
                additionalRuns++;

                // Apply rate limiting bypass delay before additional baseline request
                if (rateLimitBypass != null && rateLimitBypass.isEnabled()) {
                    rateLimitBypass.applyDelay();
                }

                // Apply header randomization to additional baseline request
                byte[] baselineRequest = applyHeaderRandomization(baseRequestResponse.getRequest());

                long startTime = System.currentTimeMillis();
                IHttpRequestResponse baselineResp = callbacks.makeHttpRequest(
                    baseRequestResponse.getHttpService(),
                    baselineRequest
                );
                long endTime = System.currentTimeMillis();

                 if (baselineResp == null || baselineResp.getResponse() == null) {
                     callbacks.printError("Additional baseline run #" + additionalRuns + " failed.");
                     continue;
                 }

                long responseTime = endTime - startTime;
                callbacks.printOutput("Additional baseline run #" + additionalRuns + ": " + responseTime + "ms");

                responseTimes.add(responseTime);
                totalTime += responseTime;

                currentAverage = totalTime / responseTimes.size();
                stdDev = calculateStandardDeviation(responseTimes, currentAverage);
                relativeStdDev = (currentAverage > 0) ? (stdDev / currentAverage) : 0;

                 callbacks.printOutput(String.format("After run #%d: Average=%dms, StdDev=%.2f, Relative StdDev=%.2f%%",
                                                     responseTimes.size(), currentAverage, stdDev, relativeStdDev * 100));

                // Small delay between additional baseline runs (rate limiting bypass is applied before each request)
                try {
                    Thread.sleep(100); // Minimal delay for stability
                } catch (InterruptedException e) {
                     Thread.currentThread().interrupt();
                     callbacks.printError("Baseline measurement interrupted during additional runs.");
                     return -1;
                }
            }

            callbacks.printOutput("Total baseline runs performed: " + responseTimes.size() +
                               " (initial: " + MIN_BASELINE_RUNS + ", additional: " + additionalRuns + ")");
            callbacks.printOutput(String.format("Final baseline relative standard deviation: %.2f%%", relativeStdDev * 100));
        }

        long finalBaseline = currentAverage;
        callbacks.printOutput("Final baseline response time: " + finalBaseline + "ms");

        if (finalBaseline <= 0) {
             callbacks.printError("Final baseline time is zero or negative. Measurement failed.");
             return -1;
        }

        // Cache the baseline for future use
        baselineCache.put(cacheKey, finalBaseline);
        baselineCacheTimestamp.put(cacheKey, System.currentTimeMillis());
        callbacks.printOutput("Cached baseline for " + cacheKey + ": " + finalBaseline + "ms");

        return finalBaseline;
    }

    /**
     * Create a cache key for baseline measurements based on request characteristics
     */
    private String createBaselineCacheKey() {
        IHttpService service = baseRequestResponse.getHttpService();
        IRequestInfo requestInfo = helpers.analyzeRequest(baseRequestResponse);

        String host = service.getHost();
        int port = service.getPort();
        String protocol = service.getProtocol();
        String method = requestInfo.getMethod();
        String url = requestInfo.getUrl().getPath();

        // Create a unique key that represents this endpoint
        return protocol + "://" + host + ":" + port + url + "#" + method;
    }

    /**
     * Calculate the standard deviation of a list of response times.
     */
    private double calculateStandardDeviation(List<Long> responseTimes, long average) {
        if (responseTimes == null || responseTimes.size() < 2) {
            return 0.0;
        }
        double sumSquaredDifferences = 0;
        for (long time : responseTimes) {
            double difference = time - average;
            sumSquaredDifferences += (difference * difference);
        }
        return Math.sqrt(sumSquaredDifferences / responseTimes.size());
    }

    // --- Payload Type Identification ---
    private enum PayloadType { SQL_CODE, OS_COMMAND, UNKNOWN }

    private PayloadType identifyPayloadType(String payload) {
         if (payload == null || payload.isEmpty()) {
             return PayloadType.UNKNOWN;
         }
         String decodedPayload = payload;
         try {
             if (payload.contains("%")) {
                 decodedPayload = URLDecoder.decode(payload, "UTF-8");
             }
             if (payload.contains("+") && !decodedPayload.contains(" ")) {
                 decodedPayload = decodedPayload.replace("+", " ");
             }
         } catch (Exception e) {
              callbacks.printError("Error decoding payload during type identification: " + e.getMessage());
         }

         // OS Command Check: Look for separators followed by OS commands
         // Corrected Regex: Escaped backslashes for Java string literals
         if (Pattern.compile(".*(?:;|\\|\\||&&|`|\\$|\\||\\n|^)\\s*(sleep|ping|timeout|wget|curl|netcat|nc|perl|python|php|\\bsh\\b|\\bbash\\b|cmd|powershell).*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(decodedPayload).matches()) {
             return PayloadType.OS_COMMAND;
         }

         // Enhanced SQL/Code Check 1: Look for specific time-based functions including XOR patterns
         // Corrected Regex: Escaped backslashes
         if (Pattern.compile(".*\\b(SELECT.+SLEEP|SLEEP\\s*\\(|WAITFOR\\s+DELAY|BENCHMARK\\s*\\(|pg_sleep\\s*\\(|dbms_pipe\\.receive_message|dbms_lock\\.sleep|setTimeout\\s*\\(|exec\\s*\\(|eval\\s*\\(|XOR.*sleep|if\\s*\\(.*sleep).*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(decodedPayload).matches()) {
             return PayloadType.SQL_CODE;
         }
         // Enhanced SQL/Code Check 2: Simpler keyword check including XOR and mathematical expressions
         // Corrected Regex: Escaped backslashes
          if (Pattern.compile(".*\\b(sleep|waitfor|benchmark|pg_sleep|dbms_pipe|dbms_lock|settimeout|exec|eval|xor.*sleep|if.*sleep|now\\(\\)|sysdate\\(\\))\\b.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(decodedPayload).matches()) {
              return PayloadType.SQL_CODE;
          }

         return PayloadType.UNKNOWN;
    }


    /**
     * Advanced vulnerability detection with statistical analysis and machine learning-like validation
     */
    private boolean isVulnerableAdvanced(long baselineTime, List<Long> payloadTimes) {
        if (payloadTimes.isEmpty()) {
            callbacks.printOutput("Result: Not Vulnerable (No valid payload responses)");
            return false;
        }

        callbacks.printOutput("=== Advanced Statistical Analysis ===");

        // Step 1: Remove statistical outliers
        List<Long> cleanedPayloadTimes = removeOutliers(payloadTimes);
        if (cleanedPayloadTimes.size() < 2) {
            callbacks.printOutput("Result: Not Vulnerable (Insufficient data after outlier removal)");
            return false;
        }

        // Step 2: Calculate advanced statistics
        AdvancedStatistics stats = calculateAdvancedStatistics(baselineTime, cleanedPayloadTimes);
        callbacks.printOutput("Advanced Stats: " + stats.toString());

        // Step 3: Enhanced Multi-Factor Vulnerability Assessment
        double pValue = performWelchsTTest(baselineTime, cleanedPayloadTimes);
        boolean statisticallySignificant = pValue < STATISTICAL_SIGNIFICANCE;
        callbacks.printOutput("Statistical Significance: p-value=" + String.format("%.6f", pValue) +
                             " (threshold=" + STATISTICAL_SIGNIFICANCE + "), Significant=" + statisticallySignificant);

        double effectSize = calculateCohenD(baselineTime, cleanedPayloadTimes);
        boolean largeEffect = effectSize >= EFFECT_SIZE_THRESHOLD;
        callbacks.printOutput("Effect Size: Cohen's d=" + String.format("%.3f", effectSize) +
                             " (threshold=" + EFFECT_SIZE_THRESHOLD + "), Large Effect=" + largeEffect);

        double temporalConsistency = calculateTemporalConsistency(cleanedPayloadTimes);
        boolean temporallyConsistent = temporalConsistency >= TEMPORAL_CONSISTENCY_THRESHOLD;
        callbacks.printOutput("Temporal Consistency: " + String.format("%.3f", temporalConsistency) +
                             " (threshold=" + TEMPORAL_CONSISTENCY_THRESHOLD + "), Consistent=" + temporallyConsistent);

        // Enhanced vulnerability scoring system
        int vulnerabilityScore = 0;
        StringBuilder scoreBreakdown = new StringBuilder("Vulnerability Score Breakdown: ");

        // Factor 1: Statistical significance (30 points)
        if (statisticallySignificant) {
            vulnerabilityScore += 30;
            scoreBreakdown.append("Statistical(+30) ");
        } else {
            scoreBreakdown.append("Statistical(+0) ");
        }

        // Factor 2: Effect size (25 points)
        if (largeEffect) {
            vulnerabilityScore += 25;
            scoreBreakdown.append("EffectSize(+25) ");
        } else if (effectSize >= 0.5) { // Medium effect
            vulnerabilityScore += 15;
            scoreBreakdown.append("EffectSize(+15) ");
        } else {
            scoreBreakdown.append("EffectSize(+0) ");
        }

        // Factor 3: Temporal consistency (20 points)
        if (temporallyConsistent) {
            vulnerabilityScore += 20;
            scoreBreakdown.append("Temporal(+20) ");
        } else if (temporalConsistency >= 0.5) { // Moderate consistency
            vulnerabilityScore += 10;
            scoreBreakdown.append("Temporal(+10) ");
        } else {
            scoreBreakdown.append("Temporal(+0) ");
        }

        // Factor 4: Delay magnitude (15 points)
        double avgPayloadTime = cleanedPayloadTimes.stream().mapToDouble(Long::doubleValue).average().orElse(0);
        double avgBaselineTime = (double) baselineTime; // baselineTime is a single long value
        double delayMagnitude = avgPayloadTime - avgBaselineTime;

        if (delayMagnitude >= 5000) { // 5+ second delay
            vulnerabilityScore += 15;
            scoreBreakdown.append("DelayMag(+15) ");
        } else if (delayMagnitude >= 3000) { // 3+ second delay
            vulnerabilityScore += 10;
            scoreBreakdown.append("DelayMag(+10) ");
        } else if (delayMagnitude >= 1000) { // 1+ second delay
            vulnerabilityScore += 5;
            scoreBreakdown.append("DelayMag(+5) ");
        } else {
            scoreBreakdown.append("DelayMag(+0) ");
        }

        // Factor 5: Consistency across multiple tests (10 points)
        if (cleanedPayloadTimes.size() >= 3) {
            vulnerabilityScore += 10;
            scoreBreakdown.append("MultiTest(+10) ");
        } else {
            scoreBreakdown.append("MultiTest(+0) ");
        }

        scoreBreakdown.append("= ").append(vulnerabilityScore).append("/100");
        callbacks.printOutput(scoreBreakdown.toString());

        // Enhanced vulnerability determination
        boolean isVulnerable = false;
        String vulnerabilityLevel = "";

        if (vulnerabilityScore >= 70) {
            isVulnerable = true;
            vulnerabilityLevel = "HIGH CONFIDENCE";
        } else if (vulnerabilityScore >= 50) {
            isVulnerable = true;
            vulnerabilityLevel = "MEDIUM CONFIDENCE";
        } else if (vulnerabilityScore >= 30 && (statisticallySignificant || largeEffect)) {
            isVulnerable = true;
            vulnerabilityLevel = "LOW CONFIDENCE";
        } else {
            vulnerabilityLevel = "NOT VULNERABLE";
        }

        callbacks.printOutput("Enhanced Detection Result: " + vulnerabilityLevel + " (Score: " + vulnerabilityScore + "/100)");

        // Fallback: Simple delay-based detection for obvious cases
        if (!isVulnerable && delayMagnitude >= 4000) { // 4+ second consistent delay
            double consistentDelayCount = 0;
            for (Long payloadTime : cleanedPayloadTimes) {
                if (payloadTime - avgBaselineTime >= 3000) { // Each test shows 3+ second delay
                    consistentDelayCount++;
                }
            }
            double consistencyRatio = consistentDelayCount / cleanedPayloadTimes.size();

            if (consistencyRatio >= 0.6) { // 60% of tests show significant delay
                isVulnerable = true;
                vulnerabilityLevel = "DELAY-BASED DETECTION";
                callbacks.printOutput("Fallback Detection: Consistent delay pattern detected (" +
                                    String.format("%.1f", delayMagnitude) + "ms avg delay, " +
                                    String.format("%.0f", consistencyRatio * 100) + "% consistency)");
            }
        }

        if (!isVulnerable) {
            callbacks.printOutput("Result: Not Vulnerable (Insufficient evidence - Score: " + vulnerabilityScore + "/100)");
            return false;
        }

        // Step 6: Network stability validation
        double networkStability = calculateNetworkStability(cleanedPayloadTimes);
        boolean networkStable = networkStability <= NETWORK_STABILITY_THRESHOLD;
        callbacks.printOutput("Network Stability: " + String.format("%.3f", networkStability) +
                             " (threshold=" + NETWORK_STABILITY_THRESHOLD + "), Stable=" + networkStable);

        if (!networkStable) {
            callbacks.printOutput("Result: Not Vulnerable (Network instability detected)");
            return false;
        }

        // Step 7: Payload-specific validation
        boolean payloadValidation = validatePayloadSpecificBehavior(baselineTime, cleanedPayloadTimes);
        if (!payloadValidation) {
            callbacks.printOutput("Result: Not Vulnerable (Payload-specific validation failed)");
            return false;
        }

        callbacks.printOutput("Result: Potentially Vulnerable (All advanced validations passed)");
        return true;
    }

    /**
     * Enhanced vulnerability detection with multiple payload validation (legacy method)
     */
    private boolean isVulnerableEnhanced(long baselineTime, List<Long> payloadTimes) {
        if (payloadTimes.isEmpty()) {
            callbacks.printOutput("Result: Not Vulnerable (No valid payload responses)");
            return false;
        }

        // Calculate statistics for payload times
        Collections.sort(payloadTimes);
        long minTime = payloadTimes.get(0);
        long maxTime = payloadTimes.get(payloadTimes.size() - 1);
        long avgTime = payloadTimes.stream().mapToLong(Long::longValue).sum() / payloadTimes.size();

        // Calculate standard deviation of payload times
        double variance = 0;
        for (long time : payloadTimes) {
            variance += Math.pow(time - avgTime, 2);
        }
        double stdDev = Math.sqrt(variance / payloadTimes.size());
        double relativeStdDev = avgTime > 0 ? stdDev / avgTime : 0;

        callbacks.printOutput("=== Enhanced Vulnerability Analysis ===");
        callbacks.printOutput("Baseline: " + baselineTime + "ms");
        callbacks.printOutput("Payload times: " + payloadTimes);
        callbacks.printOutput("Min: " + minTime + "ms, Max: " + maxTime + "ms, Avg: " + avgTime + "ms");
        callbacks.printOutput("StdDev: " + String.format("%.2f", stdDev) + "ms, Relative StdDev: " + String.format("%.2f%%", relativeStdDev * 100));

        // Check for consistency in payload times (low variance indicates reliable delay)
        if (relativeStdDev > 0.25) { // Tightened threshold
            callbacks.printOutput("Result: Not Vulnerable (High variance in payload times: " + String.format("%.2f%%", relativeStdDev * 100) + ")");
            return false;
        }

        // Check if ALL payload times show significant delay
        int consistentDelays = 0;
        for (long time : payloadTimes) {
            if (isVulnerable(baselineTime, time)) {
                consistentDelays++;
            }
        }

        double consistencyRatio = (double) consistentDelays / payloadTimes.size();
        callbacks.printOutput("Consistent delays: " + consistentDelays + "/" + payloadTimes.size() + " (" + String.format("%.1f%%", consistencyRatio * 100) + ")");

        // Require perfect consistency for positive result (all tests must show delay)
        boolean isConsistent = consistencyRatio >= 1.0; // 100% of tests must show delay

        if (!isConsistent) {
            callbacks.printOutput("Result: Not Vulnerable (Inconsistent delays - require 100% consistency)");
            return false;
        }

        // Additional validation: check if the delay matches expected payload delay
        long avgDelay = avgTime - baselineTime;
        int expectedDelay = extractDelayTime(payload);
        boolean delayMatches = validateExpectedDelay(avgDelay, expectedDelay);

        if (!delayMatches) {
            callbacks.printOutput("Result: Not Vulnerable (Delay doesn't match expected payload delay)");
            return false;
        }

        // Check for network jitter that might cause false positives
        // Check jitter in payload times
        if (hasHighNetworkJitter(payloadTimes)) {
            callbacks.printOutput("Result: Not Vulnerable (High network jitter detected)");
            return false;
        }

        // Final validation: ensure delay is significantly larger than baseline variance
        double delayFactor = (double) avgTime / baselineTime;
        boolean significantDelay = delayFactor >= MIN_DELAY_FACTOR && avgDelay >= ABSOLUTE_MIN_DELAY;

        if (!significantDelay) {
            callbacks.printOutput("Result: Not Vulnerable (Delay not significant enough: " + String.format("%.2fx", delayFactor) + ", " + avgDelay + "ms)");
            return false;
        }

        callbacks.printOutput("Average delay: " + avgDelay + "ms, Factor: " + String.format("%.2fx", delayFactor));
        callbacks.printOutput("Result: Potentially Vulnerable (All validations passed)");

        return true;
    }

    /**
     * Build advanced evidence string with comprehensive statistical analysis
     */
    private String buildAdvancedEvidence(long baselineTime, List<Long> payloadTimes, boolean vulnerable, boolean sqlErrorDetected, String sqlErrorType) {
        if (payloadTimes.isEmpty()) {
            return "No valid payload responses received";
        }

        // Remove outliers for analysis
        List<Long> cleanedTimes = removeOutliers(payloadTimes);

        // Calculate advanced statistics
        AdvancedStatistics stats = calculateAdvancedStatistics(baselineTime, cleanedTimes);

        // Calculate additional metrics
        double pValue = performWelchsTTest(baselineTime, cleanedTimes);
        double effectSize = calculateCohenD(baselineTime, cleanedTimes);
        double temporalConsistency = calculateTemporalConsistency(cleanedTimes);
        double networkStability = calculateNetworkStability(cleanedTimes);

        long avgDelay = (long) (stats.mean - baselineTime);
        double delayFactor = baselineTime > 0 ? stats.mean / baselineTime : 0;

        PayloadType payloadType = identifyPayloadType(payload);
        int extractedDelay = extractDelayTime(payload);

        StringBuilder evidence = new StringBuilder();
        evidence.append("Advanced Analysis: ");
        evidence.append(String.format("Baseline=%dms, Tests=%d/%d, ", baselineTime, cleanedTimes.size(), payloadTimes.size()));
        evidence.append(String.format("Avg=%.0fms (%.1fx), ", stats.mean, delayFactor));
        evidence.append(String.format("Delay=+%dms, ", avgDelay));
        evidence.append(String.format("p=%.4f, ", pValue));
        evidence.append(String.format("d=%.2f, ", effectSize));
        evidence.append(String.format("TC=%.2f, ", temporalConsistency));
        evidence.append(String.format("NS=%.3f, ", networkStability));
        evidence.append(String.format("Type=%s", payloadType));

        if (extractedDelay > 0) {
            evidence.append(String.format(", Expected=%ds", extractedDelay));
        }

        // Add SQL error information
        if (sqlErrorDetected) {
            evidence.append(String.format(", SQL Error: %s", sqlErrorType != null ? sqlErrorType : "Generic"));
        }

        // Add confidence indicators
        if (vulnerable) {
            evidence.append(" [HIGH CONFIDENCE]");
        } else {
            evidence.append(" [LOW CONFIDENCE]");
        }

        return evidence.toString();
    }

    /**
     * Build enhanced evidence string with statistical analysis (legacy method)
     */
    private String buildEnhancedEvidence(long baselineTime, List<Long> payloadTimes, boolean vulnerable, boolean sqlErrorDetected, String sqlErrorType) {
        if (payloadTimes.isEmpty()) {
            return "No valid payload responses received";
        }

        Collections.sort(payloadTimes);
        long minTime = payloadTimes.get(0);
        long maxTime = payloadTimes.get(payloadTimes.size() - 1);
        long avgTime = payloadTimes.stream().mapToLong(Long::longValue).sum() / payloadTimes.size();
        long avgDelay = avgTime - baselineTime;
        double delayFactor = baselineTime > 0 ? (double) avgTime / baselineTime : 0;

        // Calculate consistency
        int consistentDelays = 0;
        for (long time : payloadTimes) {
            if (isVulnerable(baselineTime, time)) {
                consistentDelays++;
            }
        }
        double consistencyRatio = (double) consistentDelays / payloadTimes.size();

        PayloadType payloadType = identifyPayloadType(payload);
        int extractedDelay = extractDelayTime(payload);

        StringBuilder evidence = new StringBuilder();
        evidence.append(String.format("Enhanced Analysis: Baseline=%dms, Tests=%d, ", baselineTime, payloadTimes.size()));
        evidence.append(String.format("Avg=%dms (%.1fx), Range=%d-%dms, ", avgTime, delayFactor, minTime, maxTime));
        evidence.append(String.format("Delay=+%dms, Consistency=%.1f%%, ", avgDelay, consistencyRatio * 100));
        evidence.append(String.format("Type=%s, Expected=%ds", payloadType, extractedDelay));

        // Add SQL error information
        if (sqlErrorDetected) {
            evidence.append(String.format(", SQL Error: %s", sqlErrorType != null ? sqlErrorType : "Generic"));
        }

        return evidence.toString();
    }

    /**
     * Determine if the response time indicates a vulnerability (legacy method for individual tests).
     */
    private boolean isVulnerable(long baselineTime, long testTime) {
        int extractedDelaySec = extractDelayTime(payload);
        PayloadType payloadType = identifyPayloadType(payload);

        callbacks.printOutput("--- Vulnerability Check ---");
        callbacks.printOutput("Payload: " + payload);
        callbacks.printOutput("Identified Payload Type: " + payloadType);
        callbacks.printOutput("Extracted Delay Time: " + extractedDelaySec + " seconds");

        if (baselineTime <= 0) {
             callbacks.printOutput("Result: Not Vulnerable (Invalid Baseline <= 0)");
             return false;
        }
        long actualDelayMs = testTime - baselineTime;
        if (actualDelayMs < ABSOLUTE_MIN_DELAY) {
             callbacks.printOutput(String.format("Result: Not Vulnerable (Actual Delay %dms < Min Threshold %dms)", actualDelayMs, ABSOLUTE_MIN_DELAY));
             return false;
        }
        double delayFactor = (double) testTime / baselineTime;
         if (delayFactor < MIN_DELAY_FACTOR) {
             callbacks.printOutput(String.format("Result: Not Vulnerable (Delay Factor %.2fx < Min Threshold %.2fx)", delayFactor, MIN_DELAY_FACTOR));
             return false;
         }

        int score = 0;
        int threshold = 10;
        int expectedDelayMs = extractedDelaySec > 0 ? extractedDelaySec * 1000 : 0;

        callbacks.printOutput(String.format("Timing: Base=%dms, Test=%dms, Actual Delay=%dms, Factor=%.2fx",
                                            baselineTime, testTime, actualDelayMs, delayFactor));
        if (expectedDelayMs > 0) {
             callbacks.printOutput(String.format("Timing: Expected Delay=%dms (from payload)", expectedDelayMs));
        } else {
             callbacks.printOutput("Timing: No specific delay time extracted from payload.");
        }

        if (payloadType == PayloadType.OS_COMMAND) {
            callbacks.printOutput("Scoring Type: OS COMMAND INJECTION");
            threshold = 9;
            if (expectedDelayMs > 0) {
                double delayPercentage = (double) actualDelayMs / expectedDelayMs;
                 callbacks.printOutput(String.format("Score Detail: Delay vs Expected = %.1f%%", delayPercentage * 100));
                if (delayPercentage >= 0.85) { score += 7; callbacks.printOutput(" +7 points (Delay >= 85% of Expected)"); }
                else if (delayPercentage >= 0.60) { score += 5; callbacks.printOutput(" +5 points (Delay >= 60% of Expected)"); }
                else if (delayPercentage >= 0.35) { score += 3; callbacks.printOutput(" +3 points (Delay >= 35% of Expected)"); }
                else if (delayPercentage >= 0.15) { score += 1; callbacks.printOutput(" +1 point (Delay >= 15% of Expected)"); }
            } else {
                 if (actualDelayMs >= 5000) { score += 4; callbacks.printOutput(" +4 points (Absolute Delay >= 5000ms, No Expected)"); }
                 else if (actualDelayMs >= 3000) { score += 2; callbacks.printOutput(" +2 points (Absolute Delay >= 3000ms, No Expected)"); }
                 else if (actualDelayMs >= TIME_THRESHOLD) { score += 1; callbacks.printOutput(" +1 point (Absolute Delay >= " + TIME_THRESHOLD + "ms, No Expected)"); }
            }
            callbacks.printOutput(String.format("Score Detail: Delay Factor = %.2fx", delayFactor));
            if (delayFactor >= 2.5) { score += 4; callbacks.printOutput(" +4 points (Factor >= 2.5x)"); }
            else if (delayFactor >= 1.8) { score += 3; callbacks.printOutput(" +3 points (Factor >= 1.8x)"); }
            else if (delayFactor >= MIN_DELAY_FACTOR) { score += 2; callbacks.printOutput(" +2 points (Factor >= " + MIN_DELAY_FACTOR + "x)"); }

            callbacks.printOutput(String.format("Score Detail: Absolute Delay = %dms", actualDelayMs));
            if (actualDelayMs >= 7000) { score += 3; callbacks.printOutput(" +3 points (Absolute Delay >= 7000ms)"); }
            else if (actualDelayMs >= 4000) { score += 2; callbacks.printOutput(" +2 points (Absolute Delay >= 4000ms)"); }
            else if (actualDelayMs >= TIME_THRESHOLD) { score += 1; callbacks.printOutput(" +1 point (Absolute Delay >= " + TIME_THRESHOLD + "ms)"); }

        } else if (payloadType == PayloadType.SQL_CODE) {
             callbacks.printOutput("Scoring Type: SQL / CODE INJECTION");
             threshold = 8;
             if (expectedDelayMs > 0) {
                 double delayPercentage = (double) actualDelayMs / expectedDelayMs;
                 callbacks.printOutput(String.format("Score Detail: Delay vs Expected = %.1f%%", delayPercentage * 100));
                 if (delayPercentage >= 0.85) { score += 7; callbacks.printOutput(" +7 points (Delay >= 85% of Expected)"); }
                 else if (delayPercentage >= 0.60) { score += 5; callbacks.printOutput(" +5 points (Delay >= 60% of Expected)"); }
                 else if (delayPercentage >= 0.35) { score += 3; callbacks.printOutput(" +3 points (Delay >= 35% of Expected)"); }
                 else if (delayPercentage >= 0.15) { score += 1; callbacks.printOutput(" +1 point (Delay >= 15% of Expected)"); }
             } else {
                 if (actualDelayMs >= 5000) { score += 4; callbacks.printOutput(" +4 points (Absolute Delay >= 5000ms, No Expected)"); }
                 else if (actualDelayMs >= 3000) { score += 2; callbacks.printOutput(" +2 points (Absolute Delay >= 3000ms, No Expected)"); }
                 else if (actualDelayMs >= TIME_THRESHOLD) { score += 1; callbacks.printOutput(" +1 point (Absolute Delay >= " + TIME_THRESHOLD + "ms, No Expected)"); }
             }
             callbacks.printOutput(String.format("Score Detail: Delay Factor = %.2fx", delayFactor));
             if (delayFactor >= 2.5) { score += 4; callbacks.printOutput(" +4 points (Factor >= 2.5x)"); }
             else if (delayFactor >= 1.8) { score += 3; callbacks.printOutput(" +3 points (Factor >= 1.8x)"); }
             else if (delayFactor >= MIN_DELAY_FACTOR) { score += 2; callbacks.printOutput(" +2 points (Factor >= " + MIN_DELAY_FACTOR + "x)"); }

             callbacks.printOutput(String.format("Score Detail: Absolute Delay = %dms", actualDelayMs));
             if (actualDelayMs >= 7000) { score += 3; callbacks.printOutput(" +3 points (Absolute Delay >= 7000ms)"); }
             else if (actualDelayMs >= 4000) { score += 2; callbacks.printOutput(" +2 points (Absolute Delay >= 4000ms)"); }
             else if (actualDelayMs >= TIME_THRESHOLD) { score += 1; callbacks.printOutput(" +1 point (Absolute Delay >= " + TIME_THRESHOLD + "ms)"); }

        } else { // UNKNOWN type
             callbacks.printOutput("Scoring Type: GENERIC / UNKNOWN");
             threshold = 10;
             boolean meetsAbsoluteDelay = actualDelayMs >= TIME_THRESHOLD;
             boolean meetsDelayFactor = delayFactor >= MIN_DELAY_FACTOR;
             callbacks.printOutput(String.format("Generic Check: Absolute Delay >= %dms? %b. Delay Factor >= %.1fx? %b.",
                                                 TIME_THRESHOLD, meetsAbsoluteDelay, MIN_DELAY_FACTOR, meetsDelayFactor));
             boolean isGenericVuln = meetsAbsoluteDelay && meetsDelayFactor;
             callbacks.printOutput(String.format("Result: %s", isGenericVuln ? "Potentially Vulnerable" : "Not Vulnerable"));
             return isGenericVuln;
        }

        boolean isVuln = score >= threshold;
        callbacks.printOutput(String.format("Final Score: %d / %d. Result: %s", score, threshold, isVuln ? "Potentially Vulnerable" : "Not Vulnerable"));
        return isVuln;
    }


    /**
     * Extract delay time value from a time-based injection payload.
     */
    private int extractDelayTime(String payload) {
        if (payload == null || payload.isEmpty()) {
            return 0;
        }

        String decodedPayload = payload;
        try {
            if (payload.contains("%")) {
                decodedPayload = URLDecoder.decode(payload, "UTF-8");
            }
            if (payload.contains("+") && !decodedPayload.contains(" ")) {
                decodedPayload = decodedPayload.replace("+", " ");
            }
        } catch (UnsupportedEncodingException | IllegalArgumentException e) {
            callbacks.printError("Error decoding payload in extractDelayTime: " + e.getMessage());
            decodedPayload = payload;
        }

        // OS Command Injection Patterns
        // Corrected Regex: Escaped backslashes
        String[][] osPatterns = {
            { "sleep\\s+(\\d+)", "1", "OS_SLEEP" },
            { "timeout\\s+/t\\s+(\\d+)", "1", "OS_TIMEOUT" },
            { "ping\\s+[^\\s]+\\s+-n\\s+(\\d+)", "1", "OS_PING_N" },
            { "ping\\s+[^\\s]+\\s+-c\\s+(\\d+)", "1", "OS_PING_C" }
        };

        for (String[] osPatternInfo : osPatterns) {
            String patternStr = osPatternInfo[0];
            String groupInfo = osPatternInfo[1];
            String typeHint = osPatternInfo[2];
            // Corrected Regex: Escaped backslashes
            String fullPatternStr = "(?:;|\\|\\||&&|`|\\$|\\||\\n|^)\\s*" + patternStr;
            try {
                Pattern pattern = Pattern.compile(fullPatternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(decodedPayload);
                if (matcher.find()) {
                    int groupIndex = Integer.parseInt(groupInfo);
                    int value = Integer.parseInt(matcher.group(groupIndex));
                    callbacks.printOutput("Found OS Command delay pattern ("+ typeHint +"): " + patternStr + " with value: " + value);
                    if (typeHint.startsWith("OS_PING")) {
                        int estimatedTime = Math.max(1, value + 1);
                         callbacks.printOutput("Estimated delay for ping: " + estimatedTime + " seconds");
                        return estimatedTime;
                    } else {
                        return Math.max(1, value);
                    }
                }
            } catch (Exception e) {
                 callbacks.printError("Error matching/parsing OS pattern ("+ typeHint +"): " + e.getMessage());
            }
        }

        // Enhanced SQL / Code Injection Patterns with mathematical expressions
        // Corrected Regex: Escaped backslashes and single quotes
        String[][] sqlCodePatterns = {
             { "SLEEP\\s*\\(\\s*(\\d+)\\s*\\)", "1", "SQL_MYSQL_SLEEP" },
             { "SLEEP\\s*\\(\\s*(\\d+\\s*\\*\\s*\\d+)\\s*\\)", "math", "SQL_MYSQL_SLEEP_MATH" },
             { "SLEEP\\s*\\(\\s*(\\d+\\s*[+\\-*/]\\s*\\d+(?:\\s*[+\\-*/]\\s*\\d+)*)\\s*\\)", "math", "SQL_MYSQL_SLEEP_COMPLEX" },
             { "sleep\\s*\\(\\s*(\\d+\\s*\\*\\s*\\d+)\\s*\\)", "math", "SQL_MYSQL_SLEEP_XOR_MATH" },
             { "sleep\\s*\\(\\s*(\\d+\\s*[+\\-*/]\\s*\\d+(?:\\s*[+\\-*/]\\s*\\d+)*)\\s*\\)", "math", "SQL_MYSQL_SLEEP_XOR_COMPLEX" },
             { "WAITFOR\\s+DELAY\\s+\\'(\\d+):(\\d+):(\\d+)\\'", "hms", "SQL_MSSQL_WAITFOR" },
             { "pg_sleep\\s*\\(\\s*(\\d+)\\s*\\)", "1", "SQL_PG_SLEEP" },
             { "pg_sleep\\s*\\(\\s*(\\d+\\s*\\*\\s*\\d+)\\s*\\)", "math", "SQL_PG_SLEEP_MATH" },
             { "pg_sleep\\s*\\(\\s*(\\d+\\s*[+\\-*/]\\s*\\d+(?:\\s*[+\\-*/]\\s*\\d+)*)\\s*\\)", "math", "SQL_PG_SLEEP_COMPLEX" },
             { "dbms_pipe\\.receive_message\\([^,]+,\\s*(\\d+)\\)", "1", "SQL_ORA_DBMS_PIPE" },
             { "DBMS_LOCK\\.SLEEP\\s*\\(\\s*(\\d+)\\s*\\)", "1", "SQL_ORA_DBMS_LOCK" },
             { "DBMS_LOCK\\.SLEEP\\s*\\(\\s*(\\d+\\s*\\*\\s*\\d+)\\s*\\)", "math", "SQL_ORA_DBMS_LOCK_MATH" },
             { "DBMS_LOCK\\.SLEEP\\s*\\(\\s*(\\d+\\s*[+\\-*/]\\s*\\d+(?:\\s*[+\\-*/]\\s*\\d+)*)\\s*\\)", "math", "SQL_ORA_DBMS_LOCK_COMPLEX" },
             { "sleep\\s*\\(\\s*(\\d+)\\s*\\)", "ms_to_s", "CODE_JS_SLEEP" },
             { "setTimeout\\s*\\([^,]+,\\s*(\\d+)\\s*\\)", "ms_to_s", "CODE_JS_SETTIMEOUT" }
        };

        for (String[] patternInfo : sqlCodePatterns) {
            String patternStr = patternInfo[0];
            String type = patternInfo[1];
            String typeHint = patternInfo[2];
            try {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(decodedPayload);
                if (matcher.find()) {
                    if ("hms".equals(type)) {
                        int hours = Integer.parseInt(matcher.group(1));
                        int minutes = Integer.parseInt(matcher.group(2));
                        int seconds = Integer.parseInt(matcher.group(3));
                        int totalSeconds = hours * 3600 + minutes * 60 + seconds;
                        callbacks.printOutput("Found SQL/Code delay pattern ("+ typeHint +"): " + totalSeconds + " seconds");
                        return Math.max(1, totalSeconds);
                    } else if ("ms_to_s".equals(type)) {
                        int ms = Integer.parseInt(matcher.group(1));
                        int seconds = ms / 1000;
                        callbacks.printOutput("Found SQL/Code delay pattern ("+ typeHint +"): " + ms + "ms -> " + seconds + " seconds");
                        return Math.max(1, seconds);
                    } else if ("math".equals(type)) {
                        // Handle mathematical expressions like "5*5", "3*2+1", etc.
                        String mathExpression = matcher.group(1);
                        int result = evaluateMathExpression(mathExpression);
                        callbacks.printOutput("Found SQL/Code delay pattern ("+ typeHint +"): " + mathExpression + " = " + result + " seconds");
                        return Math.max(1, result);
                    } else {
                        int groupIndex = Integer.parseInt(type);
                        int seconds = Integer.parseInt(matcher.group(groupIndex));
                        callbacks.printOutput("Found SQL/Code delay pattern ("+ typeHint +"): " + seconds + " seconds");
                        return Math.max(1, seconds);
                    }
                }
            } catch (Exception e) {
                 callbacks.printError("Error matching/parsing SQL/Code pattern ("+ typeHint +"): " + e.getMessage());
            }
        }

        // Fallback keyword check
        // Corrected Regex: Escaped backslashes
        if (Pattern.compile(".*\\b(sleep|waitfor|pg_sleep|dbms_pipe|timeout|benchmark)\\b.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(decodedPayload).matches()) {
            callbacks.printOutput("Time-based keyword found without specific value, using default of 5 seconds for check.");
            return 5;
        }

        return 0;
    }

    /**
     * Evaluate simple mathematical expressions like "5*5", "3*2+1", etc.
     * Supports +, -, *, / operations with proper precedence
     */
    private int evaluateMathExpression(String expression) {
        try {
            // Remove all whitespace
            expression = expression.replaceAll("\\s+", "");

            // Handle simple cases first
            if (expression.matches("\\d+")) {
                return Integer.parseInt(expression);
            }

            // Handle multiplication and division first (higher precedence)
            while (expression.contains("*") || expression.contains("/")) {
                // Find the first multiplication or division
                int multIndex = expression.indexOf("*");
                int divIndex = expression.indexOf("/");

                int opIndex;
                char operator;
                if (multIndex == -1) {
                    opIndex = divIndex;
                    operator = '/';
                } else if (divIndex == -1) {
                    opIndex = multIndex;
                    operator = '*';
                } else {
                    if (multIndex < divIndex) {
                        opIndex = multIndex;
                        operator = '*';
                    } else {
                        opIndex = divIndex;
                        operator = '/';
                    }
                }

                // Extract operands
                int leftStart = opIndex - 1;
                while (leftStart > 0 && Character.isDigit(expression.charAt(leftStart - 1))) {
                    leftStart--;
                }

                int rightEnd = opIndex + 1;
                while (rightEnd < expression.length() && Character.isDigit(expression.charAt(rightEnd))) {
                    rightEnd++;
                }

                int leftOperand = Integer.parseInt(expression.substring(leftStart, opIndex));
                int rightOperand = Integer.parseInt(expression.substring(opIndex + 1, rightEnd));

                int result;
                if (operator == '*') {
                    result = leftOperand * rightOperand;
                } else {
                    result = rightOperand != 0 ? leftOperand / rightOperand : leftOperand;
                }

                // Replace the expression with the result
                expression = expression.substring(0, leftStart) + result + expression.substring(rightEnd);
            }

            // Handle addition and subtraction (lower precedence)
            while (expression.contains("+") || expression.contains("-")) {
                // Find the first addition or subtraction (excluding negative numbers)
                int addIndex = expression.indexOf("+");
                int subIndex = expression.indexOf("-", 1); // Start from index 1 to skip negative numbers

                int opIndex;
                char operator;
                if (addIndex == -1) {
                    opIndex = subIndex;
                    operator = '-';
                } else if (subIndex == -1) {
                    opIndex = addIndex;
                    operator = '+';
                } else {
                    if (addIndex < subIndex) {
                        opIndex = addIndex;
                        operator = '+';
                    } else {
                        opIndex = subIndex;
                        operator = '-';
                    }
                }

                if (opIndex == -1) break;

                // Extract operands
                int leftStart = opIndex - 1;
                while (leftStart > 0 && Character.isDigit(expression.charAt(leftStart - 1))) {
                    leftStart--;
                }

                int rightEnd = opIndex + 1;
                while (rightEnd < expression.length() && Character.isDigit(expression.charAt(rightEnd))) {
                    rightEnd++;
                }

                int leftOperand = Integer.parseInt(expression.substring(leftStart, opIndex));
                int rightOperand = Integer.parseInt(expression.substring(opIndex + 1, rightEnd));

                int result;
                if (operator == '+') {
                    result = leftOperand + rightOperand;
                } else {
                    result = leftOperand - rightOperand;
                }

                // Replace the expression with the result
                expression = expression.substring(0, leftStart) + result + expression.substring(rightEnd);
            }

            // Final result should be a number
            return Integer.parseInt(expression);

        } catch (Exception e) {
            callbacks.printError("Error evaluating math expression '" + expression + "': " + e.getMessage());
            // Try to extract the first number as fallback
            try {
                String firstNumber = expression.replaceAll("[^\\d].*", "");
                if (!firstNumber.isEmpty()) {
                    return Integer.parseInt(firstNumber);
                }
            } catch (Exception ex) {
                // Ignore
            }
            return 7; // Default fallback
        }
    }

    /**
     * Remove statistical outliers from response times using IQR method
     */
    private List<Long> removeOutliers(List<Long> times) {
        if (times.size() < 4) {
            return new ArrayList<>(times); // Not enough data for outlier detection
        }

        List<Long> sortedTimes = new ArrayList<>(times);
        Collections.sort(sortedTimes);

        // Calculate quartiles
        int n = sortedTimes.size();
        int q1Index = n / 4;
        int q3Index = 3 * n / 4;

        long q1 = sortedTimes.get(q1Index);
        long q3 = sortedTimes.get(q3Index);
        long iqr = q3 - q1;

        // Calculate outlier bounds (1.5 * IQR rule)
        long lowerBound = q1 - (long)(1.5 * iqr);
        long upperBound = q3 + (long)(1.5 * iqr);

        // Filter out outliers
        List<Long> filtered = new ArrayList<>();
        for (long time : times) {
            if (time >= lowerBound && time <= upperBound) {
                filtered.add(time);
            } else {
                callbacks.printOutput("Removing outlier: " + time + "ms (bounds: " + lowerBound + "-" + upperBound + "ms)");
            }
        }

        return filtered;
    }

    /**
     * Detect network jitter by analyzing baseline response time patterns
     */
    private boolean hasHighNetworkJitter(List<Long> responseTimes) {
        if (responseTimes.size() < 3) {
            return false;
        }

        // Calculate consecutive differences to detect jitter
        List<Long> differences = new ArrayList<>();
        for (int i = 1; i < responseTimes.size(); i++) {
            differences.add(Math.abs(responseTimes.get(i) - responseTimes.get(i - 1)));
        }

        // Calculate average difference
        long avgDifference = differences.stream().mapToLong(Long::longValue).sum() / differences.size();
        long avgResponseTime = responseTimes.stream().mapToLong(Long::longValue).sum() / responseTimes.size();

        // High jitter if consecutive differences are > 30% of average response time
        double jitterRatio = (double) avgDifference / avgResponseTime;
        boolean highJitter = jitterRatio > 0.3;

        if (highJitter) {
            callbacks.printOutput("High network jitter detected: " + String.format("%.1f%%", jitterRatio * 100) +
                                 " (avg diff: " + avgDifference + "ms, avg time: " + avgResponseTime + "ms)");
        }

        return highJitter;
    }

    /**
     * Validate that the delay matches the expected payload delay time
     */
    private boolean validateExpectedDelay(long actualDelay, int expectedDelay) {
        if (expectedDelay <= 0) {
            return true; // Can't validate if we don't know expected delay
        }

        long expectedDelayMs = expectedDelay * 1000L;
        long tolerance = Math.max(1000, expectedDelayMs / 5); // 20% tolerance or 1 second minimum

        boolean matches = Math.abs(actualDelay - expectedDelayMs) <= tolerance;

        callbacks.printOutput("Delay validation: Expected=" + expectedDelayMs + "ms, Actual=" + actualDelay +
                             "ms, Tolerance=±" + tolerance + "ms, Match=" + matches);

        return matches;
    }

    /**
     * Analyze response content for error indicators that might suggest false positives
     */
    private boolean hasErrorIndicators(IHttpRequestResponse response) {
        if (response == null || response.getResponse() == null) {
            return false;
        }

        try {
            String responseBody = new String(response.getResponse(), "UTF-8").toLowerCase();

            // Common error indicators that might suggest timeouts or server issues
            String[] errorIndicators = {
                "timeout", "time out", "timed out",
                "connection reset", "connection closed",
                "server error", "internal server error",
                "service unavailable", "bad gateway",
                "gateway timeout", "request timeout",
                "too many requests", "rate limit",
                "database error", "sql error",
                "syntax error", "parse error",
                "fatal error", "exception",
                "stack trace", "error 500", "error 502", "error 503", "error 504"
            };

            for (String indicator : errorIndicators) {
                if (responseBody.contains(indicator)) {
                    callbacks.printOutput("Error indicator found in response: " + indicator);
                    return true;
                }
            }

            // Check HTTP status code
            String responseStr = new String(response.getResponse(), "UTF-8");
            if (responseStr.startsWith("HTTP/")) {
                String statusLine = responseStr.split("\r?\n")[0];
                if (statusLine.contains(" 5") || statusLine.contains(" 408") || statusLine.contains(" 429")) {
                    callbacks.printOutput("Error status code detected: " + statusLine);
                    return true;
                }
            }

        } catch (Exception e) {
            callbacks.printError("Error analyzing response content: " + e.getMessage());
        }

        return false;
    }

    /**
     * Advanced statistics container class
     */
    private static class AdvancedStatistics {
        public final double mean;
        public final double median;
        public final double standardDeviation;
        public final double variance;
        public final double skewness;
        public final double kurtosis;
        public final double coefficientOfVariation;
        public final long min;
        public final long max;
        public final long range;

        public AdvancedStatistics(double mean, double median, double standardDeviation, double variance,
                                double skewness, double kurtosis, double coefficientOfVariation,
                                long min, long max, long range) {
            this.mean = mean;
            this.median = median;
            this.standardDeviation = standardDeviation;
            this.variance = variance;
            this.skewness = skewness;
            this.kurtosis = kurtosis;
            this.coefficientOfVariation = coefficientOfVariation;
            this.min = min;
            this.max = max;
            this.range = range;
        }

        @Override
        public String toString() {
            return String.format("Mean=%.2f, Median=%.2f, StdDev=%.2f, CV=%.3f, Skew=%.3f, Kurt=%.3f, Range=%d-%d",
                               mean, median, standardDeviation, coefficientOfVariation, skewness, kurtosis, min, max);
        }
    }

    /**
     * Calculate comprehensive advanced statistics for response times
     */
    private AdvancedStatistics calculateAdvancedStatistics(long baselineTime, List<Long> payloadTimes) {
        if (payloadTimes.isEmpty()) {
            return new AdvancedStatistics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        }

        List<Long> sortedTimes = new ArrayList<>(payloadTimes);
        Collections.sort(sortedTimes);

        // Basic statistics
        double sum = sortedTimes.stream().mapToLong(Long::longValue).sum();
        double mean = sum / sortedTimes.size();
        long min = sortedTimes.get(0);
        long max = sortedTimes.get(sortedTimes.size() - 1);
        long range = max - min;

        // Median
        double median;
        int n = sortedTimes.size();
        if (n % 2 == 0) {
            median = (sortedTimes.get(n/2 - 1) + sortedTimes.get(n/2)) / 2.0;
        } else {
            median = sortedTimes.get(n/2);
        }

        // Variance and standard deviation
        double variance = 0;
        for (long time : payloadTimes) {
            variance += Math.pow(time - mean, 2);
        }
        variance /= payloadTimes.size();
        double standardDeviation = Math.sqrt(variance);

        // Coefficient of variation
        double coefficientOfVariation = mean > 0 ? standardDeviation / mean : 0;

        // Skewness (measure of asymmetry)
        double skewness = 0;
        if (standardDeviation > 0) {
            for (long time : payloadTimes) {
                skewness += Math.pow((time - mean) / standardDeviation, 3);
            }
            skewness /= payloadTimes.size();
        }

        // Kurtosis (measure of tail heaviness)
        double kurtosis = 0;
        if (standardDeviation > 0) {
            for (long time : payloadTimes) {
                kurtosis += Math.pow((time - mean) / standardDeviation, 4);
            }
            kurtosis = (kurtosis / payloadTimes.size()) - 3; // Excess kurtosis
        }

        return new AdvancedStatistics(mean, median, standardDeviation, variance, skewness, kurtosis,
                                    coefficientOfVariation, min, max, range);
    }

    /**
     * Perform Welch's t-test to determine statistical significance
     */
    private double performWelchsTTest(long baselineTime, List<Long> payloadTimes) {
        if (payloadTimes.isEmpty()) {
            return 1.0; // No significance
        }

        // Create baseline sample (single value repeated for comparison)
        List<Double> baselineSample = new ArrayList<>();
        for (int i = 0; i < Math.min(payloadTimes.size(), 10); i++) {
            baselineSample.add((double) baselineTime);
        }

        // Convert payload times to doubles
        List<Double> payloadSample = payloadTimes.stream()
                                                 .mapToDouble(Long::doubleValue)
                                                 .boxed()
                                                 .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        // Calculate means
        double mean1 = baselineSample.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double mean2 = payloadSample.stream().mapToDouble(Double::doubleValue).average().orElse(0);

        // Calculate variances
        double var1 = calculateVariance(baselineSample, mean1);
        double var2 = calculateVariance(payloadSample, mean2);

        int n1 = baselineSample.size();
        int n2 = payloadSample.size();

        // Welch's t-statistic
        double pooledStdError = Math.sqrt((var1 / n1) + (var2 / n2));
        if (pooledStdError == 0) {
            return mean1 == mean2 ? 1.0 : 0.0;
        }

        double tStatistic = (mean2 - mean1) / pooledStdError;

        // Degrees of freedom (Welch-Satterthwaite equation)
        double numerator = Math.pow((var1 / n1) + (var2 / n2), 2);
        double denominator = (Math.pow(var1 / n1, 2) / (n1 - 1)) + (Math.pow(var2 / n2, 2) / (n2 - 1));
        double degreesOfFreedom = numerator / denominator;

        // Approximate p-value using t-distribution (simplified)
        double pValue = approximateTDistributionPValue(Math.abs(tStatistic), degreesOfFreedom);

        callbacks.printOutput("Welch's t-test: t=" + String.format("%.3f", tStatistic) +
                             ", df=" + String.format("%.1f", degreesOfFreedom) +
                             ", p=" + String.format("%.6f", pValue));

        return pValue;
    }

    /**
     * Calculate variance for a sample
     */
    private double calculateVariance(List<Double> sample, double mean) {
        if (sample.size() <= 1) {
            return 0;
        }

        double sumSquaredDiffs = 0;
        for (double value : sample) {
            sumSquaredDiffs += Math.pow(value - mean, 2);
        }

        return sumSquaredDiffs / (sample.size() - 1); // Sample variance
    }

    /**
     * Approximate p-value for t-distribution (simplified implementation)
     */
    private double approximateTDistributionPValue(double tStatistic, double degreesOfFreedom) {
        // Simplified approximation using normal distribution for large df
        if (degreesOfFreedom >= 30) {
            return 2 * (1 - approximateStandardNormalCDF(tStatistic));
        }

        // For smaller df, use a rough approximation
        double adjustedT = tStatistic * Math.sqrt(degreesOfFreedom / (degreesOfFreedom + tStatistic * tStatistic));
        return 2 * (1 - approximateStandardNormalCDF(adjustedT));
    }

    /**
     * Approximate standard normal cumulative distribution function
     */
    private double approximateStandardNormalCDF(double x) {
        // Abramowitz and Stegun approximation
        if (x < 0) {
            return 1 - approximateStandardNormalCDF(-x);
        }

        double t = 1.0 / (1.0 + 0.2316419 * x);
        double y = t * (0.319381530 + t * (-0.356563782 + t * (1.781477937 + t * (-1.821255978 + t * 1.330274429))));

        return 1.0 - (1.0 / Math.sqrt(2 * Math.PI)) * Math.exp(-0.5 * x * x) * y;
    }

    /**
     * Calculate Cohen's d effect size
     */
    private double calculateCohenD(long baselineTime, List<Long> payloadTimes) {
        if (payloadTimes.isEmpty()) {
            return 0;
        }

        double baselineMean = baselineTime;
        double payloadMean = payloadTimes.stream().mapToLong(Long::longValue).average().orElse(0);

        // Calculate pooled standard deviation
        double baselineVariance = 0; // Baseline is a single value, so variance is 0
        double payloadVariance = 0;

        if (payloadTimes.size() > 1) {
            for (long time : payloadTimes) {
                payloadVariance += Math.pow(time - payloadMean, 2);
            }
            payloadVariance /= (payloadTimes.size() - 1);
        }

        // For single baseline value vs multiple payload values, use payload standard deviation
        double pooledStdDev = Math.sqrt(payloadVariance);

        if (pooledStdDev == 0) {
            return payloadMean > baselineMean ? Double.POSITIVE_INFINITY : 0;
        }

        return (payloadMean - baselineMean) / pooledStdDev;
    }

    /**
     * Calculate temporal consistency of response times
     */
    private double calculateTemporalConsistency(List<Long> payloadTimes) {
        if (payloadTimes.size() < 2) {
            return 1.0; // Perfect consistency for single measurement
        }

        // Calculate consecutive differences
        List<Long> differences = new ArrayList<>();
        for (int i = 1; i < payloadTimes.size(); i++) {
            differences.add(Math.abs(payloadTimes.get(i) - payloadTimes.get(i - 1)));
        }

        // Calculate mean and standard deviation of differences
        double meanDiff = differences.stream().mapToLong(Long::longValue).average().orElse(0);
        double meanTime = payloadTimes.stream().mapToLong(Long::longValue).average().orElse(1);

        // Consistency is inversely related to relative variation
        double relativeVariation = meanDiff / meanTime;
        return Math.max(0, 1.0 - relativeVariation);
    }

    /**
     * Calculate network stability metric
     */
    private double calculateNetworkStability(List<Long> payloadTimes) {
        if (payloadTimes.size() < 2) {
            return 0.0; // Perfect stability
        }

        double mean = payloadTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        double variance = 0;

        for (long time : payloadTimes) {
            variance += Math.pow(time - mean, 2);
        }
        variance /= payloadTimes.size();

        double stdDev = Math.sqrt(variance);
        return mean > 0 ? stdDev / mean : 0; // Coefficient of variation
    }

    /**
     * Validate payload-specific behavior
     */
    private boolean validatePayloadSpecificBehavior(long baselineTime, List<Long> payloadTimes) {
        int expectedDelay = extractDelayTime(payload);

        if (expectedDelay <= 0) {
            // Generic validation for unknown payload types
            return validateGenericTimingBehavior(baselineTime, payloadTimes);
        }

        // Validate that observed delay matches expected delay
        double avgPayloadTime = payloadTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long observedDelay = (long) (avgPayloadTime - baselineTime);
        long expectedDelayMs = expectedDelay * 1000L;

        // Allow 30% tolerance for expected delay
        double tolerance = expectedDelayMs * 0.3;
        boolean delayMatches = Math.abs(observedDelay - expectedDelayMs) <= tolerance;

        callbacks.printOutput("Payload Validation: Expected=" + expectedDelayMs + "ms, Observed=" + observedDelay +
                             "ms, Tolerance=±" + String.format("%.0f", tolerance) + "ms, Match=" + delayMatches);

        return delayMatches;
    }

    /**
     * Generic timing behavior validation
     */
    private boolean validateGenericTimingBehavior(long baselineTime, List<Long> payloadTimes) {
        double avgPayloadTime = payloadTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        double delayFactor = avgPayloadTime / baselineTime;
        long absoluteDelay = (long) (avgPayloadTime - baselineTime);

        // Generic validation: significant delay factor and absolute delay
        boolean significantFactor = delayFactor >= MIN_DELAY_FACTOR;
        boolean significantAbsolute = absoluteDelay >= ABSOLUTE_MIN_DELAY;

        callbacks.printOutput("Generic Validation: Factor=" + String.format("%.2f", delayFactor) +
                             " (min=" + MIN_DELAY_FACTOR + "), Absolute=" + absoluteDelay +
                             "ms (min=" + ABSOLUTE_MIN_DELAY + "ms)");

        return significantFactor && significantAbsolute;
    }

    /**
     * Perform additional confirmation tests for positive results
     */
    private boolean performConfirmationTests(long baselineTime, IHttpService service, byte[] baseRequest) {
        callbacks.printOutput("Performing " + CONFIRMATION_TESTS + " confirmation tests...");

        List<Long> confirmationTimes = new ArrayList<>();

        for (int i = 0; i < CONFIRMATION_TESTS; i++) {
            try {
                // Apply rate limiting bypass delay before confirmation request
                if (rateLimitBypass != null && rateLimitBypass.isEnabled()) {
                    rateLimitBypass.applyDelay();
                }

                byte[] modifiedRequest = injector.injectPayload(baseRequest, parameter, payload);

                // Apply header randomization to confirmation request
                modifiedRequest = applyHeaderRandomization(modifiedRequest);

                long startTime = System.currentTimeMillis();
                IHttpRequestResponse response = callbacks.makeHttpRequest(service, modifiedRequest);
                long endTime = System.currentTimeMillis();
                long responseTime = endTime - startTime;

                if (response != null && response.getResponse() != null) {
                    confirmationTimes.add(responseTime);
                    callbacks.printOutput("Confirmation test #" + (i + 1) + ": " + responseTime + "ms");

                    // Check for error indicators
                    if (hasErrorIndicators(response)) {
                        callbacks.printOutput("Confirmation test #" + (i + 1) + " shows error indicators - potential false positive");
                        return false;
                    }
                } else {
                    callbacks.printOutput("Confirmation test #" + (i + 1) + " failed (no response)");
                    return false;
                }

                // Delay between confirmation tests
                Thread.sleep(300);

            } catch (Exception e) {
                callbacks.printError("Error in confirmation test #" + (i + 1) + ": " + e.getMessage());
                return false;
            }
        }

        if (confirmationTimes.isEmpty()) {
            callbacks.printOutput("Confirmation failed: No valid responses");
            return false;
        }

        // Validate confirmation results
        double avgConfirmationTime = confirmationTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        double delayFactor = avgConfirmationTime / baselineTime;
        long absoluteDelay = (long) (avgConfirmationTime - baselineTime);

        boolean confirmationPassed = delayFactor >= MIN_DELAY_FACTOR && absoluteDelay >= ABSOLUTE_MIN_DELAY;

        callbacks.printOutput("Confirmation Results: Avg=" + String.format("%.0f", avgConfirmationTime) +
                             "ms, Factor=" + String.format("%.2f", delayFactor) +
                             ", Delay=" + absoluteDelay + "ms, Passed=" + confirmationPassed);

        // Additional validation: consistency between original and confirmation tests
        int expectedDelay = extractDelayTime(payload);
        if (expectedDelay > 0) {
            long expectedDelayMs = expectedDelay * 1000L;
            double tolerance = expectedDelayMs * 0.4; // 40% tolerance for confirmation
            boolean delayConsistent = Math.abs(absoluteDelay - expectedDelayMs) <= tolerance;

            callbacks.printOutput("Delay Consistency: Expected=" + expectedDelayMs + "ms, Actual=" + absoluteDelay +
                                 "ms, Tolerance=±" + String.format("%.0f", tolerance) + "ms, Consistent=" + delayConsistent);

            return confirmationPassed && delayConsistent;
        }

        return confirmationPassed;
    }

    /**
     * Track a request in the history panel if available
     */
    private void trackRequest(IHttpRequestResponse requestResponse, HttpParameter parameter,
                             String payload, long responseTime, String testType) {
        if (mainPanel != null && requestResponse != null) {
            try {
                // Use reflection to call the addRequestEntry method on the history panel
                java.lang.reflect.Method getHistoryPanelMethod = mainPanel.getClass().getMethod("getHistoryPanel");
                Object historyPanel = getHistoryPanelMethod.invoke(mainPanel);

                if (historyPanel != null) {
                    java.lang.reflect.Method addRequestEntryMethod = historyPanel.getClass().getMethod(
                        "addRequestEntry",
                        IHttpRequestResponse.class,
                        HttpParameter.class,
                        String.class,
                        long.class,
                        String.class
                    );
                    addRequestEntryMethod.invoke(historyPanel, requestResponse, parameter, payload, responseTime, testType);
                }
            } catch (Exception e) {
                // Silently ignore reflection errors - history tracking is optional
                // callbacks.printError("Error tracking request: " + e.getMessage());
            }
        }
    }

} // End of TimeBasedTest class

