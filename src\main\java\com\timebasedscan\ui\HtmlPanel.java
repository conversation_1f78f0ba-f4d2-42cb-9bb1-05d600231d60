package com.timebasedscan.ui;

import javax.swing.*;
import java.awt.*;

/**
 * An enhanced panel for displaying HTML content with proper rendering
 */
public class HtmlPanel extends JPanel {
    
    private final JEditorPane htmlPane;
    
    /**
     * Create a new HTML panel with the given initial HTML content
     */
    public HtmlPanel(String initialHtml) {
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createEmptyBorder());
        
        // Create the HTML display component
        htmlPane = new JEditorPane();
        htmlPane.setContentType("text/html");
        htmlPane.setEditable(false);
        htmlPane.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, Boolean.TRUE);
        
        // Use default system font and size for consistent look
        Font defaultFont = UIManager.getFont("Label.font");
        if (defaultFont != null) {
            htmlPane.setFont(defaultFont);
        }
        
        // Set initial content
        if (initialHtml != null && !initialHtml.isEmpty()) {
            setHtml(initialHtml);
        } else {
            setHtml("<html><body><div style='padding: 10px;'>No content available</div></body></html>");
        }
        
        // Add scrolling support
        JScrollPane scrollPane = new JScrollPane(htmlPane);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        
        add(scrollPane, BorderLayout.CENTER);
    }
    
    /**
     * Set the HTML content of this panel
     */
    public void setHtml(String html) {
        String content = html;
        
        // Ensure content has proper HTML wrapper
        if (!content.trim().startsWith("<html>")) {
            content = "<html><body>" + content + "</body></html>";
        }
        
        // Set the content and scroll to top
        htmlPane.setText(content);
        htmlPane.setCaretPosition(0);
    }
}