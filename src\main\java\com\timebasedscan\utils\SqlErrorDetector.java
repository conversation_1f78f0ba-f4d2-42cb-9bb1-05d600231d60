package com.timebasedscan.utils;

import burp.IBurpExtenderCallbacks;
import burp.IExtensionHelpers;
import burp.IHttpRequestResponse;
import burp.IHttpService;
import burp.IRequestInfo;
import com.timebasedscan.model.HttpParameter;
import com.timebasedscan.model.TestResult;
import com.timebasedscan.utils.RateLimitBypass;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * Utility class for detecting SQL error messages in HTTP responses
 * Enhanced for SQL Error Detection Panel integration
 */
public class SqlErrorDetector {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PayloadInjector injector;
    private RateLimitBypass rateLimitBypass;
    
    // SQL error patterns for different database systems
    private static final List<SqlErrorPattern> ERROR_PATTERNS = new ArrayList<>();
    
    static {
        initializeErrorPatterns();
    }

    /**
     * Constructor for SQL Error Detector
     */
    public SqlErrorDetector(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.injector = new PayloadInjector(helpers, callbacks);
        this.rateLimitBypass = new RateLimitBypass(callbacks, helpers);

        // Configure advanced payload injection for SQL error detection (disabled by default)
        this.injector.configureAdvancedInjection(
            false,  // Disable encoding bypass by default
            false,  // Disable WAF evasion by default
            false,  // Disable context-aware injection by default
            false   // Disable multiple encoding layers by default
        );
    }

    /**
     * Configure rate limiting bypass settings
     */
    public void configureRateLimitBypass(boolean enabled, String bypassMethod, int baseDelay,
                                       boolean randomizeUserAgents, boolean randomizeXForwardedFor,
                                       boolean useProxyRotation) {
        if (rateLimitBypass != null) {
            rateLimitBypass.configure(enabled, bypassMethod, baseDelay,
                                    randomizeUserAgents, randomizeXForwardedFor, useProxyRotation);
        }
    }

    /**
     * Configure custom header injection settings
     */
    public void configureCustomHeaderInjection(boolean enabled, String headerName, String headerValue) {
        if (injector != null) {
            injector.configureCustomHeaderInjection(enabled, headerName, headerValue);
        }
    }

    /**
     * Apply header randomization to the HTTP request if rate limiting bypass is enabled
     */
    private byte[] applyHeaderRandomization(byte[] request) {
        if (rateLimitBypass == null || !rateLimitBypass.isEnabled()) {
            return request;
        }

        try {
            // Parse the request to get headers
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = new ArrayList<>(requestInfo.getHeaders());
            boolean headersModified = false;

            // Randomize User-Agent if enabled
            String randomUserAgent = rateLimitBypass.getRandomUserAgent();
            if (randomUserAgent != null) {
                // Remove existing User-Agent header
                headers.removeIf(header -> header.toLowerCase().startsWith("user-agent:"));
                // Add randomized User-Agent
                headers.add("User-Agent: " + randomUserAgent);
                headersModified = true;
                callbacks.printOutput("SQL Error Detection - Rate Limiting Bypass: Applied User-Agent: " + randomUserAgent);
            }

            // Randomize X-Forwarded-For if enabled
            String randomXForwardedFor = rateLimitBypass.getRandomXForwardedFor();
            if (randomXForwardedFor != null) {
                // Remove existing X-Forwarded-For header
                headers.removeIf(header -> header.toLowerCase().startsWith("x-forwarded-for:"));
                // Add randomized X-Forwarded-For
                headers.add("X-Forwarded-For: " + randomXForwardedFor);
                headersModified = true;
                callbacks.printOutput("SQL Error Detection - Rate Limiting Bypass: Applied X-Forwarded-For: " + randomXForwardedFor);
            }

            // If headers were modified, rebuild the request
            if (headersModified) {
                byte[] body = null;
                int bodyOffset = requestInfo.getBodyOffset();
                if (bodyOffset < request.length) {
                    body = new byte[request.length - bodyOffset];
                    System.arraycopy(request, bodyOffset, body, 0, body.length);
                }

                return helpers.buildHttpMessage(headers, body);
            }

        } catch (Exception e) {
            callbacks.printError("Error applying header randomization in SQL error detection: " + e.getMessage());
        }

        return request;
    }

    /**
     * Test a parameter for SQL injection errors using the provided payload
     */
    public TestResult testForSQLError(IHttpRequestResponse request, HttpParameter parameter, String payload) {
        try {
            // Apply rate limiting bypass delay before request
            if (rateLimitBypass != null && rateLimitBypass.isEnabled()) {
                rateLimitBypass.applyDelay();
            }

            // Inject payload into the parameter
            byte[] modifiedRequest = injector.injectPayload(request.getRequest(), parameter, payload);

            // Apply header randomization if enabled
            modifiedRequest = applyHeaderRandomization(modifiedRequest);

            // Send the request and measure response time
            IHttpService service = request.getHttpService();
            long startTime = System.currentTimeMillis();
            IHttpRequestResponse response = callbacks.makeHttpRequest(service, modifiedRequest);
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;

            // Analyze response for SQL errors
            if (response.getResponse() != null) {
                SqlErrorResult errorResult = detectSqlError(response.getResponse());

                if (errorResult.isErrorDetected()) {
                    // Create test result for SQL error
                    TestResult result = new TestResult();
                    result.setParameterName(parameter.getName());
                    result.setParameterType(parameter.getType().toString());
                    result.setPayload(payload);
                    result.setResponseTime(responseTime);
                    result.setVulnerable(true);
                    result.setDatabaseType(errorResult.getDatabaseType());
                    result.setErrorType(determineErrorType(errorResult.getErrorMessage()));
                    result.setErrorMessage(errorResult.getErrorSnippet());
                    result.setRequest(modifiedRequest);
                    result.setResponse(response.getResponse());

                    callbacks.printOutput("SQL Error detected in parameter '" + parameter.getName() +
                                        "' - Database: " + errorResult.getDatabaseType() +
                                        ", Error: " + errorResult.getErrorMessage().substring(0, Math.min(100, errorResult.getErrorMessage().length())));

                    return result;
                }
            }

            return null; // No SQL error detected

        } catch (Exception e) {
            callbacks.printError("Error testing parameter for SQL injection: " + e.getMessage());
            return null;
        }
    }

    /**
     * Determine the type of SQL error based on the error message
     */
    private String determineErrorType(String errorMessage) {
        if (errorMessage == null) return "Unknown Error";

        String lowerMessage = errorMessage.toLowerCase();

        if (lowerMessage.contains("syntax")) {
            return "Syntax Error";
        } else if (lowerMessage.contains("column")) {
            return "Column Error";
        } else if (lowerMessage.contains("table")) {
            return "Table Error";
        } else if (lowerMessage.contains("conversion") || lowerMessage.contains("convert")) {
            return "Type Conversion Error";
        } else if (lowerMessage.contains("constraint")) {
            return "Constraint Violation";
        } else if (lowerMessage.contains("permission") || lowerMessage.contains("access")) {
            return "Permission Error";
        } else if (lowerMessage.contains("connection")) {
            return "Connection Error";
        } else if (lowerMessage.contains("timeout")) {
            return "Timeout Error";
        } else {
            return "Database Error";
        }
    }
    
    /**
     * Initialize SQL error patterns for various database systems
     */
    private static void initializeErrorPatterns() {
        // MySQL Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("You have an error in your SQL syntax", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("mysql_fetch_array\\(\\)", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("mysql_query\\(\\)", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("Warning.*mysql_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("valid MySQL result", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("MySqlClient\\.", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("Unknown column.*in.*field list", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MySQL", 
            Pattern.compile("Table.*doesn't exist", Pattern.CASE_INSENSITIVE)));
        
        // PostgreSQL Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("PostgreSQL.*ERROR", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("Warning.*\\Wpg_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("valid PostgreSQL result", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("Npgsql\\.", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("PG::Error", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("syntax error at or near", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("PostgreSQL", 
            Pattern.compile("column.*does not exist", Pattern.CASE_INSENSITIVE)));
        
        // Microsoft SQL Server Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("Microsoft.*ODBC.*SQL Server.*Driver", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("\\[SQL Server\\]", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("\\[Microsoft\\]\\[ODBC SQL Server Driver\\]", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("\\[SQLServer JDBC Driver\\]", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("SqlException", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("System\\.Data\\.SqlClient\\.SqlException", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("Unclosed quotation mark after the character string", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("'80040e14'", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("MSSQL", 
            Pattern.compile("Incorrect syntax near", Pattern.CASE_INSENSITIVE)));
        
        // Oracle Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("\\bORA-[0-9][0-9][0-9][0-9][0-9]", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("Oracle error", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("Oracle.*Driver", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("Warning.*\\Woci_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("Warning.*\\Wora_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("oracle\\.jdbc\\.driver", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Oracle", 
            Pattern.compile("quoted string not properly terminated", Pattern.CASE_INSENSITIVE)));
        
        // SQLite Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("SQLite", 
            Pattern.compile("SQLite/JDBCDriver", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("SQLite", 
            Pattern.compile("SQLite\\.Exception", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("SQLite", 
            Pattern.compile("System\\.Data\\.SQLite\\.SQLiteException", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("SQLite", 
            Pattern.compile("Warning.*sqlite_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("SQLite", 
            Pattern.compile("unrecognized token", Pattern.CASE_INSENSITIVE)));
        
        // Generic SQL Error Patterns
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("SQL syntax.*MySQL", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("Warning.*mysql_.*", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("MySqlException", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("invalid query", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("SQL Error", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("database error", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("syntax error", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("unexpected end of SQL command", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("unterminated quoted string", Pattern.CASE_INSENSITIVE)));
        ERROR_PATTERNS.add(new SqlErrorPattern("Generic", 
            Pattern.compile("SQL command not properly ended", Pattern.CASE_INSENSITIVE)));
    }
    
    /**
     * Detect SQL errors in HTTP response
     */
    public static SqlErrorResult detectSqlError(byte[] response) {
        if (response == null || response.length == 0) {
            return new SqlErrorResult(false, null, null, null);
        }
        
        String responseString = new String(response);
        
        for (SqlErrorPattern pattern : ERROR_PATTERNS) {
            Matcher matcher = pattern.getPattern().matcher(responseString);
            if (matcher.find()) {
                String errorSnippet = extractErrorSnippet(responseString, matcher.start(), matcher.end());
                return new SqlErrorResult(true, pattern.getDatabaseType(), matcher.group(), errorSnippet);
            }
        }
        
        return new SqlErrorResult(false, null, null, null);
    }
    
    /**
     * Extract error snippet with context
     */
    private static String extractErrorSnippet(String response, int start, int end) {
        int snippetStart = Math.max(0, start - 50);
        int snippetEnd = Math.min(response.length(), end + 50);
        
        String snippet = response.substring(snippetStart, snippetEnd);
        
        // Clean up the snippet
        snippet = snippet.replaceAll("\\s+", " ").trim();
        
        if (snippet.length() > 200) {
            snippet = snippet.substring(0, 200) + "...";
        }
        
        return snippet;
    }
    
    /**
     * SQL Error Pattern class
     */
    private static class SqlErrorPattern {
        private final String databaseType;
        private final Pattern pattern;
        
        public SqlErrorPattern(String databaseType, Pattern pattern) {
            this.databaseType = databaseType;
            this.pattern = pattern;
        }
        
        public String getDatabaseType() {
            return databaseType;
        }
        
        public Pattern getPattern() {
            return pattern;
        }
    }
    
    /**
     * SQL Error Result class
     */
    public static class SqlErrorResult {
        private final boolean errorDetected;
        private final String databaseType;
        private final String errorMessage;
        private final String errorSnippet;
        
        public SqlErrorResult(boolean errorDetected, String databaseType, String errorMessage, String errorSnippet) {
            this.errorDetected = errorDetected;
            this.databaseType = databaseType;
            this.errorMessage = errorMessage;
            this.errorSnippet = errorSnippet;
        }
        
        public boolean isErrorDetected() {
            return errorDetected;
        }
        
        public String getDatabaseType() {
            return databaseType;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public String getErrorSnippet() {
            return errorSnippet;
        }
        
        @Override
        public String toString() {
            if (!errorDetected) {
                return "No SQL error detected";
            }
            return String.format("SQL Error [%s]: %s", databaseType, errorMessage);
        }
    }
}
